# IoT大数据平台架构方案

## 项目背景与目标

### 业务需求
目前公司的IoT数据处理依赖博锐的SagaCare，但SagaCare即将停服。构建自主可控的IoT大数据平台迫在眉睫，实现多个项目设备数据的统一采集、处理、展示和远程控制，逐步替代对SagaCare等第三方平台的依赖。

- 第一阶段：建立自有IoT数据平台基础架构，实现核心功能
- 第二阶段：逐步将现有项目的数据处理迁移到自有平台，完善指令下发功能
- 第三阶段：完全替代SagaCare平台，实现全设备IoT数据的自主管理和智能控制

## 方案对比与选择

### 技术方案对比

| 评估维度 | 基础分层架构 | 消息队列架构 | 云原生架构 |
|---------|-------------|-------------|-----------|
| **技术栈** | Spring Boot + MySQL + Redis | Spring Cloud + Kafka + MySQL + Flink | K8s + Hbase + Flink + Spark |
| **技术复杂度** | 简单 | 中等 | 复杂 |
| **开发周期** | 1-3个月 | 4-6个月 | 6-12个月 |
| **运维成本** | 低 | 中等 | 高 |
| **并发处理能力** | 1万TPS | 10万TPS | 100万TPS |
| **数据容量支持** | TB级别 | PB级别 | EB级别 |
| **技术风险** | 低 | 中等 | 高 |
| **投入产出比** | 最优 | 良好 | 一般 |

### 最终方案选择：基础分层架构

**选择理由**：
- 成本效益最优：开发成本低，见效快，ROI高
- 技术风险可控：基于成熟技术栈，团队学习成本低
- 满足当前需求：覆盖现有业务场景，支持双向通信和远程控制

## 总体架构设计

### 1. 系统架构图

```
IoT设备端                   边缘程序                    大数据平台(服务端)
┌─────────┐               ┌─────────────┐              ┌──────────────┐
│星纵设备  │               │             │              │              │
│涂鸦设备  │◀─────────────▶│  边缘程序    │◀────────────▶│ 数据接收接口  │
│海康设备  │               │  jar包      │ MQTT/WebSocket│ AKSK认证     │
│其他设备  │               │             │              │              │
└─────────┘               └─────────────┘              └──────────────┘
     │                          │                              │
     │ 原始数据                  │ 标准化数据                    │ 验证后数据
     │ 指令执行                  │ HTTP/JSON                    │ 指令下发
     ▼                          ▼                              ▼
┌─────────┐               ┌─────────────┐              ┌──────────────┐
│传感器数据│               │厂商适配器    │              │数据验证模块   │
│设备信息  │               │数据采集转换  │              │批量处理      │
│状态信息  │               │指令解析转换  │              │异常过滤      │
│控制指令  │               │断网缓存     │              │指令管理      │
│配置更新  │               │远程配置     │              │配置推送      │
└─────────┘               └─────────────┘              └──────────────┘
                               ▲                              │
                               │ MQTT over WebSocket          ▼
                               │ 指令下发&配置推送    ┌──────────────┐
                               └─────────────────────│   消息代理   │
                                                     │ MQTT Broker │
                                                     └──────────────┘
                                                             │
                              ┌─────────────────────────────────────┐
                              │              存储层                 │
                              │  ┌─────────────┐    ┌─────────────┐ │
                              │  │   MySQL     │    │    Redis    │ │
                              │  │   按月分表   │    │   热点缓存   │ │
                              │  │   设备分组   │    │   指令队列   │ │
                              │  └─────────────┘    └─────────────┘ │
                              └─────────────────────────────────────┘
                                              │
                              ┌─────────────────────────────────────┐
                              │              计算层                 │
                              │  ┌─────────────┐    ┌─────────────┐ │
                              │  │   任务调度   │    │   计算引擎   │ │
                              │  │   定时触发   │    │   分精度统计  │ │
                              │  └─────────────┘    └─────────────┘ │
                              └─────────────────────────────────────┘
                                              │
                              ┌─────────────────────────────────────┐
                              │              应用层                 │
                              │  ┌─────────────┐    ┌─────────────┐ │
                              │  │  管理后台    │    │   数据大屏   │ │
                              │  │  设备管理    │    │   实时监控   │ │
                              │  │  远程控制    │    │   报表系统   │ │
                              │  └─────────────┘    └─────────────┘ │
                              └─────────────────────────────────────┘
```

**核心流程说明**：
1. **设备数据采集**：多厂商IoT设备通过各自协议上报数据
2. **数据标准化**：边缘程序通过厂商适配器将数据转换为统一格式
3. **双向通信**：边缘程序与大数据平台建立MQTT over WebSocket连接，支持数据上报和指令下发
4. **数据传输**：采用HTTP/JSON格式批量上报到服务端
5. **指令下发**：大数据平台通过MQTT over WebSocket向边缘程序下发控制指令
6. **数据入库**：经过验证的数据存储到MySQL按月分表，热点数据缓存到Redis
7. **远程配置**：支持边缘程序的远程参数配置和规则更新，实时推送生效
8. **指令管理**：指令队列管理、状态追踪和执行反馈
9. **数据计算**：定时任务触发计算引擎，进行分精度统计分析
10. **数据应用**：通过API接口为管理后台和数据大屏提供数据服务，支持远程控制操作

### 2. 关键技术挑战与解决方案

| 技术挑战 | 影响范围 | 解决方案 | 预期效果 |
|---------|---------|----------|----------|
| **高并发数据写入** | 系统性能 | 批量处理 + 异步写入 + 连接池优化 | 支持万级TPS |
| **海量数据存储** | 存储成本 | 分库分表 + 数据分层 + 定期归档 | 成本降低60% |
| **数据实时性** | 用户体验 | Redis缓存 + 增量计算 + 内存优化 | 响应时间<100ms |
| **系统可用性** | 业务连续性 | 集群部署 + 故障转移 + 健康检查 | 可用性99.9% |
| **数据质量** | 分析准确性 | 多层验证 + 异常检测 + 数据清洗 | 数据准确率>95% |
| **指令下发可靠性** | 设备控制 | MQTT QoS + 指令队列 + 状态追踪 | 指令到达率>99% |
| **边缘程序稳定性** | 数据采集 | 断网缓存 + 自动重连 + 远程监控 | 数据丢失率<0.1% |

## 项目结构与技术选型

### 项目组织结构
本项目采用单体架构设计，所有模块统一管理在一个代码仓库中，便于开发、测试和部署。

**项目模块划分**：
- `iot-edge`：边缘程序模块，独立jar包，支持数据采集和远程配置
- `iot-api`：API接口模块，包含数据接收服务、设备管理和对外API
- `iot-compute`：计算引擎模块，定时任务和算法实现
- `iot-common`：公共模块，包含工具类、实体类、数据访问层和配置

### 技术选型与版本
- **JDK版本**：OpenJDK 17 LTS（长期支持版本，稳定性最佳）
- **Spring Boot**：3.2.x（最新稳定版本）
- **构建工具**：Maven 3.9.x（企业级标准构建工具）
- **数据库**：MySQL 8.0（支持JSON数据类型和更好的性能）
- **缓存**：Redis 7.0（最新稳定版本）
- **消息代理**：MQTT over WebSocket（IoT专用协议，支持QoS和双向通信）
- **版本控制**：Git（统一代码仓库管理）

### 构建与部署
- **容器化**：Docker + Docker Compose
- **CI/CD**：Jenkins Pipeline

## 数据库表结构设计

### 核心数据表

#### 1. 设备分组表
```sql
CREATE TABLE device_group (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '分组唯一ID',
    project_id VARCHAR(50) NOT NULL COMMENT '项目ID',
    name VARCHAR(200) NOT NULL COMMENT '分组名称',
    parent_id BIGINT COMMENT '父分组ID，NULL表示根分组',
    group_type VARCHAR(50) DEFAULT 'building' COMMENT '分组类型：building-建筑，floor-楼层，room-房间，area-区域',
    level INTEGER DEFAULT 1 COMMENT '分组级别：1-根级，2-二级，3-三级等',
    sort_order INTEGER DEFAULT 0 COMMENT '排序字段',
    group_path VARCHAR(500) COMMENT '分组路径，如：/building1/floor2/room301',
    description TEXT COMMENT '分组描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_project_id (project_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_project_type (project_id, group_type),
    INDEX idx_level (level)
) ENGINE=InnoDB COMMENT='设备分组表';
```

#### 2. 设备信息表
```sql
CREATE TABLE device (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设备唯一ID',
    project_id VARCHAR(50) NOT NULL COMMENT '项目ID',
    device_code VARCHAR(100) NOT NULL COMMENT '设备唯一识别码',
    device_name VARCHAR(200) COMMENT '设备名称',
    device_type TINYINT NOT NULL COMMENT '设备类型：1-传感器,2-摄像头,3-网关,4-控制器',
    vendor TINYINT NOT NULL COMMENT '厂商：1-星纵,2-涂鸦,3-海康威视,4-大华,5-宇视',
    model VARCHAR(100) COMMENT '设备型号',
    firmware_version VARCHAR(50) COMMENT '固件版本',
    mac_address VARCHAR(32) COMMENT 'MAC地址',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    location VARCHAR(200) COMMENT '设备位置',
    status TINYINT DEFAULT 1 COMMENT '设备状态：1-正常,2-离线,3-故障',
    last_report_time BIGINT COMMENT '最后上报时间戳',
    edge_program_id VARCHAR(100) COMMENT '关联的边缘程序ID',
    description TEXT COMMENT '设备描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_device_code (device_code),
    INDEX idx_project_type (project_id, device_type),
    INDEX idx_vendor (vendor),
    INDEX idx_status (status),
    INDEX idx_edge_program (edge_program_id),
    INDEX idx_last_report_time (last_report_time)
) ENGINE=InnoDB COMMENT='设备信息表';
```

#### 3. 设备分组关联表
```sql
CREATE TABLE device_group_relation (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    group_id BIGINT NOT NULL COMMENT '分组ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_device_group (device_id, group_id),
    INDEX idx_device_id (device_id),
    INDEX idx_group_id (group_id)
) ENGINE=InnoDB COMMENT='设备分组关联表';
```

#### 4. 边缘程序配置表
```sql
CREATE TABLE edge_program (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '边缘程序ID',
    program_id VARCHAR(100) NOT NULL UNIQUE COMMENT '边缘程序唯一标识',
    program_name VARCHAR(200) NOT NULL COMMENT '边缘程序名称',
    project_id VARCHAR(50) NOT NULL COMMENT '项目ID',
    version VARCHAR(50) COMMENT '程序版本',
    ip_address VARCHAR(45) COMMENT '程序所在IP地址',
    port INTEGER COMMENT '程序端口号',
    config_json JSON COMMENT '程序配置（JSON格式）',
    collect_interval INTEGER DEFAULT 60 COMMENT '采集间隔（秒）',
    cache_max_size INTEGER DEFAULT 10000 COMMENT '断网缓存最大条数',
    retry_times INTEGER DEFAULT 3 COMMENT '重试次数',
    heartbeat_interval INTEGER DEFAULT 60 COMMENT '心跳间隔（秒）',
    status TINYINT DEFAULT 1 COMMENT '状态：1-在线,2-离线,3-异常',
    last_heartbeat BIGINT COMMENT '最后心跳时间戳',
    config_version VARCHAR(50) COMMENT '配置版本号',
    description TEXT COMMENT '边缘程序描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_project_id (project_id),
    INDEX idx_status (status),
    INDEX idx_last_heartbeat (last_heartbeat)
) ENGINE=InnoDB COMMENT='边缘程序配置表';
```

#### 5. IoT原始数据表（按月分表）
```sql
CREATE TABLE iot_data_202412 (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '数据ID',
    project_id VARCHAR(50) NOT NULL COMMENT '项目ID',
    device_id BIGINT NOT NULL COMMENT '设备ID',
    device_code VARCHAR(100) NOT NULL COMMENT '设备唯一识别码（冗余字段，便于查询）',
    edge_program_id BIGINT COMMENT '边缘程序ID',
    data_code TINYINT NOT NULL COMMENT '数据类型码：1-温度,2-湿度,3-PM2.5,4-二氧化碳,5-人体感应,6-光照强度,7-占用状态,8-噪音分贝',
    data_value DECIMAL(20,6) NOT NULL COMMENT '数据值',
    quality TINYINT DEFAULT 1 COMMENT '数据质量等级：1-优秀,2-良好,3-一般,4-较差',
    report_time BIGINT NOT NULL COMMENT '设备上报时间戳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '服务器接收时间',
    
    INDEX idx_device_time (device_id, report_time),
    INDEX idx_device_code_time (device_code, report_time),
    INDEX idx_project_time (project_id, report_time),
    INDEX idx_data_time (data_code, report_time),
    INDEX idx_edge_program_time (edge_program_id, report_time)
) ENGINE=InnoDB COMMENT='IoT设备原始数据表_202412';
```

#### 6. 计算任务表
```sql
CREATE TABLE task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    project_id VARCHAR(50) NOT NULL COMMENT '项目ID',
    task_name VARCHAR(200) NOT NULL COMMENT '任务名称',
    device_code VARCHAR(100) COMMENT '设备唯一识别码，NULL表示所有设备',
    group_id BIGINT COMMENT '设备分组ID，NULL表示所有分组',
    data_code TINYINT COMMENT '数据类型码，NULL表示所有数据类型',
    calc_type TINYINT NOT NULL COMMENT '计算类型：1-平均值,2-最大值,3-最小值,4-计数,5-求和,6-占用率',
    time_window TINYINT NOT NULL COMMENT '时间窗口：1-5分钟,2-15分钟,3-1小时,4-1天',
    description TEXT COMMENT '任务描述',
    last_execute_time BIGINT COMMENT '最后执行时间戳',
    next_execute_time BIGINT COMMENT '下次执行时间戳',
    execute_count BIGINT DEFAULT 0 COMMENT '执行次数',
    status TINYINT DEFAULT 1 COMMENT '任务状态：1-启用,0-禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_project_status (project_id, status),
    INDEX idx_calc_window (calc_type, time_window),
    INDEX idx_device_data (device_code, data_code),
    INDEX idx_group_id (group_id),
    INDEX idx_next_execute_time (next_execute_time)
) ENGINE=InnoDB COMMENT='计算任务配置表';
```

#### 7. 计算结果表
```sql
CREATE TABLE task_result (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '结果ID',
    task_id BIGINT NOT NULL COMMENT '计算任务ID',
    project_id VARCHAR(50) NOT NULL COMMENT '项目ID',
    device_code VARCHAR(100) COMMENT '设备唯一识别码',
    group_id BIGINT COMMENT '设备分组ID',
    data_code TINYINT COMMENT '数据类型码',
    calc_type TINYINT NOT NULL COMMENT '计算类型：1-平均值,2-最大值,3-最小值,4-计数,5-求和,6-占用率',
    time_window TINYINT NOT NULL COMMENT '时间窗口：1-5分钟,2-15分钟,3-1小时,4-1天',
    result_value DECIMAL(20,6) COMMENT '计算结果',
    data_count INTEGER DEFAULT 0 COMMENT '参与计算的数据量',
    window_start BIGINT NOT NULL COMMENT '窗口开始时间戳',
    window_end BIGINT NOT NULL COMMENT '窗口结束时间戳',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
    
    UNIQUE KEY uk_task_window (task_id, window_start),
    INDEX idx_project_time (project_id, window_start),
    INDEX idx_calc_type_time (calc_type, window_start),
    INDEX idx_device_time (device_code, window_start),
    INDEX idx_group_time (group_id, window_start)
) ENGINE=InnoDB COMMENT='计算结果表';
```

#### 8. API认证表
```sql
CREATE TABLE api_auth (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '认证ID',
    project_id VARCHAR(50) COMMENT '关联项目ID，NULL表示所有项目',
    app_name VARCHAR(100) NOT NULL COMMENT '应用名称',
    access_key VARCHAR(32) NOT NULL UNIQUE COMMENT 'Access Key',
    secret_key VARCHAR(64) NOT NULL COMMENT 'Secret Key（加密存储）',
    permissions JSON COMMENT '权限配置（JSON格式）',
    status TINYINT DEFAULT 1 COMMENT '状态：0-禁用,1-启用',
    last_used_time BIGINT COMMENT '最后使用时间戳',
    usage_count BIGINT DEFAULT 0 COMMENT '使用次数',
    expires_at TIMESTAMP NULL COMMENT '过期时间，NULL表示永不过期',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_access_key (access_key),
    INDEX idx_project_status (project_id, status),
    INDEX idx_app_name (app_name)
) ENGINE=InnoDB COMMENT='API认证表';
```

#### 9. 系统配置表
```sql
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '配置ID',
    project_id VARCHAR(50) COMMENT '项目ID，NULL表示全局配置',
    category VARCHAR(50) NOT NULL COMMENT '配置分类：system-系统配置,cache-缓存配置,database-数据库配置',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description TEXT COMMENT '配置描述',
    editable TINYINT DEFAULT 1 COMMENT '是否可编辑：1-可编辑,0-只读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_project_category_key (project_id, category, config_key),
    INDEX idx_category (category),
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB COMMENT='系统配置表';
```

#### 10. 系统日志表
```sql
CREATE TABLE system_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    project_id VARCHAR(50) COMMENT '项目ID',
    log_level VARCHAR(10) NOT NULL COMMENT '日志级别：INFO,WARN,ERROR,DEBUG',
    log_type VARCHAR(50) NOT NULL COMMENT '日志类型：DEVICE_STATUS,SYSTEM_ERROR,USER_ACTION',
    module VARCHAR(50) NOT NULL COMMENT '操作模块',
    operation VARCHAR(100) NOT NULL COMMENT '操作描述',
    details JSON COMMENT '详细信息（JSON格式）',
    object_id VARCHAR(100) COMMENT '相关对象ID（设备ID、任务ID等）',
    object_type VARCHAR(50) COMMENT '相关对象类型',
    user_id VARCHAR(100) COMMENT '操作用户ID',
    client_ip VARCHAR(45) COMMENT '客户端IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_project_time (project_id, created_at),
    INDEX idx_log_level_time (log_level, created_at),
    INDEX idx_log_type_time (log_type, created_at),
    INDEX idx_object (object_type, object_id)
) ENGINE=InnoDB COMMENT='系统日志表';
```

### 相关枚举类设计

```java
/**
 * 设备类型枚举（数据库存储数字编码）
 */
public enum DeviceType {
    SENSOR(1, "传感器", "各类环境传感器设备"),
    CAMERA(2, "摄像头", "监控摄像头设备"),
    GATEWAY(3, "网关", "数据采集网关设备"),
    CONTROLLER(4, "控制器", "设备控制器");
    
    private final Integer code;
    private final String description;
    private final String detail;
}

/**
 * 设备厂商枚举（数据库存储数字编码）
 */
public enum DeviceVendor {
    YEASTAR(1, "星纵", "YEASTAR", "星纵物联网设备"),
    TUYA(2, "涂鸦", "TUYA", "涂鸦智能设备"),
    HIKVISION(3, "海康威视", "HIKVISION", "海康威视监控设备"),
    DAHUA(4, "大华", "DAHUA", "大华监控设备"),
    UNIVIEW(5, "宇视", "UNIVIEW", "宇视监控设备");
    
    private final Integer code;
    private final String description;
    private final String identifier;
    private final String detail;
}

/**
 * 设备状态枚举（数据库存储数字编码）
 */
public enum DeviceStatus {
    ACTIVE(1, "正常", "设备正常工作状态"),
    INACTIVE(2, "离线", "设备离线或无响应"),
    FAULT(3, "故障", "设备故障需要维修");
    
    private final Integer code;
    private final String description;
    private final String detail;
}

/**
 * 数据类型码枚举（数据库存储数字编码）
 */
public enum DataCode {
    TEMPERATURE(1, "temperature", "温度", "°C", "环境温度传感器数据"),
    HUMIDITY(2, "humidity", "湿度", "%", "相对湿度传感器数据"),
    PM25(3, "pm25", "PM2.5", "μg/m³", "PM2.5颗粒物浓度"),
    CO2(4, "co2", "二氧化碳", "ppm", "CO2浓度数据"),
    MOTION(5, "motion", "人体感应", "0/1", "人体移动检测"),
    LIGHT(6, "light", "光照强度", "lux", "环境光照强度"),
    OCCUPANCY(7, "occupancy", "占用状态", "0/1", "空间占用状态"),
    NOISE(8, "noise", "噪音分贝", "dB", "环境噪音值");
    
    private final Integer code;
    private final String identifier;
    private final String description;
    private final String unit;
    private final String detail;
}

/**
 * 计算类型枚举（数据库存储数字编码）
 */
public enum CalcType {
    AVG(1, "平均值", "average", "计算指定时间窗口内的平均值"),
    MAX(2, "最大值", "maximum", "计算指定时间窗口内的最大值"),
    MIN(3, "最小值", "minimum", "计算指定时间窗口内的最小值"),
    COUNT(4, "计数", "count", "统计数据记录数量"),
    SUM(5, "求和", "sum", "计算指定时间窗口内的总和"),
    OCCUPANCY_RATE(6, "占用率", "occupancy_rate", "计算状态为1的时间占比");
    
    private final Integer code;
    private final String description;
    private final String identifier;
    private final String detail;
}

/**
 * 时间窗口枚举（数据库存储数字编码）
 */
public enum TimeWindow {
    MIN_5(1, 5 * 60L, 5 * 60 * 1000L, "5分钟", "高频实时统计"),
    MIN_15(2, 15 * 60L, 15 * 60 * 1000L, "15分钟", "中频数据统计"),
    HOUR_1(3, 60 * 60L, 60 * 60 * 1000L, "1小时", "小时级数据统计"),
    DAY_1(4, 24 * 60 * 60L, 24 * 60 * 60 * 1000L, "1天", "日级数据统计");
    
    private final Integer code;
    private final Long seconds;
    private final Long milliseconds;
    private final String description;
    private final String detail;
}
```

## 核心模块设计

### 1. 边缘程序模块（iot-edge）

#### 设计目标
- 支持多厂商设备接入（星纵YEASTAR、涂鸦TUYA、海康威视HIKVISION、大华DAHUA、宇视UNIVIEW等）
- 实现数据格式标准化和质量控制
- 支持指令下发和设备控制
- 提供断网续传和故障恢复能力
- 支持远程配置和程序更新

#### 核心功能
- **厂商适配器**：插件化设计，支持快速接入新厂商，包含数据采集转换和指令解析转换
- **数据清洗**：去重、异常值检测、数据格式校验
- **指令处理**：接收大数据平台下发的指令，转换为厂商特定格式并执行
- **本地缓存**：断网时本地存储，网络恢复后自动上报
- **批量上报**：减少网络开销，提升传输效率
- **远程配置**：支持配置参数远程下发和热更新
- **双向通信**：维护与大数据平台的MQTT over WebSocket连接
- **心跳机制**：定期向服务端发送心跳，维持连接状态

#### 部署方式
- 独立jar包，支持Windows/Linux跨平台部署
- 轻量级设计，资源占用<100MB内存
- 配置文件外置，支持热更新配置（无需重启）

#### 厂商适配器接口设计
```java
public interface DeviceAdapter {
    /**
     * 数据采集转换：将厂商原始数据转换为平台标准格式
     */
    List<IoTData> parseCollectedData(String rawData);
    
    /**
     * 指令转换：将平台标准指令转换为厂商特定格式
     */
    String convertCommand(String commandType, String commandData, String deviceCode);
    
    /**
     * 执行指令：向设备发送指令并获取执行结果
     */
    CommandResult executeCommand(String deviceAddress, String rawCommand);
    
    /**
     * 获取支持的设备类型
     */
    List<DeviceType> getSupportedDeviceTypes();
    
    /**
     * 获取厂商标识
     */
    DeviceVendor getVendor();
    
    /**
     * 验证数据格式
     */
    boolean validateData(String rawData);
}
```

### 2. API接口模块（iot-api）

#### 设计目标
- 高并发数据接收处理
- 数据验证和质量控制
- 指令下发和设备控制
- 设备管理和分组功能
- 实时监控和告警

#### 核心功能
- **数据接收接口**：支持1000条/批次的数据处理
- **AKSK认证**：采用Access Key + Secret Key方式进行API访问认证
- **数据验证**：多层验证机制，确保数据质量
- **指令下发**：通过MQTT over WebSocket向边缘程序下发控制指令
- **设备管理**：设备注册、分组管理、状态监控
- **远程配置**：边缘程序配置的远程管理和下发
- **负载均衡**：支持水平扩展，应对高并发场景
- **监控告警**：实时监控处理性能和异常情况

#### 性能指标
- 处理能力：10,000 TPS
- 响应时间：平均50ms，99%请求<200ms
- 可用性：99.9%（年停机时间<9小时）
- 指令下发延迟：<1秒

### 3. 数据存储层（集成在iot-common中）

#### 设计目标
- 支持海量时序数据存储
- 设备分组和层级管理
- 保证查询性能和数据一致性
- 优化存储成本

#### 存储策略

**分表设计**：
- **分表规则**：按时间按月分表（iot_data_YYYYMM），便于数据归档和查询优化
- **索引策略**：针对查询场景优化的复合索引，支持按设备、时间范围快速查询
- **扩展预留**：代码层面预留分库接口，后期可平滑升级到分库分表

**数据分层存储**：
- **热数据**：近3个月数据，SSD存储，毫秒级查询
- **温数据**：3-12个月数据，机械硬盘存储，秒级查询
- **冷数据**：12个月以上数据，归档存储，分钟级查询

**缓存策略**：
- **热点数据缓存**：设备最新状态，24小时TTL
- **指令队列缓存**：待执行指令队列，实时更新
- **计算结果缓存**：按计算精度设置不同TTL
- **查询缓存**：常用查询结果，1小时TTL

### 4. 数据计算层（iot-compute）

#### 设计目标
- 支持多种时间精度的数据统计
- 提供灵活的计算配置能力
- 保证计算结果的准确性和及时性

#### 计算类型

**基础统计**：
- 数值型数据：平均值、最大值、最小值、求和、计数
- 状态型数据：占用率、在线率、故障率

**时间窗口**：
- 分钟级：5分钟、15分钟统计
- 小时级：1小时统计
- 天级：日统计、周统计、月统计

**特殊业务计算**：
- 人体感应器占用率统计
- 环境传感器数据趋势分析
- 设备运行状态分析

#### 计算性能
- 计算延迟：<30秒（分钟级）、<5分钟（小时级）
- 数据准确性：>99.5%
- 支持任务并发：100个计算任务同时执行

## 技术架构优势

### 1. 高性能
- **批量处理**：减少数据库连接开销，提升吞吐量
- **异步写入**：解耦数据接收和存储，提升响应速度
- **缓存优化**：多层缓存设计，热点数据毫秒级访问
- **连接复用**：MQTT长连接和HTTP连接池复用，降低连接开销

### 2. 高可用
- **集群部署**：支持多节点部署，单点故障不影响服务
- **故障转移**：自动检测故障节点，流量自动切换
- **数据备份**：主从复制 + 定期备份，确保数据安全
- **多通道备份**：指令下发支持MQTT+HTTP双通道备份，确保可达性
- **健康检查**：实时监控服务状态，及时发现异常

### 3. 高扩展
- **水平扩展**：支持通过增加节点提升处理能力
- **垂直扩展**：支持通过升级硬件提升单机性能
- **功能扩展**：模块化设计，支持新功能快速集成
- **厂商扩展**：插件化适配器，新厂商设备快速接入

### 4. 易维护
- **标准化部署**：Docker化部署，环境一致性保证
- **监控完善**：全链路监控，问题快速定位
- **自动化运维**：自动扩缩容，减少人工干预
- **远程管理**：支持边缘程序远程配置和升级

## 实施计划

### 阶段一：基础框架搭建
**目标**：完成核心框架和数据采集功能

**开发任务**：
- [ ] 创建Maven多模块项目结构，配置父子模块依赖关系
- [ ] 搭建MySQL数据库，创建核心数据表结构（包含设备分组表和关联表）
- [ ] 配置Redis缓存环境，设计缓存Key规范
- [ ] 开发IoT数据统一模型类和基础工具类
- [ ] 实现星纵(YEASTAR)设备数据适配器，支持基础数据解析
- [ ] 开发边缘程序jar框架，支持配置文件外置和远程配置
- [ ] 实现HTTP批量数据上报接口，支持JSON格式数据接收
- [ ] 实现设备分组管理功能，支持层级分组和设备多分组归属
- [ ] 创建基础的单元测试和集成测试用例

**验收标准**：
- [ ] 能够成功采集星纵(YEASTAR)设备数据并上报到服务端
- [ ] 数据能够正确入库到MySQL按月分表中，支持设备分组查询
- [ ] Redis缓存能够正常存储设备最新状态数据
- [ ] 边缘程序支持断网缓存和自动重连
- [ ] 设备分组功能完整，支持多对多关联

### 阶段二：数据存储与配置优化
**目标**：完善数据存储和远程配置功能

**开发任务**：
- [ ] 实现按月自动创建数据表的功能，支持表结构自动初始化
- [ ] 开发数据库连接池配置和优化，确保高并发下的稳定性
- [ ] 实现Redis缓存服务封装，支持设备最新数据缓存
- [ ] 开发设备分组管理API接口，支持层级分组和设备归属管理
- [ ] 实现AKSK认证机制，包含AccessKey/SecretKey生成和验证
- [ ] 实现边缘程序远程配置功能，支持配置热更新
- [ ] 实现数据清洗和验证逻辑，过滤异常数据
- [ ] 配置MySQL主从复制，实现数据备份机制
- [ ] 开发数据归档脚本，支持历史数据定期归档
- [ ] 进行数据库性能测试，优化查询索引

**验收标准**：
- [ ] 系统能够处理1万TPS的数据写入压力
- [ ] 查询响应时间在100ms以内
- [ ] AKSK认证功能正常，支持权限控制
- [ ] 边缘程序能够成功接收远程配置并生效
- [ ] 数据备份和恢复机制验证通过
- [ ] 设备分组关联功能完整，支持多对多查询

### 阶段三：计算引擎与多厂商适配
**目标**：实现分精度计算功能和多厂商设备支持

**开发任务**：
- [ ] 设计计算任务配置表和管理界面，支持可视化任务配置
- [ ] 实现基础统计算法：平均值、最大值、最小值、计数、求和
- [ ] 开发时间窗口计算逻辑，支持5分钟、1小时、1天精度
- [ ] 实现定时任务调度器，支持Cron表达式配置
- [ ] 开发计算结果存储和缓存机制
- [ ] 实现涂鸦设备数据适配器，支持数据采集
- [ ] 实现海康威视设备数据适配器，扩展设备支持范围
- [ ] 开发厂商适配器工厂模式，支持动态加载和切换
- [ ] 开发计算任务监控和告警功能
- [ ] 创建计算性能测试用例，验证计算准确性

**验收标准**：
- [ ] 能够支持100个并发计算任务
- [ ] 分钟级计算延迟小于30秒
- [ ] 计算结果准确率达到99.5%以上
- [ ] 支持星纵、涂鸦、海康威视三家厂商设备
- [ ] 设备分组统计功能完整，支持按分组计算

### 阶段四：系统集成与生产部署
**目标**：系统集成测试、性能优化和生产部署

**开发任务**：
- [ ] 整合所有模块，解决模块间依赖和冲突问题
- [ ] 配置Docker容器化部署，编写docker-compose配置文件
- [ ] 进行端到端集成测试，验证完整业务流程
- [ ] 执行性能压力测试，验证系统承载能力
- [ ] 实现边缘程序版本管理和远程升级功能
- [ ] 配置生产环境服务器，包括数据库、Redis
- [ ] 部署监控系统，配置系统健康检查和告警
- [ ] 实现设备状态实时监控和异常告警
- [ ] 编写部署文档、操作手册和故障排查指南
- [ ] 编写API接口文档，包含AKSK认证和设备管理使用说明
- [ ] 进行用户培训和系统验收

**验收标准**：
- [ ] 系统整体可用性达到99.9%
- [ ] 数据处理能力达到万级TPS
- [ ] 边缘程序能够稳定运行，断网恢复后数据不丢失
- [ ] 设备分组管理功能完整，支持复杂查询场景
- [ ] 完整覆盖预期的业务场景和功能需求
- [ ] 用户培训完成，系统投入生产使用