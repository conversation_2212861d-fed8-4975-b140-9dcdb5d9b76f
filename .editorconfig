# IoT大数据平台 - 编辑器配置文件
# 统一代码格式化规范，支持多种IDE和编辑器

# 根配置文件
root = true

# 所有文件的默认配置
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 4

# Java源代码文件
[*.java]
indent_style = space
indent_size = 4
max_line_length = 120
continuation_indent_size = 8

# XML文件（包括Maven POM文件）
[*.{xml,pom}]
indent_style = space
indent_size = 4

# YAML文件（包括Spring Boot配置文件）
[*.{yml,yaml}]
indent_style = space
indent_size = 2

# JSON文件
[*.json]
indent_style = space
indent_size = 2

# Properties文件
[*.properties]
indent_style = space
indent_size = 4

# SQL文件
[*.sql]
indent_style = space
indent_size = 4

# Shell脚本
[*.{sh,bash}]
indent_style = space
indent_size = 4
end_of_line = lf

# Dockerfile
[Dockerfile*]
indent_style = space
indent_size = 4

# Docker Compose文件
[docker-compose*.yml]
indent_style = space
indent_size = 2

# Markdown文件
[*.md]
indent_style = space
indent_size = 2
trim_trailing_whitespace = false

# 配置文件
[*.{conf,config,cfg}]
indent_style = space
indent_size = 4

# 日志配置文件
[logback*.xml]
indent_style = space
indent_size = 4

# 前端文件（如果有）
[*.{js,ts,jsx,tsx}]
indent_style = space
indent_size = 2

[*.{css,scss,less}]
indent_style = space
indent_size = 2

[*.html]
indent_style = space
indent_size = 2

# 批处理文件
[*.{bat,cmd}]
end_of_line = crlf

# Makefile
[{Makefile,makefile}]
indent_style = tab
indent_size = 4
