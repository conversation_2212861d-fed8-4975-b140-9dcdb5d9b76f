package com.iot.platform.api.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iot.platform.api.BaseTest;
import com.iot.platform.api.service.impl.DeviceServiceImpl;
import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.iot.platform.common.enums.DeviceType;
import com.iot.platform.common.enums.DeviceVendor;
import com.iot.platform.common.mapper.DeviceMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 设备服务测试类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@DisplayName("设备服务测试")
class DeviceServiceTest extends BaseTest {

    @Mock
    private DeviceMapper deviceMapper;

    @InjectMocks
    private DeviceServiceImpl deviceService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("根据设备编码获取设备 - 成功")
    void testGetByDeviceCode_Success() {
        // 准备测试数据
        Device expectedDevice = createTestDevice(TEST_DEVICE_CODE, "测试设备");

        // Mock mapper方法
        when(deviceMapper.selectOne(any(QueryWrapper.class))).thenReturn(expectedDevice);

        // 执行测试
        Device actualDevice = deviceService.getByDeviceCode(TEST_DEVICE_CODE);

        // 验证结果
        assertNotNull(actualDevice);
        assertEquals(TEST_DEVICE_CODE, actualDevice.getDeviceCode());
        assertEquals("测试设备", actualDevice.getDeviceName());
        assertEquals(TEST_PROJECT_ID, actualDevice.getProjectId());

        // 验证方法调用
        verify(deviceMapper, times(1)).selectOne(any(QueryWrapper.class));
    }

    @Test
    @DisplayName("根据设备编码获取设备 - 设备不存在")
    void testGetByDeviceCode_NotFound() {
        // Mock mapper方法返回null
        when(deviceMapper.selectOne(any(QueryWrapper.class))).thenReturn(null);

        // 执行测试
        Device actualDevice = deviceService.getByDeviceCode("NON_EXISTENT_DEVICE");

        // 验证结果
        assertNull(actualDevice);

        // 验证方法调用
        verify(deviceMapper, times(1)).selectOne(any(QueryWrapper.class));
    }

    @Test
    @DisplayName("根据项目ID获取设备列表 - 成功")
    void testGetDevicesByProjectId_Success() {
        // 准备测试数据
        Device device1 = createTestDevice("DEVICE_001", "设备1");
        Device device2 = createTestDevice("DEVICE_002", "设备2");
        List<Device> expectedDevices = Arrays.asList(device1, device2);

        // Mock mapper方法
        when(deviceMapper.selectList(any(QueryWrapper.class))).thenReturn(expectedDevices);

        // 执行测试
        List<Device> actualDevices = deviceService.getDevicesByProjectId(TEST_PROJECT_ID);

        // 验证结果
        assertNotNull(actualDevices);
        assertEquals(2, actualDevices.size());
        assertEquals("DEVICE_001", actualDevices.get(0).getDeviceCode());
        assertEquals("DEVICE_002", actualDevices.get(1).getDeviceCode());

        // 验证方法调用
        verify(deviceMapper, times(1)).selectList(any(QueryWrapper.class));
    }

    @Test
    @DisplayName("保存设备 - 成功")
    void testSaveDevice_Success() {
        // 准备测试数据
        Device device = createTestDevice("NEW_DEVICE", "新设备");

        // Mock mapper方法
        when(deviceMapper.insert(any(Device.class))).thenReturn(1);

        // 执行测试
        boolean result = deviceService.save(device);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(deviceMapper, times(1)).insert(any(Device.class));
    }

    @Test
    @DisplayName("更新设备状态 - 成功")
    void testUpdateDeviceStatus_Success() {
        // 准备测试数据
        Device existingDevice = createTestDevice(TEST_DEVICE_CODE, "测试设备");
        existingDevice.setStatus(DeviceStatus.ACTIVE.getCode());

        // Mock mapper方法
        when(deviceMapper.selectOne(any(QueryWrapper.class))).thenReturn(existingDevice);
        when(deviceMapper.updateById(any(Device.class))).thenReturn(1);

        // 执行测试
        boolean result = deviceService.updateDeviceStatus(TEST_DEVICE_CODE, DeviceStatus.INACTIVE);

        // 验证结果
        assertTrue(result);
        assertEquals(DeviceStatus.INACTIVE.getCode(), existingDevice.getStatus());

        // 验证方法调用
        verify(deviceMapper, times(1)).selectOne(any(QueryWrapper.class));
        verify(deviceMapper, times(1)).updateById(any(Device.class));
    }

    @Test
    @DisplayName("更新设备状态 - 设备不存在")
    void testUpdateDeviceStatus_DeviceNotFound() {
        // Mock mapper方法返回null
        when(deviceMapper.selectOne(any(QueryWrapper.class))).thenReturn(null);

        // 执行测试
        boolean result = deviceService.updateDeviceStatus("NON_EXISTENT_DEVICE", DeviceStatus.INACTIVE);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        verify(deviceMapper, times(1)).selectOne(any(QueryWrapper.class));
        verify(deviceMapper, never()).updateById(any(Device.class));
    }

    @Test
    @DisplayName("检查设备编码是否存在 - 存在")
    void testIsDeviceCodeExists_Exists() {
        // Mock mapper方法
        when(deviceMapper.selectCount(any(QueryWrapper.class))).thenReturn(1L);

        // 执行测试
        boolean exists = deviceService.isDeviceCodeExists(TEST_DEVICE_CODE);

        // 验证结果
        assertTrue(exists);

        // 验证方法调用
        verify(deviceMapper, times(1)).selectCount(any(QueryWrapper.class));
    }

    @Test
    @DisplayName("检查设备编码是否存在 - 不存在")
    void testIsDeviceCodeExists_NotExists() {
        // Mock mapper方法
        when(deviceMapper.selectCount(any(QueryWrapper.class))).thenReturn(0L);

        // 执行测试
        boolean exists = deviceService.isDeviceCodeExists("NON_EXISTENT_DEVICE");

        // 验证结果
        assertFalse(exists);

        // 验证方法调用
        verify(deviceMapper, times(1)).selectCount(any(QueryWrapper.class));
    }

    /**
     * 创建测试设备对象
     */
    private Device createTestDevice(String deviceCode, String deviceName) {
        Device device = new Device();
        device.setId(1L);
        device.setProjectId(TEST_PROJECT_ID);
        device.setDeviceCode(deviceCode);
        device.setDeviceName(deviceName);
        device.setDeviceType(DeviceType.SENSOR.getCode());
        device.setVendor(DeviceVendor.YEASTAR.getCode());
        device.setModel("TEST-MODEL-001");
        device.setLocation("测试位置");
        device.setStatus(DeviceStatus.ACTIVE.getCode());
        device.setEdgeProgramId("edge_test_001");
        device.setDescription("测试设备描述");
        return device;
    }
}
