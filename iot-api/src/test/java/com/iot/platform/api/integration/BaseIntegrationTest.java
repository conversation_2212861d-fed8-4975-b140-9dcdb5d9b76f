package com.iot.platform.api.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iot.platform.api.ApiApplication;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

/**
 * 集成测试基类
 * 提供集成测试的通用配置和工具方法
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@SpringBootTest(classes = ApiApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@TestPropertySource(locations = "classpath:application-test.yml")
@Transactional
public abstract class BaseIntegrationTest {

    @Autowired
    protected WebApplicationContext webApplicationContext;

    @Autowired
    protected JdbcTemplate jdbcTemplate;

    @Autowired
    protected RedisTemplate<String, Object> redisTemplate;

    @Autowired
    protected ObjectMapper objectMapper;

    protected MockMvc mockMvc;

    // 测试常量
    protected static final String TEST_PROJECT_ID = "test_project_001";
    protected static final String TEST_DEVICE_CODE = "TEST_DEVICE_001";
    protected static final String TEST_EDGE_ID = "test_edge_001";
    protected static final String API_BASE_PATH = "/api/v1";

    @BeforeEach
    void setUpIntegrationTest() {
        // 初始化MockMvc
        mockMvc = MockMvcBuilders
                .webAppContextSetup(webApplicationContext)
                .build();

        // 清理测试数据
        cleanupTestData();

        // 初始化测试数据
        initTestData();
    }

    /**
     * 清理测试数据
     */
    protected void cleanupTestData() {
        try {
            // 清理Redis缓存
            redisTemplate.getConnectionFactory().getConnection().flushDb();

            // 清理数据库测试数据
            jdbcTemplate.execute("DELETE FROM device_data WHERE project_id = '" + TEST_PROJECT_ID + "'");
            jdbcTemplate.execute("DELETE FROM compute_result WHERE project_id = '" + TEST_PROJECT_ID + "'");
            jdbcTemplate.execute("DELETE FROM device WHERE project_id = '" + TEST_PROJECT_ID + "'");
            jdbcTemplate.execute("DELETE FROM project WHERE project_id = '" + TEST_PROJECT_ID + "'");

        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    /**
     * 初始化测试数据
     */
    protected void initTestData() {
        try {
            // 创建测试项目
            createTestProject();

            // 创建测试设备
            createTestDevice();

        } catch (Exception e) {
            throw new RuntimeException("初始化测试数据失败", e);
        }
    }

    /**
     * 创建测试项目
     */
    protected void createTestProject() {
        String sql = """
            INSERT INTO project (project_id, project_name, description, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, NOW(), NOW())
            """;

        jdbcTemplate.update(sql,
                TEST_PROJECT_ID,
                "测试项目",
                "用于集成测试的项目",
                1);
    }

    /**
     * 创建测试设备
     */
    protected void createTestDevice() {
        String sql = """
            INSERT INTO device (project_id, device_code, device_name, device_type, vendor, model,
                               location, status, edge_program_id, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            """;

        jdbcTemplate.update(sql,
                TEST_PROJECT_ID,
                TEST_DEVICE_CODE,
                "测试设备",
                1, // SENSOR
                1, // YEASTAR
                "TEST-MODEL-001",
                "测试位置",
                1, // ACTIVE
                TEST_EDGE_ID,
                "用于集成测试的设备");
    }

    /**
     * 创建测试设备数据
     */
    protected void createTestDeviceData(String deviceCode, byte dataCode, double dataValue) {
        String sql = """
            INSERT INTO device_data (project_id, device_code, data_code, data_value, quality,
                                   timestamp, edge_program_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            """;

        jdbcTemplate.update(sql,
                TEST_PROJECT_ID,
                deviceCode,
                dataCode,
                dataValue,
                (byte) 1,
                System.currentTimeMillis(),
                TEST_EDGE_ID);
    }

    /**
     * 获取设备数据数量
     */
    protected int getDeviceDataCount(String deviceCode) {
        String sql = "SELECT COUNT(*) FROM device_data WHERE device_code = ?";
        return jdbcTemplate.queryForObject(sql, Integer.class, deviceCode);
    }

    /**
     * 获取计算结果数量
     */
    protected int getComputeResultCount(String projectId) {
        String sql = "SELECT COUNT(*) FROM compute_result WHERE project_id = ?";
        return jdbcTemplate.queryForObject(sql, Integer.class, projectId);
    }

    /**
     * 检查Redis中是否存在指定键
     */
    protected boolean existsInRedis(String key) {
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }

    /**
     * 从Redis获取值
     */
    protected Object getFromRedis(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    /**
     * 向Redis设置值
     */
    protected void setToRedis(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    /**
     * 等待异步操作完成
     */
    protected void waitForAsyncOperation(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 将对象转换为JSON字符串
     */
    protected String toJson(Object object) {
        try {
            return objectMapper.writeValueAsString(object);
        } catch (Exception e) {
            throw new RuntimeException("转换JSON失败", e);
        }
    }

    /**
     * 将JSON字符串转换为对象
     */
    protected <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("解析JSON失败", e);
        }
    }

    /**
     * 生成随机设备编码
     */
    protected String generateDeviceCode() {
        return "TEST_DEVICE_" + System.currentTimeMillis();
    }

    /**
     * 生成随机数据值
     */
    protected double generateRandomValue(double min, double max) {
        return min + (max - min) * Math.random();
    }

    /**
     * 验证响应结果
     */
    protected void assertSuccessResponse(String responseBody) {
        try {
            var response = objectMapper.readTree(responseBody);
            assertEquals(200, response.get("code").asInt());
            assertTrue(response.get("success").asBoolean());
        } catch (Exception e) {
            throw new RuntimeException("验证响应失败", e);
        }
    }

    /**
     * 验证错误响应
     */
    protected void assertErrorResponse(String responseBody, int expectedCode) {
        try {
            var response = objectMapper.readTree(responseBody);
            assertEquals(expectedCode, response.get("code").asInt());
            assertFalse(response.get("success").asBoolean());
        } catch (Exception e) {
            throw new RuntimeException("验证错误响应失败", e);
        }
    }

    // 导入断言方法
    protected static void assertEquals(Object expected, Object actual) {
        org.junit.jupiter.api.Assertions.assertEquals(expected, actual);
    }

    protected static void assertTrue(boolean condition) {
        org.junit.jupiter.api.Assertions.assertTrue(condition);
    }

    protected static void assertTrue(boolean condition, String message) {
        org.junit.jupiter.api.Assertions.assertTrue(condition, message);
    }

    protected static void assertFalse(boolean condition) {
        org.junit.jupiter.api.Assertions.assertFalse(condition);
    }

    protected static void assertNotNull(Object object) {
        org.junit.jupiter.api.Assertions.assertNotNull(object);
    }
}
