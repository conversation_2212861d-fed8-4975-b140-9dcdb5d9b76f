package com.iot.platform.api.performance;

import com.iot.platform.api.integration.BaseIntegrationTest;
import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.api.dto.DeviceDataRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 设备数据性能测试
 * 测试系统在高并发和大数据量情况下的性能表现
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@DisplayName("设备数据性能测试")
@TestPropertySource(properties = {
        "logging.level.com.iot.platform=WARN", // 减少日志输出
        "spring.jpa.show-sql=false"
})
class DeviceDataPerformanceTest extends BaseIntegrationTest {

    private static final int CONCURRENT_THREADS = 10;
    private static final int REQUESTS_PER_THREAD = 100;
    private static final int BATCH_SIZE = 50;

    @Test
    @DisplayName("单条数据上报性能测试")
    void testSingleDataReportPerformance() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_THREADS);
        CountDownLatch latch = new CountDownLatch(CONCURRENT_THREADS);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 启动并发测试
        for (int i = 0; i < CONCURRENT_THREADS; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < REQUESTS_PER_THREAD; j++) {
                        long requestStart = System.currentTimeMillis();

                        DeviceDataRequest request = createPerformanceTestData(threadId, j);

                        try {
                            mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                                            .contentType(MediaType.APPLICATION_JSON)
                                            .content(toJson(request)))
                                    .andExpect(status().isOk());

                            successCount.incrementAndGet();
                            long responseTime = System.currentTimeMillis() - requestStart;
                            totalResponseTime.addAndGet(responseTime);

                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            System.err.println("Request failed: " + e.getMessage());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(5, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();

        executor.shutdown();

        // 输出性能统计
        printPerformanceStats("单条数据上报性能测试",
                startTime, endTime, successCount.get(), errorCount.get(), totalResponseTime.get());

        // 验证结果
        assertTrue(completed, "测试未在预期时间内完成");
        assertTrue(successCount.get() > 0, "没有成功的请求");

        // 验证数据库中的数据
        int totalDataCount = jdbcTemplate.queryForObject(
                "SELECT COUNT(*) FROM device_data WHERE project_id = ?",
                Integer.class, TEST_PROJECT_ID);
        assertEquals(successCount.get(), totalDataCount);
    }

    @Test
    @DisplayName("批量数据上报性能测试")
    void testBatchDataReportPerformance() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_THREADS);
        CountDownLatch latch = new CountDownLatch(CONCURRENT_THREADS);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        AtomicLong totalDataPoints = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 启动并发批量测试
        for (int i = 0; i < CONCURRENT_THREADS; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < REQUESTS_PER_THREAD / 10; j++) { // 减少批次数量，增加每批数据量
                        long requestStart = System.currentTimeMillis();

                        BatchDataRequest batchRequest = createBatchPerformanceTestData(threadId, j);

                        try {
                            mockMvc.perform(post(API_BASE_PATH + "/device-data/batch-report")
                                            .contentType(MediaType.APPLICATION_JSON)
                                            .content(toJson(batchRequest)))
                                    .andExpect(status().isOk());

                            successCount.incrementAndGet();
                            totalDataPoints.addAndGet(batchRequest.getDataList().size());
                            long responseTime = System.currentTimeMillis() - requestStart;
                            totalResponseTime.addAndGet(responseTime);

                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            System.err.println("Batch request failed: " + e.getMessage());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(10, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();

        executor.shutdown();

        // 输出性能统计
        printBatchPerformanceStats("批量数据上报性能测试",
                startTime, endTime, successCount.get(), errorCount.get(),
                totalResponseTime.get(), totalDataPoints.get());

        // 验证结果
        assertTrue(completed, "批量测试未在预期时间内完成");
        assertTrue(successCount.get() > 0, "没有成功的批量请求");
    }

    @Test
    @DisplayName("数据查询性能测试")
    void testDataQueryPerformance() throws Exception {
        // 先准备大量测试数据
        prepareQueryTestData(1000);

        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_THREADS);
        CountDownLatch latch = new CountDownLatch(CONCURRENT_THREADS);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 启动并发查询测试
        for (int i = 0; i < CONCURRENT_THREADS; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < REQUESTS_PER_THREAD; j++) {
                        long requestStart = System.currentTimeMillis();

                        try {
                            mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                                            .get(API_BASE_PATH + "/device-data/query")
                                            .param("deviceCode", TEST_DEVICE_CODE)
                                            .param("limit", "100"))
                                    .andExpect(status().isOk());

                            successCount.incrementAndGet();
                            long responseTime = System.currentTimeMillis() - requestStart;
                            totalResponseTime.addAndGet(responseTime);

                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            System.err.println("Query failed: " + e.getMessage());
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(3, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();

        executor.shutdown();

        // 输出性能统计
        printPerformanceStats("数据查询性能测试",
                startTime, endTime, successCount.get(), errorCount.get(), totalResponseTime.get());

        // 验证结果
        assertTrue(completed, "查询测试未在预期时间内完成");
        assertTrue(successCount.get() > 0, "没有成功的查询请求");
    }

    @Test
    @DisplayName("混合负载性能测试")
    void testMixedWorkloadPerformance() throws Exception {
        ExecutorService executor = Executors.newFixedThreadPool(CONCURRENT_THREADS);
        CountDownLatch latch = new CountDownLatch(CONCURRENT_THREADS);
        AtomicInteger reportSuccessCount = new AtomicInteger(0);
        AtomicInteger querySuccessCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        // 启动混合负载测试
        for (int i = 0; i < CONCURRENT_THREADS; i++) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    for (int j = 0; j < REQUESTS_PER_THREAD; j++) {
                        try {
                            if (j % 3 == 0) {
                                // 数据上报
                                DeviceDataRequest request = createPerformanceTestData(threadId, j);
                                mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                                                .contentType(MediaType.APPLICATION_JSON)
                                                .content(toJson(request)))
                                        .andExpect(status().isOk());
                                reportSuccessCount.incrementAndGet();

                            } else {
                                // 数据查询
                                mockMvc.perform(org.springframework.test.web.servlet.request.MockMvcRequestBuilders
                                                .get(API_BASE_PATH + "/device-data/query")
                                                .param("deviceCode", TEST_DEVICE_CODE)
                                                .param("limit", "10"))
                                        .andExpect(status().isOk());
                                querySuccessCount.incrementAndGet();
                            }

                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(5, TimeUnit.MINUTES);
        long endTime = System.currentTimeMillis();

        executor.shutdown();

        // 输出混合负载统计
        printMixedWorkloadStats("混合负载性能测试",
                startTime, endTime, reportSuccessCount.get(), querySuccessCount.get(), errorCount.get());

        // 验证结果
        assertTrue(completed, "混合负载测试未在预期时间内完成");
        assertTrue(reportSuccessCount.get() > 0, "没有成功的上报请求");
        assertTrue(querySuccessCount.get() > 0, "没有成功的查询请求");
    }

    /**
     * 创建性能测试数据
     */
    private DeviceDataRequest createPerformanceTestData(int threadId, int requestId) {
        DeviceDataRequest request = new DeviceDataRequest();
        request.setProjectId(TEST_PROJECT_ID);
        request.setDeviceCode(TEST_DEVICE_CODE);
        request.setDataCode((byte) (requestId % 3 + 1)); // 轮换数据类型
        request.setDataValue(new java.math.BigDecimal(generateRandomValue(20.0, 30.0)));
        request.setQuality((byte) 1);
        request.setReportTime(System.currentTimeMillis() - (requestId * 1000));
        request.setEdgeProgramId(TEST_EDGE_ID);
        return request;
    }

    /**
     * 创建批量性能测试数据
     */
    private BatchDataRequest createBatchPerformanceTestData(int threadId, int batchId) {
        BatchDataRequest batchRequest = new BatchDataRequest();
        batchRequest.setBatchId("perf_batch_" + threadId + "_" + batchId);
        batchRequest.setEdgeProgramId(TEST_EDGE_ID);
        batchRequest.setBatchTime(System.currentTimeMillis());

        List<DeviceDataRequest> dataList = new ArrayList<>();
        for (int i = 0; i < BATCH_SIZE; i++) {
            DeviceDataRequest data = createPerformanceTestData(threadId, batchId * BATCH_SIZE + i);
            dataList.add(data);
        }

        batchRequest.setDataList(dataList);
        return batchRequest;
    }

    /**
     * 准备查询测试数据
     */
    private void prepareQueryTestData(int dataCount) {
        String sql = """
            INSERT INTO device_data (project_id, device_code, data_code, data_value, quality,
                                   timestamp, edge_program_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            """;

        for (int i = 0; i < dataCount; i++) {
            jdbcTemplate.update(sql,
                    TEST_PROJECT_ID,
                    TEST_DEVICE_CODE,
                    (byte) (i % 3 + 1),
                    generateRandomValue(20.0, 30.0),
                    (byte) 1,
                    System.currentTimeMillis() - (i * 1000),
                    TEST_EDGE_ID);
        }
    }

    /**
     * 输出性能统计信息
     */
    private void printPerformanceStats(String testName, long startTime, long endTime,
                                     int successCount, int errorCount, long totalResponseTime) {
        long totalTime = endTime - startTime;
        double throughput = (double) successCount / totalTime * 1000; // 每秒请求数
        double avgResponseTime = successCount > 0 ? (double) totalResponseTime / successCount : 0;
        double errorRate = (double) errorCount / (successCount + errorCount) * 100;

        System.out.println("\n========== " + testName + " ==========");
        System.out.println("总耗时: " + totalTime + " ms");
        System.out.println("成功请求: " + successCount);
        System.out.println("失败请求: " + errorCount);
        System.out.println("错误率: " + String.format("%.2f", errorRate) + "%");
        System.out.println("吞吐量: " + String.format("%.2f", throughput) + " 请求/秒");
        System.out.println("平均响应时间: " + String.format("%.2f", avgResponseTime) + " ms");
        System.out.println("================================================\n");
    }

    /**
     * 输出批量性能统计信息
     */
    private void printBatchPerformanceStats(String testName, long startTime, long endTime,
                                          int successCount, int errorCount, long totalResponseTime, long totalDataPoints) {
        long totalTime = endTime - startTime;
        double batchThroughput = (double) successCount / totalTime * 1000; // 每秒批次数
        double dataThroughput = (double) totalDataPoints / totalTime * 1000; // 每秒数据点数
        double avgResponseTime = successCount > 0 ? (double) totalResponseTime / successCount : 0;

        System.out.println("\n========== " + testName + " ==========");
        System.out.println("总耗时: " + totalTime + " ms");
        System.out.println("成功批次: " + successCount);
        System.out.println("失败批次: " + errorCount);
        System.out.println("总数据点: " + totalDataPoints);
        System.out.println("批次吞吐量: " + String.format("%.2f", batchThroughput) + " 批次/秒");
        System.out.println("数据吞吐量: " + String.format("%.2f", dataThroughput) + " 数据点/秒");
        System.out.println("平均响应时间: " + String.format("%.2f", avgResponseTime) + " ms");
        System.out.println("================================================\n");
    }

    /**
     * 输出混合负载统计信息
     */
    private void printMixedWorkloadStats(String testName, long startTime, long endTime,
                                       int reportSuccessCount, int querySuccessCount, int errorCount) {
        long totalTime = endTime - startTime;
        int totalSuccess = reportSuccessCount + querySuccessCount;
        double throughput = (double) totalSuccess / totalTime * 1000;

        System.out.println("\n========== " + testName + " ==========");
        System.out.println("总耗时: " + totalTime + " ms");
        System.out.println("成功上报: " + reportSuccessCount);
        System.out.println("成功查询: " + querySuccessCount);
        System.out.println("失败请求: " + errorCount);
        System.out.println("总吞吐量: " + String.format("%.2f", throughput) + " 请求/秒");
        System.out.println("================================================\n");
    }
}
