package com.iot.platform.api.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iot.platform.api.BaseTest;
import com.iot.platform.api.service.DeviceService;
import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.iot.platform.common.enums.DeviceType;
import com.iot.platform.common.enums.DeviceVendor;
import com.iot.platform.common.response.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 设备控制器测试类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@AutoConfigureWebMvc
@DisplayName("设备控制器测试")
class DeviceControllerTest extends BaseTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @MockBean
    private DeviceService deviceService;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    @DisplayName("获取设备列表 - 成功")
    void testGetDeviceList_Success() throws Exception {
        // 准备测试数据
        Device device1 = createTestDevice("DEVICE_001", "测试设备1");
        Device device2 = createTestDevice("DEVICE_002", "测试设备2");
        List<Device> deviceList = Arrays.asList(device1, device2);

        // Mock服务方法
        when(deviceService.list(any(com.baomidou.mybatisplus.core.conditions.Wrapper.class))).thenReturn(deviceList);

        // 执行测试
        mockMvc.perform(get("/api/device/list")
                        .param("projectId", TEST_PROJECT_ID)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andExpect(jsonPath("$.data[0].deviceCode").value("DEVICE_001"))
                .andExpect(jsonPath("$.data[1].deviceCode").value("DEVICE_002"));
    }

    @Test
    @DisplayName("根据设备编码获取设备 - 成功")
    void testGetDeviceByCode_Success() throws Exception {
        // 准备测试数据
        Device device = createTestDevice(TEST_DEVICE_CODE, "测试设备");

        // Mock服务方法
        when(deviceService.getByDeviceCode(TEST_DEVICE_CODE)).thenReturn(device);

        // 执行测试
        mockMvc.perform(get("/api/device/{deviceCode}", TEST_DEVICE_CODE)
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.deviceCode").value(TEST_DEVICE_CODE))
                .andExpect(jsonPath("$.data.deviceName").value("测试设备"));
    }

    @Test
    @DisplayName("根据设备编码获取设备 - 设备不存在")
    void testGetDeviceByCode_NotFound() throws Exception {
        // Mock服务方法返回null
        when(deviceService.getByDeviceCode(anyString())).thenReturn(null);

        // 执行测试
        mockMvc.perform(get("/api/device/{deviceCode}", "NON_EXISTENT_DEVICE")
                        .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("设备不存在"));
    }

    @Test
    @DisplayName("创建设备 - 成功")
    void testCreateDevice_Success() throws Exception {
        // 准备测试数据
        Device device = createTestDevice("NEW_DEVICE_001", "新设备");

        // Mock服务方法
        when(deviceService.save(any(Device.class))).thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/api/device")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(device)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.message").value("创建成功"));
    }

    @Test
    @DisplayName("创建设备 - 参数验证失败")
    void testCreateDevice_ValidationFailed() throws Exception {
        // 准备无效的测试数据（缺少必填字段）
        Device device = new Device();
        device.setDeviceName("测试设备");
        // 缺少deviceCode、deviceType、vendor等必填字段

        // 执行测试
        mockMvc.perform(post("/api/device")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(device)))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    /**
     * 创建测试设备对象
     */
    private Device createTestDevice(String deviceCode, String deviceName) {
        Device device = new Device();
        device.setId(1L);
        device.setProjectId(TEST_PROJECT_ID);
        device.setDeviceCode(deviceCode);
        device.setDeviceName(deviceName);
        device.setDeviceType(DeviceType.SENSOR.getCode());
        device.setVendor(DeviceVendor.YEASTAR.getCode());
        device.setModel("TEST-MODEL-001");
        device.setLocation("测试位置");
        device.setStatus(DeviceStatus.ACTIVE.getCode());
        device.setEdgeProgramId("edge_test_001");
        device.setDescription("测试设备描述");
        return device;
    }
}
