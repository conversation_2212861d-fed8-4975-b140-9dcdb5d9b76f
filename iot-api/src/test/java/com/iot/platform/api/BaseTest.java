package com.iot.platform.api;

import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

/**
 * 测试基类
 * 提供通用的测试配置和工具方法
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = ApiApplication.class)
@ActiveProfiles("test")
@Transactional
public abstract class BaseTest {

    /**
     * 测试用的项目ID
     */
    protected static final String TEST_PROJECT_ID = "test_project";

    /**
     * 测试用的设备编码
     */
    protected static final String TEST_DEVICE_CODE = "TEST_DEVICE_001";

    /**
     * 测试用的Access Key
     */
    protected static final String TEST_ACCESS_KEY = "test_access_key";

    /**
     * 测试用的Secret Key
     */
    protected static final String TEST_SECRET_KEY = "test_secret_key";

    /**
     * 测试用的时间戳
     */
    protected static final long TEST_TIMESTAMP = 1703673600000L; // 2023-12-27 12:00:00

    /**
     * 获取当前时间戳
     */
    protected long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 等待指定毫秒数
     */
    protected void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
