package com.iot.platform.api.integration;

import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.api.dto.DeviceDataRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 端到端集成测试
 * 测试完整的数据流：数据上报 -> 存储 -> 计算 -> 查询
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@DisplayName("端到端集成测试")
class EndToEndIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("完整数据流测试：数据上报 -> 存储 -> 查询")
    void testCompleteDataFlow() throws Exception {
        // 1. 上报设备数据
        DeviceDataRequest dataRequest = createTemperatureDataRequest(25.5);

        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(dataRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));

        // 2. 验证数据已存储
        int dataCount = getDeviceDataCount(TEST_DEVICE_CODE);
        assertEquals(1, dataCount);

        // 3. 查询刚上报的数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("dataCode", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(1))
                .andExpect(jsonPath("$.data[0].dataValue").value(25.5));

        // 4. 检查设备在线状态
        mockMvc.perform(get(API_BASE_PATH + "/device-data/online-status")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.online").value(true));
    }

    @Test
    @DisplayName("批量数据处理流程测试")
    void testBatchDataProcessingFlow() throws Exception {
        // 1. 批量上报多种类型的设备数据
        BatchDataRequest batchRequest = createMultiTypeDataBatch();

        MvcResult result = mockMvc.perform(post(API_BASE_PATH + "/device-data/batch-report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(batchRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.totalCount").value(6))
                .andReturn();

        // 2. 验证批量数据已存储
        int totalDataCount = getDeviceDataCount(TEST_DEVICE_CODE);
        assertEquals(6, totalDataCount);

        // 3. 查询不同类型的数据
        // 查询温度数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("dataCode", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(3));

        // 查询湿度数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("dataCode", "2"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(2));

        // 查询PM2.5数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("dataCode", "3"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(1));

        // 4. 获取设备统计信息
        long endTime = System.currentTimeMillis();
        long startTime = endTime - 3600000; // 1小时前

        mockMvc.perform(get(API_BASE_PATH + "/device-data/statistics")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("startTime", String.valueOf(startTime))
                        .param("endTime", String.valueOf(endTime)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.totalCount").value(6))
                .andExpect(jsonPath("$.data.dataTypes.length()").value(3));
    }

    @Test
    @DisplayName("多设备数据处理流程测试")
    void testMultiDeviceDataFlow() throws Exception {
        // 1. 创建第二个测试设备
        String secondDeviceCode = generateDeviceCode();
        createAdditionalTestDevice(secondDeviceCode);

        // 2. 为两个设备分别上报数据
        // 设备1数据
        DeviceDataRequest device1Data = createTemperatureDataRequest(25.0);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(device1Data)))
                .andExpect(status().isOk());

        // 设备2数据
        DeviceDataRequest device2Data = createTemperatureDataRequest(30.0);
        device2Data.setDeviceCode(secondDeviceCode);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(device2Data)))
                .andExpect(status().isOk());

        // 3. 验证各设备数据独立存储
        assertEquals(1, getDeviceDataCount(TEST_DEVICE_CODE));
        assertEquals(1, getDeviceDataCount(secondDeviceCode));

        // 4. 分别查询各设备数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].dataValue").value(25.0));

        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", secondDeviceCode))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data[0].dataValue").value(30.0));
    }

    @Test
    @DisplayName("数据质量评估流程测试")
    void testDataQualityAssessmentFlow() throws Exception {
        // 1. 上报正常范围的数据
        DeviceDataRequest normalData = createTemperatureDataRequest(25.0);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(normalData)))
                .andExpect(status().isOk());

        // 2. 上报异常范围的数据
        DeviceDataRequest abnormalData = createTemperatureDataRequest(80.0); // 异常高温
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(abnormalData)))
                .andExpect(status().isOk());

        // 3. 查询数据并验证质量评估
        MvcResult result = mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("dataCode", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(2))
                .andReturn();

        // 验证数据质量字段存在
        String responseBody = result.getResponse().getContentAsString();
        assertTrue(responseBody.contains("quality"));
    }

    @Test
    @DisplayName("时间范围查询流程测试")
    void testTimeRangeQueryFlow() throws Exception {
        long now = System.currentTimeMillis();
        long oneHourAgo = now - 3600000;
        long twoHoursAgo = now - 7200000;

        // 1. 上报不同时间的数据
        DeviceDataRequest oldData = createTemperatureDataRequest(20.0);
        oldData.setReportTime(twoHoursAgo);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(oldData)))
                .andExpect(status().isOk());

        DeviceDataRequest recentData = createTemperatureDataRequest(25.0);
        recentData.setReportTime(oneHourAgo);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(recentData)))
                .andExpect(status().isOk());

        DeviceDataRequest currentData = createTemperatureDataRequest(30.0);
        currentData.setReportTime(now);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(currentData)))
                .andExpect(status().isOk());

        // 2. 查询最近1小时的数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("startTime", String.valueOf(oneHourAgo - 60000))
                        .param("endTime", String.valueOf(now + 60000)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(2)); // 最近的两条数据

        // 3. 查询全部数据
        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.length()").value(3)); // 全部三条数据
    }

    @Test
    @DisplayName("缓存机制验证流程测试")
    void testCachingMechanismFlow() throws Exception {
        // 1. 上报数据
        DeviceDataRequest dataRequest = createTemperatureDataRequest(25.5);
        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(dataRequest)))
                .andExpect(status().isOk());

        // 2. 验证缓存已更新
        String lastDataKey = "device:last_data:" + TEST_DEVICE_CODE;
        String statusKey = "device:status:" + TEST_DEVICE_CODE;

        assertTrue(existsInRedis(lastDataKey));
        assertTrue(existsInRedis(statusKey));

        // 3. 获取最新数据（应该从缓存获取）
        mockMvc.perform(get(API_BASE_PATH + "/device-data/latest")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data").isArray());

        // 4. 检查在线状态（应该从缓存获取）
        mockMvc.perform(get(API_BASE_PATH + "/device-data/online-status")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.online").value(true));
    }

    /**
     * 创建温度数据请求
     */
    private DeviceDataRequest createTemperatureDataRequest(double temperature) {
        DeviceDataRequest request = new DeviceDataRequest();
        request.setProjectId(TEST_PROJECT_ID);
        request.setDeviceCode(TEST_DEVICE_CODE);
        request.setDataCode((byte) 1); // 温度
        request.setDataValue(new java.math.BigDecimal(temperature));
        request.setQuality((byte) 1);
        request.setReportTime(System.currentTimeMillis());
        return request;
    }

    /**
     * 创建多类型数据批次
     */
    private BatchDataRequest createMultiTypeDataBatch() {
        BatchDataRequest batchRequest = new BatchDataRequest();
        batchRequest.setBatchId("e2e_batch_" + System.currentTimeMillis());
        batchRequest.setEdgeProgramId(TEST_EDGE_ID);
        batchRequest.setBatchTime(System.currentTimeMillis());

        long baseTime = System.currentTimeMillis();

        // 温度数据 (3条)
        DeviceDataRequest temp1 = createDataRequest((byte) 1, 24.0, baseTime - 300000);
        DeviceDataRequest temp2 = createDataRequest((byte) 1, 25.0, baseTime - 200000);
        DeviceDataRequest temp3 = createDataRequest((byte) 1, 26.0, baseTime - 100000);

        // 湿度数据 (2条)
        DeviceDataRequest humidity1 = createDataRequest((byte) 2, 55.0, baseTime - 150000);
        DeviceDataRequest humidity2 = createDataRequest((byte) 2, 60.0, baseTime - 50000);

        // PM2.5数据 (1条)
        DeviceDataRequest pm25 = createDataRequest((byte) 3, 35.0, baseTime);

        batchRequest.setDataList(Arrays.asList(temp1, temp2, temp3, humidity1, humidity2, pm25));
        return batchRequest;
    }

    /**
     * 创建数据请求
     */
    private DeviceDataRequest createDataRequest(byte dataCode, double value, long timestamp) {
        DeviceDataRequest request = new DeviceDataRequest();
        request.setProjectId(TEST_PROJECT_ID);
        request.setDeviceCode(TEST_DEVICE_CODE);
        request.setDataCode(dataCode);
        request.setDataValue(new java.math.BigDecimal(value));
        request.setQuality((byte) 1);
        request.setReportTime(timestamp);
        return request;
    }

    /**
     * 创建额外的测试设备
     */
    private void createAdditionalTestDevice(String deviceCode) {
        String sql = """
            INSERT INTO device (project_id, device_code, device_name, device_type, vendor, model,
                               location, status, edge_program_id, description, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
            """;

        jdbcTemplate.update(sql,
                TEST_PROJECT_ID,
                deviceCode,
                "额外测试设备",
                1, // SENSOR
                1, // YEASTAR
                "TEST-MODEL-002",
                "测试位置2",
                1, // ACTIVE
                TEST_EDGE_ID,
                "用于多设备测试的设备");
    }
}
