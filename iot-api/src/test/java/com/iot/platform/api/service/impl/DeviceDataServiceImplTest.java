package com.iot.platform.api.service.impl;

import com.iot.platform.api.BaseTest;
import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.api.dto.DeviceDataRequest;
import com.iot.platform.api.service.DeviceService;
import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.iot.platform.common.enums.DeviceType;
import com.iot.platform.common.enums.DeviceVendor;
import com.iot.platform.common.response.Result;
import com.iot.platform.common.response.ResultCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 设备数据服务测试类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@DisplayName("设备数据服务测试")
class DeviceDataServiceImplTest extends BaseTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    private DeviceService deviceService;

    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private DeviceDataServiceImpl deviceDataService;

    private Device testDevice;
    private DeviceDataRequest testDataRequest;

    @BeforeEach
    void setUp() {
        // 设置Redis模拟
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        // 创建测试设备
        testDevice = createTestDevice();

        // 创建测试数据请求
        testDataRequest = createTestDataRequest();
    }

    @Test
    @DisplayName("处理设备数据 - 成功")
    void testProcessDeviceData_Success() {
        // 模拟设备存在
        when(deviceService.getByDeviceCode(TEST_DEVICE_CODE)).thenReturn(testDevice);

        // 模拟数据库操作成功
        when(jdbcTemplate.update(anyString(), any(Object[].class))).thenReturn(1);

        // 模拟设备服务更新成功
        when(deviceService.updateLastReportTime(anyString(), anyLong())).thenReturn(true);

        Result<Void> result = deviceDataService.processDeviceData(testDataRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(ResultCode.SUCCESS.getCode(), result.getCode());

        // 验证方法调用
        verify(deviceService).getByDeviceCode(TEST_DEVICE_CODE);
        verify(deviceService).updateLastReportTime(eq(TEST_DEVICE_CODE), anyLong());
        verify(valueOperations, times(2)).set(anyString(), any(), anyLong(), any());
    }

    @Test
    @DisplayName("处理设备数据 - 设备不存在")
    void testProcessDeviceData_DeviceNotFound() {
        // 模拟设备不存在
        when(deviceService.getByDeviceCode(TEST_DEVICE_CODE)).thenReturn(null);

        Result<Void> result = deviceDataService.processDeviceData(testDataRequest);

        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(ResultCode.NOT_FOUND.getCode(), result.getCode());
        assertEquals("设备不存在", result.getMessage());

        // 验证没有进行数据库操作
        verify(jdbcTemplate, never()).update(anyString(), any(Object[].class));
    }

    @Test
    @DisplayName("处理设备数据 - 数据验证失败")
    void testProcessDeviceData_ValidationFailed() {
        // 创建无效的数据请求
        DeviceDataRequest invalidRequest = new DeviceDataRequest();
        invalidRequest.setDeviceCode(""); // 空设备编码
        invalidRequest.setDataCode((byte) 1);
        invalidRequest.setDataValue(new java.math.BigDecimal(25.5));
        invalidRequest.setReportTime(System.currentTimeMillis());

        Result<Void> result = deviceDataService.processDeviceData(invalidRequest);

        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals(ResultCode.PARAM_ERROR.getCode(), result.getCode());
        assertEquals("数据格式验证失败", result.getMessage());
    }

    @Test
    @DisplayName("批量处理设备数据 - 成功")
    void testProcessBatchDeviceData_Success() {
        BatchDataRequest batchRequest = createBatchDataRequest();

        // 模拟设备存在
        when(deviceService.getByDeviceCode(TEST_DEVICE_CODE)).thenReturn(testDevice);

        // 模拟数据库批量操作成功
        when(jdbcTemplate.batchUpdate(anyString(), anyList())).thenReturn(new int[]{1, 1});

        Result<Map<String, Object>> result = deviceDataService.processBatchDeviceData(batchRequest);

        assertNotNull(result);
        assertTrue(result.isSuccess());

        Map<String, Object> resultData = result.getData();
        assertNotNull(resultData);
        assertEquals(2, resultData.get("totalCount"));
        assertEquals(1L, resultData.get("deviceCount"));
        assertTrue((Integer) resultData.get("successCount") > 0);
    }

    @Test
    @DisplayName("验证设备数据 - 有效数据")
    void testValidateDeviceData_ValidData() {
        boolean isValid = deviceDataService.validateDeviceData(testDataRequest);
        assertTrue(isValid);
    }

    @Test
    @DisplayName("验证设备数据 - 无效数据")
    void testValidateDeviceData_InvalidData() {
        // 测试空请求
        assertFalse(deviceDataService.validateDeviceData(null));

        // 测试空设备编码
        DeviceDataRequest invalidRequest1 = new DeviceDataRequest();
        invalidRequest1.setDeviceCode("");
        assertFalse(deviceDataService.validateDeviceData(invalidRequest1));

        // 测试空数据值
        DeviceDataRequest invalidRequest2 = new DeviceDataRequest();
        invalidRequest2.setDeviceCode(TEST_DEVICE_CODE);
        invalidRequest2.setDataCode((byte) 1);
        invalidRequest2.setDataValue(null);
        assertFalse(deviceDataService.validateDeviceData(invalidRequest2));

        // 测试未来时间戳
        DeviceDataRequest invalidRequest3 = new DeviceDataRequest();
        invalidRequest3.setDeviceCode(TEST_DEVICE_CODE);
        invalidRequest3.setDataCode((byte) 1);
        invalidRequest3.setDataValue(new java.math.BigDecimal(25.5));
        invalidRequest3.setReportTime(System.currentTimeMillis() + 10 * 60 * 1000); // 10分钟后
        assertFalse(deviceDataService.validateDeviceData(invalidRequest3));
    }

    @Test
    @DisplayName("数据质量评估 - 正常范围")
    void testEvaluateDataQuality_NormalRange() {
        // 温度数据在正常范围内
        byte quality = deviceDataService.evaluateDataQuality(new java.math.BigDecimal("25.0"), (byte) 1, TEST_DEVICE_CODE);
        assertEquals(1, quality); // 优秀

        // 湿度数据在正常范围内
        quality = deviceDataService.evaluateDataQuality(new java.math.BigDecimal("50.0"), (byte) 2, TEST_DEVICE_CODE);
        assertEquals(1, quality); // 优秀
    }

    @Test
    @DisplayName("数据质量评估 - 异常范围")
    void testEvaluateDataQuality_AbnormalRange() {
        // 温度数据超出正常范围
        byte quality = deviceDataService.evaluateDataQuality(new java.math.BigDecimal("80.0"), (byte) 1, TEST_DEVICE_CODE);
        assertEquals(3, quality); // 一般

        // PM2.5数据超出良好范围
        quality = deviceDataService.evaluateDataQuality(new java.math.BigDecimal("100.0"), (byte) 3, TEST_DEVICE_CODE);
        assertEquals(3, quality); // 一般
    }

    @Test
    @DisplayName("数据质量评估 - 空值处理")
    void testEvaluateDataQuality_NullValues() {
        byte quality = deviceDataService.evaluateDataQuality(null, (byte) 1, TEST_DEVICE_CODE);
        assertEquals(4, quality); // 较差

        quality = deviceDataService.evaluateDataQuality(new java.math.BigDecimal("25.0"), null, TEST_DEVICE_CODE);
        assertEquals(4, quality); // 较差
    }

    @Test
    @DisplayName("检查设备在线状态 - 在线")
    void testIsDeviceOnline_Online() {
        // 模拟Redis返回在线状态
        when(valueOperations.get(anyString())).thenReturn("online");

        boolean isOnline = deviceDataService.isDeviceOnline(TEST_DEVICE_CODE, 10);
        assertTrue(isOnline);
    }

    @Test
    @DisplayName("检查设备在线状态 - 离线")
    void testIsDeviceOnline_Offline() {
        // 模拟Redis返回空值或其他状态
        when(valueOperations.get(anyString())).thenReturn(null);

        boolean isOnline = deviceDataService.isDeviceOnline(TEST_DEVICE_CODE, 10);
        assertFalse(isOnline);
    }

    @Test
    @DisplayName("更新设备最后上报时间")
    void testUpdateDeviceLastReportTime() {
        long reportTime = System.currentTimeMillis();

        // 模拟设备服务更新成功
        when(deviceService.updateLastReportTime(TEST_DEVICE_CODE, reportTime)).thenReturn(true);

        deviceDataService.updateDeviceLastReportTime(TEST_DEVICE_CODE, reportTime);

        verify(deviceService).updateLastReportTime(TEST_DEVICE_CODE, reportTime);
    }

    /**
     * 创建测试设备
     */
    private Device createTestDevice() {
        Device device = new Device();
        device.setId(1L);
        device.setProjectId(TEST_PROJECT_ID);
        device.setDeviceCode(TEST_DEVICE_CODE);
        device.setDeviceName("测试设备");
        device.setDeviceType(DeviceType.SENSOR.getCode());
        device.setVendor(DeviceVendor.YEASTAR.getCode());
        device.setModel("TEST-MODEL-001");
        device.setLocation("测试位置");
        device.setStatus(DeviceStatus.ACTIVE.getCode());
        device.setEdgeProgramId("edge_test_001");
        device.setDescription("测试设备描述");
        return device;
    }

    /**
     * 创建测试数据请求
     */
    private DeviceDataRequest createTestDataRequest() {
        DeviceDataRequest request = new DeviceDataRequest();
        request.setDeviceCode(TEST_DEVICE_CODE);
        request.setDataCode((byte) 1); // 温度
        request.setDataValue(new java.math.BigDecimal(25.5));
        request.setReportTime(System.currentTimeMillis());
        request.setProjectId(TEST_PROJECT_ID);
        request.setEdgeProgramId("edge_test_001");
        request.setQuality((byte) 1);
        return request;
    }

    /**
     * 创建批量数据请求
     */
    private BatchDataRequest createBatchDataRequest() {
        BatchDataRequest batchRequest = new BatchDataRequest();

        DeviceDataRequest request1 = createTestDataRequest();
        request1.setDataValue(new java.math.BigDecimal(25.0));
        request1.setReportTime(System.currentTimeMillis() - 60000);

        DeviceDataRequest request2 = createTestDataRequest();
        request2.setDataValue(new java.math.BigDecimal(26.0));
        request2.setReportTime(System.currentTimeMillis());

        batchRequest.setDataList(Arrays.asList(request1, request2));
        batchRequest.setBatchId("batch_test_001");
        batchRequest.setEdgeProgramId("edge_test_001");
        batchRequest.setBatchTime(System.currentTimeMillis());

        return batchRequest;
    }
}
