package com.iot.platform.api.integration;

import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.api.dto.DeviceDataRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Arrays;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 设备数据API集成测试
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@DisplayName("设备数据API集成测试")
class DeviceDataIntegrationTest extends BaseIntegrationTest {

    @Test
    @DisplayName("上报单条设备数据 - 成功")
    void testReportDeviceData_Success() throws Exception {
        DeviceDataRequest request = createDeviceDataRequest();

        MvcResult result = mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        String responseBody = result.getResponse().getContentAsString();
        assertSuccessResponse(responseBody);

        // 验证数据已保存到数据库
        int dataCount = getDeviceDataCount(TEST_DEVICE_CODE);
        assertEquals(1, dataCount);

        // 验证缓存已更新
        String cacheKey = "device:last_data:" + TEST_DEVICE_CODE;
        assertTrue(existsInRedis(cacheKey));
    }

    @Test
    @DisplayName("上报单条设备数据 - 设备不存在")
    void testReportDeviceData_DeviceNotFound() throws Exception {
        DeviceDataRequest request = createDeviceDataRequest();
        request.setDeviceCode("NON_EXISTENT_DEVICE");

        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(request)))
                .andExpect(status().isNotFound())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(404))
                .andExpect(jsonPath("$.message").value("设备不存在"));

        // 验证数据未保存
        int dataCount = getDeviceDataCount("NON_EXISTENT_DEVICE");
        assertEquals(0, dataCount);
    }

    @Test
    @DisplayName("上报单条设备数据 - 参数验证失败")
    void testReportDeviceData_ValidationFailed() throws Exception {
        DeviceDataRequest request = new DeviceDataRequest();
        // 缺少必要字段

        mockMvc.perform(post(API_BASE_PATH + "/device-data/report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.code").value(400));
    }

    @Test
    @DisplayName("批量上报设备数据 - 成功")
    void testReportBatchDeviceData_Success() throws Exception {
        BatchDataRequest batchRequest = createBatchDeviceDataRequest();

        MvcResult result = mockMvc.perform(post(API_BASE_PATH + "/device-data/batch-report")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(toJson(batchRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.totalCount").value(3))
                .andExpect(jsonPath("$.data.successCount").exists())
                .andReturn();

        String responseBody = result.getResponse().getContentAsString();
        assertSuccessResponse(responseBody);

        // 验证数据已保存到数据库
        int dataCount = getDeviceDataCount(TEST_DEVICE_CODE);
        assertEquals(3, dataCount);
    }

    @Test
    @DisplayName("查询设备数据 - 成功")
    void testQueryDeviceData_Success() throws Exception {
        // 先创建一些测试数据
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 1, 25.5);
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 1, 26.0);
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 2, 60.0);

        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("dataCode", "1")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2)); // 只有dataCode=1的数据
    }

    @Test
    @DisplayName("查询设备数据 - 按时间范围")
    void testQueryDeviceDataByTimeRange_Success() throws Exception {
        // 创建不同时间的测试数据
        long now = System.currentTimeMillis();
        long oneHourAgo = now - 3600000; // 1小时前
        long twoHoursAgo = now - 7200000; // 2小时前

        // 插入测试数据（需要直接操作数据库以控制时间戳）
        String sql = """
            INSERT INTO device_data (project_id, device_code, data_code, data_value, quality,
                                   timestamp, edge_program_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
            """;

        jdbcTemplate.update(sql, TEST_PROJECT_ID, TEST_DEVICE_CODE, (byte) 1, 25.0, (byte) 1, twoHoursAgo, TEST_EDGE_ID);
        jdbcTemplate.update(sql, TEST_PROJECT_ID, TEST_DEVICE_CODE, (byte) 1, 26.0, (byte) 1, oneHourAgo, TEST_EDGE_ID);
        jdbcTemplate.update(sql, TEST_PROJECT_ID, TEST_DEVICE_CODE, (byte) 1, 27.0, (byte) 1, now, TEST_EDGE_ID);

        mockMvc.perform(get(API_BASE_PATH + "/device-data/query")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("startTime", String.valueOf(oneHourAgo - 60000)) // 稍微提前一点
                        .param("endTime", String.valueOf(now + 60000)) // 稍微延后一点
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2)); // 最近1小时的数据
    }

    @Test
    @DisplayName("获取设备最新数据 - 成功")
    void testGetLatestDeviceData_Success() throws Exception {
        // 创建测试数据
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 1, 25.5);
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 2, 60.0);

        mockMvc.perform(get(API_BASE_PATH + "/device-data/latest")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data.length()").value(2)); // 每种数据类型的最新数据
    }

    @Test
    @DisplayName("获取设备统计信息 - 成功")
    void testGetDeviceStatistics_Success() throws Exception {
        // 创建测试数据
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 1, 25.0);
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 1, 26.0);
        createTestDeviceData(TEST_DEVICE_CODE, (byte) 1, 27.0);

        long endTime = System.currentTimeMillis();
        long startTime = endTime - 3600000; // 1小时前

        mockMvc.perform(get(API_BASE_PATH + "/device-data/statistics")
                        .param("deviceCode", TEST_DEVICE_CODE)
                        .param("startTime", String.valueOf(startTime))
                        .param("endTime", String.valueOf(endTime)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.deviceCode").value(TEST_DEVICE_CODE))
                .andExpect(jsonPath("$.data.totalCount").exists())
                .andExpect(jsonPath("$.data.dataTypes").exists());
    }

    @Test
    @DisplayName("检查设备在线状态 - 在线")
    void testCheckDeviceOnlineStatus_Online() throws Exception {
        // 设置设备在线状态
        String statusKey = "device:status:" + TEST_DEVICE_CODE;
        setToRedis(statusKey, "online");

        mockMvc.perform(get(API_BASE_PATH + "/device-data/online-status")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.deviceCode").value(TEST_DEVICE_CODE))
                .andExpect(jsonPath("$.data.online").value(true));
    }

    @Test
    @DisplayName("检查设备在线状态 - 离线")
    void testCheckDeviceOnlineStatus_Offline() throws Exception {
        mockMvc.perform(get(API_BASE_PATH + "/device-data/online-status")
                        .param("deviceCode", TEST_DEVICE_CODE))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.deviceCode").value(TEST_DEVICE_CODE))
                .andExpect(jsonPath("$.data.online").value(false));
    }

    /**
     * 创建设备数据请求
     */
    private DeviceDataRequest createDeviceDataRequest() {
        DeviceDataRequest request = new DeviceDataRequest();
        request.setProjectId(TEST_PROJECT_ID);
        request.setDeviceCode(TEST_DEVICE_CODE);
        request.setDataCode((byte) 1); // 温度
        request.setDataValue(new java.math.BigDecimal(25.5));
        request.setQuality((byte) 1);
        request.setReportTime(System.currentTimeMillis());
        request.setEdgeProgramId(TEST_EDGE_ID);
        return request;
    }

    /**
     * 创建批量设备数据请求
     */
    private BatchDataRequest createBatchDeviceDataRequest() {
        BatchDataRequest batchRequest = new BatchDataRequest();
        batchRequest.setBatchId("batch_test_" + System.currentTimeMillis());
        batchRequest.setEdgeProgramId(TEST_EDGE_ID);
        batchRequest.setBatchTime(System.currentTimeMillis());

        // 创建多条数据
        DeviceDataRequest data1 = createDeviceDataRequest();
        data1.setDataValue(new java.math.BigDecimal(25.0));
        data1.setReportTime(System.currentTimeMillis() - 120000); // 2分钟前

        DeviceDataRequest data2 = createDeviceDataRequest();
        data2.setDataValue(new java.math.BigDecimal(26.0));
        data2.setReportTime(System.currentTimeMillis() - 60000); // 1分钟前

        DeviceDataRequest data3 = createDeviceDataRequest();
        data3.setDataCode((byte) 2); // 湿度
        data3.setDataValue(new java.math.BigDecimal(60.0));
        data3.setReportTime(System.currentTimeMillis());

        batchRequest.setDataList(Arrays.asList(data1, data2, data3));
        return batchRequest;
    }
}
