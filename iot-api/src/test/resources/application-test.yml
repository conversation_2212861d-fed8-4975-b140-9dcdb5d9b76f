# IoT大数据平台 - 测试环境配置
# 用于单元测试和集成测试

server:
  port: 8000

spring:
  profiles:
    active: test

  # 数据源配置 - 使用H2内存数据库进行测试
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password:
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 300000
      max-lifetime: 900000

  # H2数据库控制台（仅测试环境）
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect

  # Redis配置 - 使用嵌入式Redis进行测试
  data:
    redis:
      host: localhost
      port: 6379
      database: 15  # 使用独立的测试数据库
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 5
          max-idle: 3
          min-idle: 1
          max-wait: 3000ms

  # 消息队列配置 - 测试环境禁用
  integration:
    mqtt:
      enabled: false

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/*.xml

# 日志配置
logging:
  level:
    com.iot.platform: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security: DEBUG
    org.mybatis: DEBUG
    com.baomidou.mybatisplus: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# IoT平台配置
iot:
  # AK/SK认证配置
  auth:
    ak-sk:
      enabled: true
      signature-timeout: 300  # 签名有效期（秒）
      cache-size: 1000       # 缓存大小

  # 数据源配置
  datasource:
    master:
      url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
      username: sa
      password:
    pool:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 300000
      max-lifetime: 900000

  # MQTT配置 - 测试环境禁用
  mqtt:
    broker-url: tcp://localhost:1883
    client-id-prefix: iot-test
    username: test_user
    password: test_pass
    connection-timeout: 10
    keep-alive-interval: 30
    qos: 1

  # 调度配置
  scheduling:
    core-pool-size: 2
    max-pool-size: 5
    queue-capacity: 50
    keep-alive-seconds: 30
    thread-name-prefix: Test-Scheduler-
    await-termination-seconds: 30

  # 数据处理配置
  data:
    batch-size: 100          # 批处理大小
    max-retry-times: 3       # 最大重试次数
    process-timeout: 30      # 处理超时时间（秒）

  # 缓存配置
  cache:
    device-ttl: 300          # 设备缓存TTL（秒）
    result-ttl: 180          # 结果缓存TTL（秒）
    max-size: 1000           # 最大缓存条数

# 测试专用配置
test:
  # 数据库初始化
  database:
    init-schema: true
    init-data: true

  # Mock配置
  mock:
    enabled: true
    delay: 0  # Mock延迟（毫秒）

  # 性能测试配置
  performance:
    concurrent-users: 10
    test-duration: 60  # 测试持续时间（秒）
    ramp-up-time: 10   # 启动时间（秒）
