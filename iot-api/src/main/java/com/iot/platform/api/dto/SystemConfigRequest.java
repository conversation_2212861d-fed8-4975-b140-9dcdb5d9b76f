package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 系统配置请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Data
@Schema(name = "SystemConfigRequest", description = "系统配置请求")
public class SystemConfigRequest {

    @Schema(description = "项目ID（为空表示全局配置）", example = "demo_project_001")
    @Size(max = 50, message = "项目ID长度不能超过50个字符")
    private String projectId;

    @Schema(description = "配置分类", example = "system", allowableValues = {"system", "cache", "database", "mqtt", "algorithm"})
    @NotBlank(message = "配置分类不能为空")
    @Size(max = 50, message = "配置分类长度不能超过50个字符")
    private String category;

    @Schema(description = "配置键", example = "data_retention_days")
    @NotBlank(message = "配置键不能为空")
    @Size(max = 100, message = "配置键长度不能超过100个字符")
    private String configKey;

    @Schema(description = "配置值", example = "30")
    @NotBlank(message = "配置值不能为空")
    private String configValue;

    @Schema(description = "配置描述", example = "数据保留天数")
    @Size(max = 500, message = "配置描述长度不能超过500个字符")
    private String description;

    @Schema(description = "是否可编辑", example = "1", allowableValues = {"0", "1"})
    private Byte editable = 1;
}
