package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.DeviceGroup;
import com.iot.platform.common.mapper.DeviceGroupMapper;
import com.iot.platform.api.service.DeviceGroupService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备分组表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class DeviceGroupServiceImpl extends ServiceImpl<DeviceGroupMapper, DeviceGroup> implements DeviceGroupService {

}
