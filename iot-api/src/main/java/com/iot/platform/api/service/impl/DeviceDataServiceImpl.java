package com.iot.platform.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.api.dto.DeviceDataRequest;
import com.iot.platform.api.service.DeviceDataService;
import com.iot.platform.api.service.DeviceService;
import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.iot.platform.common.response.Result;
import com.iot.platform.common.response.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 设备数据处理服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceDataServiceImpl implements DeviceDataService {

    private final JdbcTemplate jdbcTemplate;
    private final RedisTemplate<String, Object> redisTemplate;
    private final DeviceService deviceService;

    private static final String DEVICE_CACHE_PREFIX = "device:data:";
    private static final String DEVICE_STATUS_PREFIX = "device:status:";
    private static final int CACHE_TTL_HOURS = 24;
    private static final int BATCH_SIZE = 1000;

    @Override
    @Transactional
    public Result<Void> processDeviceData(DeviceDataRequest request) {
        try {
            // 1. 验证数据格式
            if (!validateDeviceData(request)) {
                return Result.error(ResultCode.PARAM_ERROR, "数据格式验证失败");
            }

            // 2. 验证设备是否存在
            Device device = deviceService.getByDeviceCode(request.getDeviceCode());
            if (device == null) {
                log.warn("设备不存在: {}", request.getDeviceCode());
                return Result.error(ResultCode.NOT_FOUND, "设备不存在");
            }

            // 3. 数据质量评估
            byte quality = evaluateDataQuality(request.getDataValue(), request.getDataCode(), request.getDeviceCode());
            request.setQuality(quality);

            // 4. 存储数据到数据库
            saveDeviceDataToDatabase(request, device);

            // 5. 更新缓存
            updateDeviceDataCache(request);

            // 6. 更新设备最后上报时间
            updateDeviceLastReportTime(request.getDeviceCode(), request.getTimestamp());

            log.debug("设备数据处理成功: device={}, dataCode={}, value={}",
                    request.getDeviceCode(), request.getDataCode(), request.getDataValue());

            return Result.success();

        } catch (Exception e) {
            log.error("设备数据处理失败: device={}, error={}", request.getDeviceCode(), e.getMessage(), e);
            return Result.error(ResultCode.INTERNAL_ERROR, "数据处理失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public Result<Map<String, Object>> processBatchDeviceData(BatchDataRequest request) {
        Map<String, Object> result = new HashMap<>();
        int successCount = 0;
        int failureCount = 0;
        List<String> errors = new ArrayList<>();

        try {
            // 1. 验证批次数据一致性
            if (!request.validateBatchConsistency()) {
                return Result.error(ResultCode.PARAM_ERROR, "批次数据一致性验证失败");
            }

            // 2. 按设备分组处理
            Map<String, List<DeviceDataRequest>> deviceGroups = request.groupByDevice();

            for (Map.Entry<String, List<DeviceDataRequest>> entry : deviceGroups.entrySet()) {
                String deviceCode = entry.getKey();
                List<DeviceDataRequest> deviceDataList = entry.getValue();

                try {
                    // 验证设备存在性
                    Device device = deviceService.getByDeviceCode(deviceCode);
                    if (device == null) {
                        failureCount += deviceDataList.size();
                        errors.add("设备不存在: " + deviceCode);
                        continue;
                    }

                    // 批量处理该设备的数据
                    int deviceSuccessCount = processBatchDeviceDataForDevice(deviceDataList, device);
                    successCount += deviceSuccessCount;
                    failureCount += (deviceDataList.size() - deviceSuccessCount);

                } catch (Exception e) {
                    log.error("处理设备{}的批量数据失败: {}", deviceCode, e.getMessage(), e);
                    failureCount += deviceDataList.size();
                    errors.add("设备" + deviceCode + "处理失败: " + e.getMessage());
                }
            }

            // 3. 构建返回结果
            result.put("totalCount", request.getDataCount());
            result.put("successCount", successCount);
            result.put("failureCount", failureCount);
            result.put("deviceCount", request.getDeviceCount());
            result.put("batchId", request.getBatchId());
            result.put("processTime", System.currentTimeMillis());

            if (!errors.isEmpty()) {
                result.put("errors", errors);
            }

            log.info("批量数据处理完成: total={}, success={}, failure={}, devices={}",
                    request.getDataCount(), successCount, failureCount, request.getDeviceCount());

            return Result.success(result);

        } catch (Exception e) {
            log.error("批量数据处理失败: {}", e.getMessage(), e);
            result.put("error", e.getMessage());
            return Result.error(ResultCode.INTERNAL_ERROR, "批量数据处理失败", result);
        }
    }

    @Override
    public boolean validateDeviceData(DeviceDataRequest request) {
        if (request == null) {
            return false;
        }

        // 基本字段验证
        if (request.getDeviceCode() == null || request.getDeviceCode().trim().isEmpty()) {
            return false;
        }

        if (request.getDataCode() == null || request.getDataValue() == null || request.getTimestamp() == null) {
            return false;
        }

        // 数据值范围验证
        if (!request.isDataValueValid()) {
            log.warn("数据值超出合理范围: device={}, dataCode={}, value={}",
                    request.getDeviceCode(), request.getDataCode(), request.getDataValue());
            return false;
        }

        // 时间戳合理性验证（不能是未来时间，不能太久远）
        long currentTime = System.currentTimeMillis();
        long dataTime = request.getTimestamp();

        if (dataTime > currentTime + 5 * 60 * 1000) { // 不能超过当前时间5分钟
            log.warn("数据时间戳异常（未来时间）: device={}, timestamp={}", request.getDeviceCode(), dataTime);
            return false;
        }

        if (currentTime - dataTime > 7 * 24 * 60 * 60 * 1000L) { // 不能超过7天前
            log.warn("数据时间戳异常（过于久远）: device={}, timestamp={}", request.getDeviceCode(), dataTime);
            return false;
        }

        return true;
    }

    @Override
    public byte evaluateDataQuality(BigDecimal dataValue, Byte dataCode, String deviceCode) {
        if (dataValue == null || dataCode == null) {
            return 4; // 较差
        }

        // 基于数据值的质量评估
        boolean isInNormalRange = switch (dataCode) {
            case 1 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(new BigDecimal("50")) <= 0;    // 温度正常范围
            case 2 -> dataValue.compareTo(new BigDecimal("20")) >= 0 && dataValue.compareTo(new BigDecimal("80")) <= 0;   // 湿度正常范围
            case 3 -> dataValue.compareTo(new BigDecimal("75")) <= 0;                        // PM2.5良好范围
            case 4 -> dataValue.compareTo(new BigDecimal("1000")) <= 0;                      // CO2正常范围
            case 6 -> dataValue.compareTo(new BigDecimal("100")) >= 0 && dataValue.compareTo(new BigDecimal("1000")) <= 0; // 光照正常范围
            case 8 -> dataValue.compareTo(new BigDecimal("60")) <= 0;                        // 噪音正常范围
            default -> true;
        };

        // 检查设备历史数据的稳定性（简化版本）
        boolean isStable = checkDataStability(deviceCode, dataCode, dataValue);

        if (isInNormalRange && isStable) {
            return 1; // 优秀
        } else if (isInNormalRange || isStable) {
            return 2; // 良好
        } else {
            return 3; // 一般
        }
    }

    /**
     * 检查数据稳定性
     */
    private boolean checkDataStability(String deviceCode, Byte dataCode, BigDecimal currentValue) {
        try {
            String cacheKey = DEVICE_CACHE_PREFIX + deviceCode + ":" + dataCode + ":latest";
            Object cachedValue = redisTemplate.opsForValue().get(cacheKey);

            if (cachedValue != null) {
                BigDecimal lastValue = new BigDecimal(cachedValue.toString());
                BigDecimal diff = currentValue.subtract(lastValue).abs();
                BigDecimal maxValue = lastValue.abs().max(BigDecimal.ONE);
                BigDecimal changeRate = diff.divide(maxValue, 4, BigDecimal.ROUND_HALF_UP);

                // 变化率超过50%认为不稳定
                return changeRate.compareTo(new BigDecimal("0.5")) <= 0;
            }

            return true; // 没有历史数据时认为稳定
        } catch (Exception e) {
            log.warn("检查数据稳定性失败: device={}, dataCode={}", deviceCode, dataCode, e);
            return true;
        }
    }

    /**
     * 保存设备数据到数据库
     */
    private void saveDeviceDataToDatabase(DeviceDataRequest request, Device device) {
        String tableName = getDataTableName(request.getTimestamp());

        String sql = String.format("""
            INSERT INTO %s (project_id, device_code, edge_program_id,
                           data_code, data_value, quality, report_time, ext_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, tableName);

        jdbcTemplate.update(sql,
                device.getProjectId(),
                request.getDeviceCode(),
                request.getEdgeProgramId(),
                request.getDataCode(),
                request.getDataValue(),
                request.getQuality(),
                request.getTimestamp(),
                request.getExtData()
        );
    }

    /**
     * 更新设备数据缓存
     */
    private void updateDeviceDataCache(DeviceDataRequest request) {
        try {
            String latestKey = DEVICE_CACHE_PREFIX + request.getDeviceCode() + ":" + request.getDataCode() + ":latest";
            redisTemplate.opsForValue().set(latestKey, request.getDataValue(), CACHE_TTL_HOURS, TimeUnit.HOURS);

            String statusKey = DEVICE_STATUS_PREFIX + request.getDeviceCode();
            redisTemplate.opsForValue().set(statusKey, "online", CACHE_TTL_HOURS, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("更新设备数据缓存失败: device={}", request.getDeviceCode(), e);
        }
    }

    /**
     * 批量处理单个设备的数据
     */
    private int processBatchDeviceDataForDevice(List<DeviceDataRequest> dataList, Device device) {
        int successCount = 0;

        // 按时间排序
        dataList.sort(Comparator.comparing(DeviceDataRequest::getTimestamp));

        // 分批处理
        for (int i = 0; i < dataList.size(); i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, dataList.size());
            List<DeviceDataRequest> batch = dataList.subList(i, endIndex);

            try {
                successCount += saveBatchDataToDatabase(batch, device);
            } catch (Exception e) {
                log.error("批量保存数据失败: device={}, batch size={}", device.getDeviceCode(), batch.size(), e);
            }
        }

        // 更新设备最后上报时间
        if (!dataList.isEmpty()) {
            long lastReportTime = dataList.get(dataList.size() - 1).getTimestamp();
            updateDeviceLastReportTime(device.getDeviceCode(), lastReportTime);
        }

        return successCount;
    }

    /**
     * 批量保存数据到数据库
     */
    private int saveBatchDataToDatabase(List<DeviceDataRequest> dataList, Device device) {
        if (dataList.isEmpty()) {
            return 0;
        }

        // 按月份分组
        Map<String, List<DeviceDataRequest>> monthlyGroups = dataList.stream()
                .collect(Collectors.groupingBy(data -> getDataTableName(data.getTimestamp())));

        int totalSaved = 0;
        for (Map.Entry<String, List<DeviceDataRequest>> entry : monthlyGroups.entrySet()) {
            String tableName = entry.getKey();
            List<DeviceDataRequest> monthlyData = entry.getValue();

            totalSaved += saveBatchToTable(tableName, monthlyData, device);
        }

        return totalSaved;
    }

    /**
     * 保存批量数据到指定表
     */
    private int saveBatchToTable(String tableName, List<DeviceDataRequest> dataList, Device device) {
        String sql = String.format("""
            INSERT INTO %s (project_id, device_code, edge_program_id,
                           data_code, data_value, quality, report_time, ext_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """, tableName);

        List<Object[]> batchArgs = dataList.stream()
                .map(data -> new Object[]{
                        device.getProjectId(),
                        data.getDeviceCode(),
                        data.getEdgeProgramId(),
                        data.getDataCode(),
                        data.getDataValue(),
                        evaluateDataQuality(data.getDataValue(), data.getDataCode(), data.getDeviceCode()),
                        data.getTimestamp(),
                        data.getExtData()
                })
                .collect(Collectors.toList());

        int[] results = jdbcTemplate.batchUpdate(sql, batchArgs);
        return Arrays.stream(results).sum();
    }

    /**
     * 根据时间戳获取数据表名
     */
    private String getDataTableName(Long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timestamp),
                java.time.ZoneId.systemDefault()
        );
        return "iot_data_" + dateTime.format(DateTimeFormatter.ofPattern("yyyyMM"));
    }

    @Override
    public List<Map<String, Object>> getLatestDeviceData(String deviceCode, Byte dataCode) {
        // 实现获取最新数据的逻辑
        // 这里先返回空列表，后续完善
        return new ArrayList<>();
    }

    @Override
    public List<Map<String, Object>> getDeviceHistoryData(String deviceCode, Byte dataCode,
                                                          Long startTime, Long endTime, Integer limit) {
        // 实现获取历史数据的逻辑
        // 这里先返回空列表，后续完善
        return new ArrayList<>();
    }

    @Override
    public Map<String, Object> getDeviceDataStatistics(String deviceCode, Byte dataCode, Integer timeRange) {
        // 实现获取统计信息的逻辑
        // 这里先返回空Map，后续完善
        return new HashMap<>();
    }

    @Override
    public long cleanExpiredData(int retentionDays) {
        // 实现清理过期数据的逻辑
        // 这里先返回0，后续完善
        return 0;
    }

    @Override
    public void updateDeviceLastReportTime(String deviceCode, Long reportTime) {
        try {
            deviceService.updateLastReportTime(deviceCode, reportTime);
        } catch (Exception e) {
            log.warn("更新设备最后上报时间失败: device={}, time={}", deviceCode, reportTime, e);
        }
    }

    @Override
    public boolean isDeviceOnline(String deviceCode, int timeoutMinutes) {
        try {
            String statusKey = DEVICE_STATUS_PREFIX + deviceCode;
            Object status = redisTemplate.opsForValue().get(statusKey);
            return "online".equals(status);
        } catch (Exception e) {
            log.warn("检查设备在线状态失败: device={}", deviceCode, e);
            return false;
        }
    }
}
