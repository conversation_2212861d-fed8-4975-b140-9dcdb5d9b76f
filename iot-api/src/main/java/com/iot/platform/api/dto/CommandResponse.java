package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 设备控制指令响应DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Schema(description = "设备控制指令响应")
public class CommandResponse {

    @Schema(description = "指令ID", example = "cmd_20241216_001")
    private String commandId;

    @Schema(description = "设备编码", example = "DEVICE_001")
    private String deviceCode;

    @Schema(description = "指令类型", example = "SET_TEMPERATURE")
    private String commandType;

    @Schema(description = "指令状态", example = "SUCCESS")
    private String status;

    @Schema(description = "状态描述", example = "指令执行成功")
    private String statusDescription;

    @Schema(description = "执行结果")
    private Map<String, Object> result;

    @Schema(description = "错误信息", example = "设备离线")
    private String errorMessage;

    @Schema(description = "指令创建时间")
    private LocalDateTime createTime;

    @Schema(description = "指令发送时间")
    private LocalDateTime sendTime;

    @Schema(description = "指令完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "执行耗时（毫秒）", example = "1500")
    private Long executionTime;

    /**
     * 创建成功响应
     */
    public static CommandResponse success(String commandId, String deviceCode, String commandType) {
        CommandResponse response = new CommandResponse();
        response.setCommandId(commandId);
        response.setDeviceCode(deviceCode);
        response.setCommandType(commandType);
        response.setStatus("SUCCESS");
        response.setStatusDescription("指令执行成功");
        response.setCompleteTime(LocalDateTime.now());
        return response;
    }

    /**
     * 创建失败响应
     */
    public static CommandResponse failure(String commandId, String deviceCode, String commandType, String errorMessage) {
        CommandResponse response = new CommandResponse();
        response.setCommandId(commandId);
        response.setDeviceCode(deviceCode);
        response.setCommandType(commandType);
        response.setStatus("FAILED");
        response.setStatusDescription("指令执行失败");
        response.setErrorMessage(errorMessage);
        response.setCompleteTime(LocalDateTime.now());
        return response;
    }

    /**
     * 创建等待响应
     */
    public static CommandResponse pending(String commandId, String deviceCode, String commandType) {
        CommandResponse response = new CommandResponse();
        response.setCommandId(commandId);
        response.setDeviceCode(deviceCode);
        response.setCommandType(commandType);
        response.setStatus("PENDING");
        response.setStatusDescription("指令等待执行");
        response.setCreateTime(LocalDateTime.now());
        return response;
    }

    /**
     * 判断是否为成功状态
     */
    public boolean isSuccess() {
        return "SUCCESS".equals(status);
    }

    /**
     * 判断是否为失败状态
     */
    public boolean isFailure() {
        return "FAILED".equals(status);
    }

    /**
     * 判断是否为等待状态
     */
    public boolean isPending() {
        return "PENDING".equals(status);
    }
}
