package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 设备查询请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Data
@Schema(name = "DeviceQueryRequest", description = "设备查询请求")
public class DeviceQueryRequest {

    @Schema(description = "项目ID", example = "demo_project_001")
    private String projectId;

    @Schema(description = "设备编码", example = "TEMP_001")
    private String deviceCode;

    @Schema(description = "设备名称（模糊查询）", example = "温度")
    private String deviceName;

    @Schema(description = "设备类型", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"})
    private Byte deviceType;

    @Schema(description = "厂商", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"})
    private Byte vendor;

    @Schema(description = "设备状态", example = "1", allowableValues = {"1", "2", "3"})
    private Byte status;

    @Schema(description = "边缘程序ID", example = "edge_001")
    private String edgeProgramId;

    @Schema(description = "位置信息（模糊查询）", example = "车间A")
    private String location;

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    @Schema(description = "每页大小", example = "10")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 100, message = "每页大小不能超过100")
    private Integer pageSize = 10;

    @Schema(description = "排序字段", example = "created_at", allowableValues = {"created_at", "updated_at", "device_name", "last_report_time"})
    private String sortField = "created_at";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";
}
