package com.iot.platform.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.iot.platform.api.dto.SystemConfigRequest;
import com.iot.platform.api.service.SystemConfigService;
import com.iot.platform.common.entity.SystemConfig;
import com.iot.platform.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 系统配置管理控制器
 * 处理系统配置的CRUD操作
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/system-config")
@RequiredArgsConstructor
@Validated
@Tag(name = "系统配置管理", description = "系统配置的增删改查和分类管理")
public class SystemConfigController {

    private final SystemConfigService systemConfigService;

    @PostMapping
    @Operation(summary = "创建系统配置", description = "创建新的系统配置项")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "409", description = "配置项已存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<SystemConfig> createConfig(@Valid @RequestBody SystemConfigRequest request) {
        log.info("创建系统配置: category={}, configKey={}", request.getCategory(), request.getConfigKey());

        // 检查配置项是否已存在
        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", request.getCategory())
                   .eq("config_key", request.getConfigKey());
        if (request.getProjectId() != null) {
            queryWrapper.eq("project_id", request.getProjectId());
        } else {
            queryWrapper.isNull("project_id");
        }

        SystemConfig existingConfig = systemConfigService.getOne(queryWrapper);
        if (existingConfig != null) {
            return Result.error("配置项已存在: " + request.getCategory() + "." + request.getConfigKey());
        }

        // 创建配置对象
        SystemConfig config = new SystemConfig();
        BeanUtils.copyProperties(request, config);
        config.setCreatedAt(LocalDateTime.now());
        config.setUpdatedAt(LocalDateTime.now());

        // 保存配置
        boolean success = systemConfigService.save(config);
        if (success) {
            log.info("系统配置创建成功: id={}, category={}, configKey={}",
                    config.getId(), config.getCategory(), config.getConfigKey());
            return Result.success(config);
        } else {
            log.error("系统配置创建失败: category={}, configKey={}", request.getCategory(), request.getConfigKey());
            return Result.error("系统配置创建失败");
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取配置", description = "根据配置ID获取配置详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "配置不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<SystemConfig> getConfigById(
            @Parameter(description = "配置ID", required = true)
            @PathVariable @NotNull Long id) {

        log.debug("根据ID获取系统配置: id={}", id);

        SystemConfig config = systemConfigService.getById(id);
        if (config != null) {
            return Result.success(config);
        } else {
            return Result.error("配置不存在");
        }
    }

    @GetMapping("/category/{category}")
    @Operation(summary = "根据分类获取配置列表", description = "获取指定分类下的所有配置项")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<List<SystemConfig>> getConfigsByCategory(
            @Parameter(description = "配置分类", required = true)
            @PathVariable @NotBlank String category,
            @Parameter(description = "项目ID（可选）")
            @RequestParam(required = false) String projectId) {

        log.debug("根据分类获取配置列表: category={}, projectId={}", category, projectId);

        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category);
        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        } else {
            queryWrapper.isNull("project_id");
        }
        queryWrapper.orderByAsc("config_key");

        List<SystemConfig> configs = systemConfigService.list(queryWrapper);
        return Result.success(configs);
    }

    @GetMapping("/key/{category}/{configKey}")
    @Operation(summary = "根据分类和键获取配置", description = "根据配置分类和键获取具体配置项")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "配置不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<SystemConfig> getConfigByKey(
            @Parameter(description = "配置分类", required = true)
            @PathVariable @NotBlank String category,
            @Parameter(description = "配置键", required = true)
            @PathVariable @NotBlank String configKey,
            @Parameter(description = "项目ID（可选）")
            @RequestParam(required = false) String projectId) {

        log.debug("根据分类和键获取配置: category={}, configKey={}, projectId={}", category, configKey, projectId);

        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("category", category)
                   .eq("config_key", configKey);
        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        } else {
            queryWrapper.isNull("project_id");
        }

        SystemConfig config = systemConfigService.getOne(queryWrapper);
        if (config != null) {
            return Result.success(config);
        } else {
            return Result.error("配置不存在");
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新系统配置", description = "根据配置ID更新配置信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "配置不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<SystemConfig> updateConfig(
            @Parameter(description = "配置ID", required = true)
            @PathVariable @NotNull Long id,
            @Valid @RequestBody SystemConfigRequest request) {

        log.info("更新系统配置: id={}, request={}", id, request);

        // 检查配置是否存在
        SystemConfig existingConfig = systemConfigService.getById(id);
        if (existingConfig == null) {
            return Result.error("配置不存在");
        }

        // 检查是否可编辑
        if (existingConfig.getEditable() != null && existingConfig.getEditable() == 0) {
            return Result.error("该配置项不允许编辑");
        }

        // 更新配置信息
        BeanUtils.copyProperties(request, existingConfig, "id", "createdAt");
        existingConfig.setUpdatedAt(LocalDateTime.now());

        boolean success = systemConfigService.updateById(existingConfig);
        if (success) {
            log.info("系统配置更新成功: id={}, category={}, configKey={}",
                    id, existingConfig.getCategory(), existingConfig.getConfigKey());
            return Result.success(existingConfig);
        } else {
            log.error("系统配置更新失败: id={}", id);
            return Result.error("系统配置更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除系统配置", description = "根据配置ID删除配置")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "400", description = "配置不允许删除"),
        @ApiResponse(responseCode = "404", description = "配置不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> deleteConfig(
            @Parameter(description = "配置ID", required = true)
            @PathVariable @NotNull Long id) {

        log.info("删除系统配置: id={}", id);

        // 检查配置是否存在
        SystemConfig existingConfig = systemConfigService.getById(id);
        if (existingConfig == null) {
            return Result.error("配置不存在");
        }

        // 检查是否可编辑（不可编辑的配置也不能删除）
        if (existingConfig.getEditable() != null && existingConfig.getEditable() == 0) {
            return Result.error("该配置项不允许删除");
        }

        boolean success = systemConfigService.removeById(id);
        if (success) {
            log.info("系统配置删除成功: id={}, category={}, configKey={}",
                    id, existingConfig.getCategory(), existingConfig.getConfigKey());
            return Result.success();
        } else {
            log.error("系统配置删除失败: id={}", id);
            return Result.error("系统配置删除失败");
        }
    }

    @GetMapping("/categories")
    @Operation(summary = "获取所有配置分类", description = "获取系统中所有的配置分类列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<List<String>> getCategories() {
        log.debug("获取所有配置分类");

        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("DISTINCT category")
                   .orderByAsc("category");

        List<Map<String, Object>> result = systemConfigService.listMaps(queryWrapper);
        List<String> categories = result.stream()
                .map(map -> (String) map.get("category"))
                .toList();

        return Result.success(categories);
    }

    @GetMapping("/all")
    @Operation(summary = "获取所有配置", description = "获取所有系统配置项")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<List<SystemConfig>> getAllConfigs(
            @Parameter(description = "项目ID（可选）")
            @RequestParam(required = false) String projectId) {

        log.debug("获取所有配置: projectId={}", projectId);

        QueryWrapper<SystemConfig> queryWrapper = new QueryWrapper<>();
        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        } else {
            queryWrapper.isNull("project_id");
        }
        queryWrapper.orderByAsc("category", "config_key");

        List<SystemConfig> configs = systemConfigService.list(queryWrapper);
        return Result.success(configs);
    }
}
