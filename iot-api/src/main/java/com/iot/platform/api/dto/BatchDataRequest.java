package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 批量设备数据上报请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Schema(description = "批量设备数据上报请求")
public class BatchDataRequest {

    @Schema(description = "设备数据列表", required = true)
    @NotEmpty(message = "设备数据列表不能为空")
    @Size(max = 1000, message = "单次批量上报数据不能超过1000条")
    @Valid
    private List<DeviceDataRequest> dataList;

    @Schema(description = "边缘程序ID", example = "edge_001")
    @Size(max = 100, message = "边缘程序ID长度不能超过100个字符")
    private String edgeProgramId;

    @Schema(description = "批次ID", example = "batch_20241216_001")
    @Size(max = 50, message = "批次ID长度不能超过50个字符")
    private String batchId;

    @Schema(description = "批次时间戳（毫秒）", example = "1703664000000")
    private Long batchTime;

    /**
     * 获取数据条数
     */
    public int getDataCount() {
        return dataList != null ? dataList.size() : 0;
    }

    /**
     * 验证所有数据项
     */
    public boolean validateAllData() {
        if (dataList == null || dataList.isEmpty()) {
            return false;
        }

        return dataList.stream().allMatch(DeviceDataRequest::isDataValueValid);
    }

    /**
     * 获取涉及的项目ID列表
     */
    public List<String> getProjectIds() {
        if (dataList == null) {
            return List.of();
        }

        return dataList.stream()
                .map(DeviceDataRequest::getProjectId)
                .distinct()
                .toList();
    }

    /**
     * 获取涉及的设备编码列表
     */
    public List<String> getDeviceCodes() {
        if (dataList == null) {
            return List.of();
        }

        return dataList.stream()
                .map(DeviceDataRequest::getDeviceCode)
                .distinct()
                .toList();
    }

    /**
     * 获取设备数量
     */
    public int getDeviceCount() {
        return getDeviceCodes().size();
    }

    /**
     * 验证批次数据一致性
     */
    public boolean validateBatchConsistency() {
        if (dataList == null || dataList.isEmpty()) {
            return false;
        }

        // 检查批次ID和时间戳一致性
        if (batchId != null) {
            boolean allMatch = dataList.stream()
                    .allMatch(data -> batchId.equals(data.getBatchId()));
            if (!allMatch) {
                return false;
            }
        }

        // 检查边缘程序ID一致性
        if (edgeProgramId != null) {
            boolean allMatch = dataList.stream()
                    .allMatch(data -> edgeProgramId.equals(data.getEdgeProgramId()));
            if (!allMatch) {
                return false;
            }
        }

        return true;
    }

    /**
     * 按设备分组
     */
    public Map<String, List<DeviceDataRequest>> groupByDevice() {
        if (dataList == null) {
            return Map.of();
        }

        return dataList.stream()
                .collect(Collectors.groupingBy(DeviceDataRequest::getDeviceCode));
    }
}
