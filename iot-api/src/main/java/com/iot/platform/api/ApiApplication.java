package com.iot.platform.api;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * IoT平台API服务主启动类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@SpringBootApplication(scanBasePackages = {
    "com.iot.platform.api",
    "com.iot.platform.common"
})
@EnableConfigurationProperties
@EnableTransactionManagement
@EnableAsync
public class ApiApplication {

    public static void main(String[] args) {
        SpringApplication.run(ApiApplication.class, args);
        System.out.println("""

            ====================================
            🚀 IoT大数据平台API服务启动成功！
            📖 API文档地址: http://localhost:8000/api/swagger-ui.html
            🔍 健康检查: http://localhost:8000/api/actuator/health
            ====================================
            """);
    }
}
