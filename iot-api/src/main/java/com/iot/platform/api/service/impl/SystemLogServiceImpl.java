package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.SystemLog;
import com.iot.platform.common.mapper.SystemLogMapper;
import com.iot.platform.api.service.SystemLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统日志表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class SystemLogServiceImpl extends ServiceImpl<SystemLogMapper, SystemLog> implements SystemLogService {

}
