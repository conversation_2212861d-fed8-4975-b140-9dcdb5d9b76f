package com.iot.platform.api.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.iot.platform.common.mapper.DeviceMapper;
import com.iot.platform.api.service.DeviceService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备信息服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class DeviceServiceImpl extends ServiceImpl<DeviceMapper, Device> implements DeviceService {

    @Override
    public Device getByDeviceCode(String deviceCode) {
        if (deviceCode == null || deviceCode.trim().isEmpty()) {
            return null;
        }

        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_code", deviceCode);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<Device> getDevicesByProjectId(String projectId) {
        if (projectId == null || projectId.trim().isEmpty()) {
            return List.of();
        }

        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("project_id", projectId);
        queryWrapper.orderByDesc("created_at");
        return this.list(queryWrapper);
    }

    @Override
    public boolean updateDeviceStatus(String deviceCode, DeviceStatus status) {
        if (deviceCode == null || deviceCode.trim().isEmpty() || status == null) {
            return false;
        }

        try {
            UpdateWrapper<Device> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("device_code", deviceCode);
            updateWrapper.set("status", status.getCode());
            updateWrapper.set("updated_at", new java.util.Date());

            int result = this.baseMapper.update(null, updateWrapper);

            log.debug("更新设备状态: device={}, status={}, result={}",
                    deviceCode, status.getDescription(), result > 0);

            return result > 0;
        } catch (Exception e) {
            log.error("更新设备状态失败: device={}, status={}", deviceCode, status, e);
            return false;
        }
    }

    @Override
    public boolean updateLastReportTime(String deviceCode, Long reportTime) {
        if (deviceCode == null || deviceCode.trim().isEmpty() || reportTime == null) {
            return false;
        }

        try {
            UpdateWrapper<Device> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("device_code", deviceCode);
            updateWrapper.set("last_report_time", reportTime);
            updateWrapper.set("updated_at", new java.util.Date());

            int result = this.baseMapper.update(null, updateWrapper);

            log.debug("更新设备最后上报时间: device={}, time={}, result={}",
                    deviceCode, reportTime, result > 0);

            return result > 0;
        } catch (Exception e) {
            log.error("更新设备最后上报时间失败: device={}, time={}", deviceCode, reportTime, e);
            return false;
        }
    }

    @Override
    public boolean isDeviceCodeExists(String deviceCode) {
        if (deviceCode == null || deviceCode.trim().isEmpty()) {
            return false;
        }

        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("device_code", deviceCode);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public List<Device> getDevicesByEdgeProgramId(String edgeProgramId) {
        if (edgeProgramId == null || edgeProgramId.trim().isEmpty()) {
            return List.of();
        }

        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("edge_program_id", edgeProgramId);
        queryWrapper.orderByDesc("created_at");
        return this.list(queryWrapper);
    }

    @Override
    public long getOnlineDeviceCount(String projectId) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", DeviceStatus.ACTIVE.getCode());

        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        }

        // 检查最后上报时间在10分钟内的设备
        long tenMinutesAgo = System.currentTimeMillis() - 10 * 60 * 1000;
        queryWrapper.ge("last_report_time", tenMinutesAgo);

        return this.count(queryWrapper);
    }

    @Override
    public List<Device> getOfflineDevices(String projectId, int timeoutMinutes) {
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();

        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        }

        // 查找超过指定时间未上报的设备
        long timeoutMillis = System.currentTimeMillis() - timeoutMinutes * 60 * 1000L;
        queryWrapper.and(wrapper -> wrapper
                .lt("last_report_time", timeoutMillis)
                .or()
                .isNull("last_report_time")
        );

        queryWrapper.orderByDesc("last_report_time");
        return this.list(queryWrapper);
    }
}
