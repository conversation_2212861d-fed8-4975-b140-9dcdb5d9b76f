package com.iot.platform.api.config;

import com.iot.platform.api.security.AkSkAuthInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Configuration
@RequiredArgsConstructor
public class WebConfig implements WebMvcConfigurer {

    private final AkSkAuthInterceptor akSkAuthInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(akSkAuthInterceptor)
                .addPathPatterns("/**")
                .order(1);
    }
}
