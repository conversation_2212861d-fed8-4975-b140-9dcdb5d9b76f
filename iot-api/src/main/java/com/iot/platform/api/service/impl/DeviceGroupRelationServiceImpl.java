package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.DeviceGroupRelation;
import com.iot.platform.common.mapper.DeviceGroupRelationMapper;
import com.iot.platform.api.service.DeviceGroupRelationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 设备分组关联表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class DeviceGroupRelationServiceImpl extends ServiceImpl<DeviceGroupRelationMapper, DeviceGroupRelation> implements DeviceGroupRelationService {

}
