package com.iot.platform.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * AK/SK认证配置属性
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Component
@ConfigurationProperties(prefix = "iot.security.ak-sk")
public class AkSkProperties {

    /**
     * 是否启用AK/SK认证
     */
    private Boolean enabled = true;

    /**
     * Access Key请求头名称
     */
    private String headerAccessKey = "X-Access-Key";

    /**
     * 签名请求头名称
     */
    private String headerSignature = "X-Signature";

    /**
     * 时间戳请求头名称
     */
    private String headerTimestamp = "X-Timestamp";

    /**
     * 签名超时时间（秒）
     */
    private Long signatureTimeout = 300L;

    /**
     * 需要跳过认证的路径
     */
    private String[] skipPaths = {
        "/api/actuator/**",
        "/api/swagger-ui/**",
        "/api/v3/api-docs/**",
        "/api/swagger-resources/**",
        "/api/webjars/**",
        "/api/doc.html",
        "/api/favicon.ico",
        "/api/error"
    };
}
