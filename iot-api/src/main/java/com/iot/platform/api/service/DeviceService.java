package com.iot.platform.api.service;

import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 设备信息服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface DeviceService extends IService<Device> {

    /**
     * 根据设备编码获取设备信息
     *
     * @param deviceCode 设备编码
     * @return 设备信息
     */
    Device getByDeviceCode(String deviceCode);

    /**
     * 根据项目ID获取设备列表
     *
     * @param projectId 项目ID
     * @return 设备列表
     */
    List<Device> getDevicesByProjectId(String projectId);

    /**
     * 更新设备状态
     *
     * @param deviceCode 设备编码
     * @param status 设备状态
     * @return 更新结果
     */
    boolean updateDeviceStatus(String deviceCode, DeviceStatus status);

    /**
     * 更新设备最后上报时间
     *
     * @param deviceCode 设备编码
     * @param reportTime 上报时间戳
     * @return 更新结果
     */
    boolean updateLastReportTime(String deviceCode, Long reportTime);

    /**
     * 检查设备编码是否存在
     *
     * @param deviceCode 设备编码
     * @return 是否存在
     */
    boolean isDeviceCodeExists(String deviceCode);

    /**
     * 根据边缘程序ID获取设备列表
     *
     * @param edgeProgramId 边缘程序ID
     * @return 设备列表
     */
    List<Device> getDevicesByEdgeProgramId(String edgeProgramId);

    /**
     * 获取在线设备数量
     *
     * @param projectId 项目ID（可选）
     * @return 在线设备数量
     */
    long getOnlineDeviceCount(String projectId);

    /**
     * 获取离线设备列表
     *
     * @param projectId 项目ID（可选）
     * @param timeoutMinutes 超时分钟数
     * @return 离线设备列表
     */
    List<Device> getOfflineDevices(String projectId, int timeoutMinutes);
}
