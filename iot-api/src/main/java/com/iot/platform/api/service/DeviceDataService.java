package com.iot.platform.api.service;

import com.iot.platform.api.dto.DeviceDataRequest;
import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.common.response.Result;

import java.math.BigDecimal;

import java.util.List;
import java.util.Map;

/**
 * 设备数据处理服务接口
 * 负责设备数据的接收、验证、处理和存储
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface DeviceDataService {

    /**
     * 处理单条设备数据
     *
     * @param request 设备数据请求
     * @return 处理结果
     */
    Result<Void> processDeviceData(DeviceDataRequest request);

    /**
     * 批量处理设备数据
     *
     * @param request 批量设备数据请求
     * @return 处理结果
     */
    Result<Map<String, Object>> processBatchDeviceData(BatchDataRequest request);

    /**
     * 验证设备数据格式
     *
     * @param request 设备数据请求
     * @return 验证结果
     */
    boolean validateDeviceData(DeviceDataRequest request);

    /**
     * 数据质量评估
     *
     * @param dataValue 数据值
     * @param dataCode 数据类型码
     * @param deviceCode 设备编码
     * @return 质量等级 (1-优秀, 2-良好, 3-一般, 4-较差)
     */
    byte evaluateDataQuality(BigDecimal dataValue, Byte dataCode, String deviceCode);

    /**
     * 获取设备最新数据
     *
     * @param deviceCode 设备编码
     * @param dataCode 数据类型码（可选）
     * @return 最新数据列表
     */
    List<Map<String, Object>> getLatestDeviceData(String deviceCode, Byte dataCode);

    /**
     * 获取设备历史数据
     *
     * @param deviceCode 设备编码
     * @param dataCode 数据类型码
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param limit 限制条数
     * @return 历史数据列表
     */
    List<Map<String, Object>> getDeviceHistoryData(String deviceCode, Byte dataCode,
                                                   Long startTime, Long endTime, Integer limit);

    /**
     * 获取设备数据统计信息
     *
     * @param deviceCode 设备编码
     * @param dataCode 数据类型码
     * @param timeRange 时间范围（小时）
     * @return 统计信息
     */
    Map<String, Object> getDeviceDataStatistics(String deviceCode, Byte dataCode, Integer timeRange);

    /**
     * 清理过期数据
     *
     * @param retentionDays 保留天数
     * @return 清理的记录数
     */
    long cleanExpiredData(int retentionDays);

    /**
     * 更新设备最后上报时间
     *
     * @param deviceCode 设备编码
     * @param reportTime 上报时间戳
     */
    void updateDeviceLastReportTime(String deviceCode, Long reportTime);

    /**
     * 检查设备是否在线
     *
     * @param deviceCode 设备编码
     * @param timeoutMinutes 超时分钟数
     * @return 是否在线
     */
    boolean isDeviceOnline(String deviceCode, int timeoutMinutes);
}
