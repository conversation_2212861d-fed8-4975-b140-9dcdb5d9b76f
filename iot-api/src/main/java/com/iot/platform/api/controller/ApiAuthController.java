package com.iot.platform.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iot.platform.api.dto.ApiAuthCreateRequest;
import com.iot.platform.api.dto.ApiAuthUpdateRequest;
import com.iot.platform.api.service.ApiAuthService;
import com.iot.platform.common.entity.ApiAuth;
import com.iot.platform.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * API认证管理控制器
 * 处理API认证信息的CRUD操作，作为IoT平台的用户管理系统
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/api-auth")
@RequiredArgsConstructor
@Validated
@Tag(name = "API认证管理", description = "API认证的增删改查和权限管理，IoT平台的用户管理系统")
public class ApiAuthController {

    private final ApiAuthService apiAuthService;

    @PostMapping
    @Operation(summary = "创建API认证", description = "为应用创建新的API认证")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<ApiAuth> createApiAuth(@Valid @RequestBody ApiAuthCreateRequest request) {
        log.info("创建API认证: projectId={}, appName={}", request.getProjectId(), request.getAppName());

        // 创建API认证对象
        ApiAuth apiAuth = new ApiAuth();
        BeanUtils.copyProperties(request, apiAuth);

        // 生成API Key和Secret
        apiAuth.setAccessKey(generateApiKey());
        apiAuth.setSecretKey(generateApiSecret());
        apiAuth.setStatus((byte) 1); // 默认启用
        apiAuth.setCreatedAt(LocalDateTime.now());
        apiAuth.setUpdatedAt(LocalDateTime.now());

        // 保存API认证
        boolean success = apiAuthService.save(apiAuth);
        if (success) {
            log.info("API认证创建成功: id={}, accessKey={}", apiAuth.getId(), apiAuth.getAccessKey());
            return Result.success(apiAuth);
        } else {
            log.error("API认证创建失败: projectId={}, appName={}", request.getProjectId(), request.getAppName());
            return Result.error("API认证创建失败");
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取API认证", description = "根据认证ID获取API认证详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "认证不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<ApiAuth> getApiAuthById(
            @Parameter(description = "认证ID", required = true)
            @PathVariable @NotNull Long id) {

        log.debug("根据ID获取API认证: id={}", id);

        ApiAuth apiAuth = apiAuthService.getById(id);
        if (apiAuth != null) {
            // 隐藏敏感信息
            apiAuth.setSecretKey("******");
            return Result.success(apiAuth);
        } else {
            return Result.error("API认证不存在");
        }
    }

    @GetMapping("/list")
    @Operation(summary = "分页查询API认证列表", description = "根据条件分页查询API认证列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<IPage<ApiAuth>> getApiAuthList(
            @Parameter(description = "项目ID")
            @RequestParam(required = false) String projectId,
            @Parameter(description = "应用名称（模糊查询）")
            @RequestParam(required = false) String appName,
            @Parameter(description = "状态")
            @RequestParam(required = false) Byte status,
            @Parameter(description = "页码")
            @RequestParam(defaultValue = "1") @Min(1) Integer pageNum,
            @Parameter(description = "每页大小")
            @RequestParam(defaultValue = "10") @Min(1) @Max(100) Integer pageSize) {

        log.debug("分页查询API认证列表: projectId={}, appName={}, status={}, pageNum={}, pageSize={}",
                projectId, appName, status, pageNum, pageSize);

        // 构建查询条件
        QueryWrapper<ApiAuth> queryWrapper = new QueryWrapper<>();

        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        }
        if (appName != null && !appName.trim().isEmpty()) {
            queryWrapper.like("app_name", appName);
        }
        if (status != null) {
            queryWrapper.eq("status", status);
        }

        queryWrapper.orderByDesc("created_at");

        // 分页查询
        Page<ApiAuth> page = new Page<>(pageNum, pageSize);
        IPage<ApiAuth> result = apiAuthService.page(page, queryWrapper);

        // 隐藏敏感信息
        result.getRecords().forEach(auth -> auth.setSecretKey("******"));

        return Result.success(result);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新API认证", description = "根据认证ID更新API认证信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "认证不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<ApiAuth> updateApiAuth(
            @Parameter(description = "认证ID", required = true)
            @PathVariable @NotNull Long id,
            @Valid @RequestBody ApiAuthUpdateRequest request) {

        log.info("更新API认证: id={}, request={}", id, request);

        // 检查认证是否存在
        ApiAuth existingAuth = apiAuthService.getById(id);
        if (existingAuth == null) {
            return Result.error("API认证不存在");
        }

        // 更新认证信息
        BeanUtils.copyProperties(request, existingAuth, "id", "projectId", "accessKey", "secretKey", "createdAt");
        existingAuth.setUpdatedAt(LocalDateTime.now());

        boolean success = apiAuthService.updateById(existingAuth);
        if (success) {
            log.info("API认证更新成功: id={}, accessKey={}", id, existingAuth.getAccessKey());
            // 隐藏敏感信息
            existingAuth.setSecretKey("******");
            return Result.success(existingAuth);
        } else {
            log.error("API认证更新失败: id={}", id);
            return Result.error("API认证更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除API认证", description = "根据认证ID删除API认证")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "认证不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> deleteApiAuth(
            @Parameter(description = "认证ID", required = true)
            @PathVariable @NotNull Long id) {

        log.info("删除API认证: id={}", id);

        // 检查认证是否存在
        ApiAuth existingAuth = apiAuthService.getById(id);
        if (existingAuth == null) {
            return Result.error("API认证不存在");
        }

        boolean success = apiAuthService.removeById(id);
        if (success) {
            log.info("API认证删除成功: id={}, accessKey={}", id, existingAuth.getAccessKey());
            return Result.success();
        } else {
            log.error("API认证删除失败: id={}", id);
            return Result.error("API认证删除失败");
        }
    }

    @PutMapping("/{id}/status")
    @Operation(summary = "更新API认证状态", description = "启用或禁用API认证")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "状态更新成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "认证不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> updateApiAuthStatus(
            @Parameter(description = "认证ID", required = true)
            @PathVariable @NotNull Long id,
            @Parameter(description = "状态（0-禁用，1-启用）", required = true)
            @RequestParam @NotNull Byte status) {

        log.info("更新API认证状态: id={}, status={}", id, status);

        // 验证状态值
        if (status != 0 && status != 1) {
            return Result.error("无效的状态值，只能是0（禁用）或1（启用）");
        }

        // 检查认证是否存在
        ApiAuth existingAuth = apiAuthService.getById(id);
        if (existingAuth == null) {
            return Result.error("API认证不存在");
        }

        existingAuth.setStatus(status);
        existingAuth.setUpdatedAt(LocalDateTime.now());

        boolean success = apiAuthService.updateById(existingAuth);
        if (success) {
            log.info("API认证状态更新成功: id={}, status={}", id, status);
            return Result.success();
        } else {
            log.error("API认证状态更新失败: id={}", id);
            return Result.error("API认证状态更新失败");
        }
    }

    @PostMapping("/{id}/regenerate-secret")
    @Operation(summary = "重新生成API Secret", description = "为指定的API认证重新生成Secret")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "重新生成成功"),
        @ApiResponse(responseCode = "404", description = "认证不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<String> regenerateApiSecret(
            @Parameter(description = "认证ID", required = true)
            @PathVariable @NotNull Long id) {

        log.info("重新生成API Secret: id={}", id);

        // 检查认证是否存在
        ApiAuth existingAuth = apiAuthService.getById(id);
        if (existingAuth == null) {
            return Result.error("API认证不存在");
        }

        // 生成新的Secret
        String newSecret = generateApiSecret();
        existingAuth.setSecretKey(newSecret);
        existingAuth.setUpdatedAt(LocalDateTime.now());

        boolean success = apiAuthService.updateById(existingAuth);
        if (success) {
            log.info("API Secret重新生成成功: id={}, accessKey={}", id, existingAuth.getAccessKey());
            return Result.success(newSecret);
        } else {
            log.error("API Secret重新生成失败: id={}", id);
            return Result.error("API Secret重新生成失败");
        }
    }

    /**
     * 生成API Key
     */
    private String generateApiKey() {
        return "ak_" + UUID.randomUUID().toString().replace("-", "").substring(0, 24);
    }

    /**
     * 生成API Secret
     */
    private String generateApiSecret() {
        return "sk_" + UUID.randomUUID().toString().replace("-", "") +
               UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
}
