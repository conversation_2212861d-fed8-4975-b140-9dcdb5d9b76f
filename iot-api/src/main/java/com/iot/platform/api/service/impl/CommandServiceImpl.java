package com.iot.platform.api.service.impl;

import com.iot.platform.api.dto.CommandRequest;
import com.iot.platform.api.dto.CommandResponse;
import com.iot.platform.api.service.CommandService;
import com.iot.platform.common.response.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备控制指令服务实现
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CommandServiceImpl implements CommandService {

    /**
     * 指令缓存（实际项目中应该使用数据库）
     */
    private final Map<String, CommandInfo> commandCache = new ConcurrentHashMap<>();

    @Override
    public Result<CommandResponse> sendCommand(CommandRequest request) {
        try {
            // 生成指令ID
            String commandId = generateCommandId();

            // 验证设备是否存在
            if (!isDeviceExists(request.getDeviceCode())) {
                return Result.error("设备不存在: " + request.getDeviceCode());
            }

            // 创建指令信息
            CommandInfo commandInfo = new CommandInfo();
            commandInfo.setCommandId(commandId);
            commandInfo.setDeviceCode(request.getDeviceCode());
            commandInfo.setCommandType(request.getCommandType());
            commandInfo.setParameters(request.getParameters());
            commandInfo.setPriority(request.getPriority());
            commandInfo.setTimeout(request.getTimeout());
            commandInfo.setRequireResponse(request.getRequireResponse());
            commandInfo.setRetryCount(request.getRetryCount());
            commandInfo.setProjectId(request.getProjectId());
            commandInfo.setOperatorId(request.getOperatorId());
            commandInfo.setDescription(request.getDescription());
            commandInfo.setCreateTime(LocalDateTime.now());
            commandInfo.setStatus("PENDING");

            // 保存指令信息
            commandCache.put(commandId, commandInfo);

            // 模拟发送指令到设备（实际项目中应该通过MQTT或其他协议发送）
            boolean sendSuccess = sendCommandToDevice(commandInfo);

            if (sendSuccess) {
                commandInfo.setStatus("SENT");
                commandInfo.setSendTime(LocalDateTime.now());

                // 模拟指令执行（实际项目中设备会异步响应）
                simulateCommandExecution(commandInfo);

                CommandResponse response = CommandResponse.pending(commandId, request.getDeviceCode(), request.getCommandType());
                return Result.success(response);
            } else {
                commandInfo.setStatus("FAILED");
                commandInfo.setErrorMessage("指令发送失败");

                CommandResponse response = CommandResponse.failure(commandId, request.getDeviceCode(),
                        request.getCommandType(), "指令发送失败");
                return Result.error("指令发送失败");
            }

        } catch (Exception e) {
            log.error("发送设备控制指令失败", e);
            return Result.error("指令发送异常: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> sendBatchCommands(List<CommandRequest> requests) {
        try {
            List<Map<String, Object>> results = new ArrayList<>();
            int successCount = 0;
            int failureCount = 0;

            for (CommandRequest request : requests) {
                Result<CommandResponse> result = sendCommand(request);

                Map<String, Object> commandResult = new HashMap<>();
                commandResult.put("deviceCode", request.getDeviceCode());
                commandResult.put("commandType", request.getCommandType());
                commandResult.put("success", result.isSuccess());

                if (result.isSuccess()) {
                    commandResult.put("commandId", result.getData().getCommandId());
                    successCount++;
                } else {
                    commandResult.put("errorMessage", result.getMessage());
                    failureCount++;
                }

                results.add(commandResult);
            }

            Map<String, Object> batchResult = new HashMap<>();
            batchResult.put("totalCount", requests.size());
            batchResult.put("successCount", successCount);
            batchResult.put("failureCount", failureCount);
            batchResult.put("results", results);
            batchResult.put("batchId", "batch_" + System.currentTimeMillis());
            batchResult.put("executeTime", LocalDateTime.now());

            return Result.success(batchResult);

        } catch (Exception e) {
            log.error("批量发送设备控制指令失败", e);
            return Result.error("批量指令发送异常: " + e.getMessage());
        }
    }

    @Override
    public Result<Map<String, Object>> getCommandStatus(String commandId) {
        try {
            CommandInfo commandInfo = commandCache.get(commandId);
            if (commandInfo == null) {
                return Result.error("指令不存在: " + commandId);
            }

            Map<String, Object> status = new HashMap<>();
            status.put("commandId", commandInfo.getCommandId());
            status.put("deviceCode", commandInfo.getDeviceCode());
            status.put("commandType", commandInfo.getCommandType());
            status.put("status", commandInfo.getStatus());
            status.put("statusDescription", getStatusDescription(commandInfo.getStatus()));
            status.put("createTime", commandInfo.getCreateTime());
            status.put("sendTime", commandInfo.getSendTime());
            status.put("completeTime", commandInfo.getCompleteTime());
            status.put("errorMessage", commandInfo.getErrorMessage());
            status.put("result", commandInfo.getResult());

            // 计算执行时间
            if (commandInfo.getCreateTime() != null && commandInfo.getCompleteTime() != null) {
                // 简化计算，实际项目中应该使用更精确的时间计算
                status.put("executionTimeMs", 1500);
            }

            return Result.success(status);

        } catch (Exception e) {
            log.error("查询指令状态失败: {}", commandId, e);
            return Result.error("查询指令状态异常: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Map<String, Object>>> getCommandHistory(String deviceCode, Long startTime, Long endTime, Integer limit) {
        try {
            List<Map<String, Object>> history = new ArrayList<>();

            // 从缓存中查找指定设备的指令历史（实际项目中应该从数据库查询）
            commandCache.values().stream()
                    .filter(cmd -> deviceCode.equals(cmd.getDeviceCode()))
                    .sorted((a, b) -> b.getCreateTime().compareTo(a.getCreateTime()))
                    .limit(limit != null ? limit : 100)
                    .forEach(cmd -> {
                        Map<String, Object> cmdInfo = new HashMap<>();
                        cmdInfo.put("commandId", cmd.getCommandId());
                        cmdInfo.put("commandType", cmd.getCommandType());
                        cmdInfo.put("status", cmd.getStatus());
                        cmdInfo.put("createTime", cmd.getCreateTime());
                        cmdInfo.put("completeTime", cmd.getCompleteTime());
                        cmdInfo.put("description", cmd.getDescription());
                        cmdInfo.put("operatorId", cmd.getOperatorId());
                        history.add(cmdInfo);
                    });

            return Result.success(history);

        } catch (Exception e) {
            log.error("查询设备指令历史失败: {}", deviceCode, e);
            return Result.error("查询指令历史异常: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> cancelCommand(String commandId) {
        return cancelCommand(commandId, "用户取消");
    }

    /**
     * 取消指令执行（带原因）
     */
    public Result<Void> cancelCommand(String commandId, String reason) {
        try {
            CommandInfo commandInfo = commandCache.get(commandId);
            if (commandInfo == null) {
                return Result.error("指令不存在: " + commandId);
            }

            if ("SUCCESS".equals(commandInfo.getStatus()) || "FAILED".equals(commandInfo.getStatus())) {
                return Result.error("指令已完成，无法取消");
            }

            commandInfo.setStatus("CANCELLED");
            commandInfo.setErrorMessage("指令已取消: " + (reason != null ? reason : "用户取消"));
            commandInfo.setCompleteTime(LocalDateTime.now());

            log.info("指令已取消: commandId={}, reason={}", commandId, reason);
            return Result.success("指令取消成功");

        } catch (Exception e) {
            log.error("取消指令失败: {}", commandId, e);
            return Result.error("取消指令异常: " + e.getMessage());
        }
    }

    @Override
    public Result<Void> handleCommandResponse(String commandId, Map<String, Object> response) {
        try {
            CommandInfo commandInfo = commandCache.get(commandId);
            if (commandInfo == null) {
                return Result.error("指令不存在: " + commandId);
            }

            commandInfo.setResult(response);
            commandInfo.setCompleteTime(LocalDateTime.now());

            // 根据响应结果设置状态
            Object success = response.get("success");
            if (Boolean.TRUE.equals(success)) {
                commandInfo.setStatus("SUCCESS");
            } else {
                commandInfo.setStatus("FAILED");
                commandInfo.setErrorMessage(String.valueOf(response.get("errorMessage")));
            }

            log.info("处理指令响应: commandId={}, status={}", commandId, commandInfo.getStatus());
            return Result.success("指令响应处理成功");

        } catch (Exception e) {
            log.error("处理指令响应失败: {}", commandId, e);
            return Result.error("处理指令响应异常: " + e.getMessage());
        }
    }

    @Override
    public Result<List<Map<String, Object>>> getSupportedCommands(String deviceCode) {
        try {
            // 模拟返回设备支持的指令类型
            List<Map<String, Object>> commands = List.of(
                Map.of("commandType", "SWITCH_ON", "description", "开启设备", "parameters", List.of()),
                Map.of("commandType", "SWITCH_OFF", "description", "关闭设备", "parameters", List.of()),
                Map.of("commandType", "SET_TEMPERATURE", "description", "设置温度",
                       "parameters", List.of(Map.of("name", "temperature", "type", "number", "required", true))),
                Map.of("commandType", "GET_STATUS", "description", "获取状态", "parameters", List.of())
            );

            return Result.success(commands);

        } catch (Exception e) {
            log.error("获取设备支持指令失败: {}", deviceCode, e);
            return Result.error("获取设备支持指令异常: " + e.getMessage());
        }
    }

    @Override
    public boolean isCommandTimeout(String commandId) {
        try {
            CommandInfo commandInfo = commandCache.get(commandId);
            if (commandInfo == null) {
                return false;
            }

            if (commandInfo.getTimeout() == null || commandInfo.getSendTime() == null) {
                return false;
            }

            LocalDateTime timeoutTime = commandInfo.getSendTime().plusSeconds(commandInfo.getTimeout());
            return LocalDateTime.now().isAfter(timeoutTime);

        } catch (Exception e) {
            log.error("检查指令超时失败: {}", commandId, e);
            return false;
        }
    }

    @Override
    public Result<Void> retryCommand(String commandId) {
        try {
            CommandInfo commandInfo = commandCache.get(commandId);
            if (commandInfo == null) {
                return Result.error("指令不存在: " + commandId);
            }

            if (!"FAILED".equals(commandInfo.getStatus()) && !"TIMEOUT".equals(commandInfo.getStatus())) {
                return Result.error("只能重试失败或超时的指令");
            }

            // 重置状态并重新发送
            commandInfo.setStatus("PENDING");
            commandInfo.setErrorMessage(null);
            commandInfo.setSendTime(null);
            commandInfo.setCompleteTime(null);

            boolean sendSuccess = sendCommandToDevice(commandInfo);
            if (sendSuccess) {
                commandInfo.setStatus("SENT");
                commandInfo.setSendTime(LocalDateTime.now());
                simulateCommandExecution(commandInfo);

                log.info("指令重试成功: {}", commandId);
                return Result.success("指令重试成功");
            } else {
                commandInfo.setStatus("FAILED");
                commandInfo.setErrorMessage("重试发送失败");
                return Result.error("指令重试失败");
            }

        } catch (Exception e) {
            log.error("重试指令失败: {}", commandId, e);
            return Result.error("重试指令异常: " + e.getMessage());
        }
    }

    @Override
    public Map<String, Object> getCommandStatistics(String deviceCode, Integer timeRange) {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 统计指定时间范围内的指令
            LocalDateTime startTime = LocalDateTime.now().minusHours(timeRange != null ? timeRange : 24);

            long totalCount = 0;
            long successCount = 0;
            long failedCount = 0;
            long pendingCount = 0;

            for (CommandInfo cmd : commandCache.values()) {
                if (deviceCode != null && !deviceCode.equals(cmd.getDeviceCode())) {
                    continue;
                }

                if (cmd.getCreateTime() != null && cmd.getCreateTime().isAfter(startTime)) {
                    totalCount++;

                    switch (cmd.getStatus()) {
                        case "SUCCESS" -> successCount++;
                        case "FAILED", "TIMEOUT" -> failedCount++;
                        case "PENDING", "SENT", "EXECUTING" -> pendingCount++;
                    }
                }
            }

            stats.put("totalCount", totalCount);
            stats.put("successCount", successCount);
            stats.put("failedCount", failedCount);
            stats.put("pendingCount", pendingCount);
            stats.put("successRate", totalCount > 0 ? (double) successCount / totalCount * 100 : 0.0);
            stats.put("timeRange", timeRange != null ? timeRange : 24);
            stats.put("deviceCode", deviceCode);

            return stats;

        } catch (Exception e) {
            log.error("获取指令统计失败: deviceCode={}, timeRange={}", deviceCode, timeRange, e);
            return Map.of("error", "获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成指令ID
     */
    private String generateCommandId() {
        return "cmd_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }

    /**
     * 检查设备是否存在
     */
    private boolean isDeviceExists(String deviceCode) {
        // 实际项目中应该查询设备表
        return deviceCode != null && !deviceCode.trim().isEmpty();
    }

    /**
     * 发送指令到设备
     */
    private boolean sendCommandToDevice(CommandInfo commandInfo) {
        // 实际项目中应该通过MQTT、HTTP等协议发送指令到设备
        log.info("发送指令到设备: device={}, command={}",
                commandInfo.getDeviceCode(), commandInfo.getCommandType());

        // 模拟发送成功
        return true;
    }

    /**
     * 模拟指令执行
     */
    private void simulateCommandExecution(CommandInfo commandInfo) {
        // 模拟异步执行指令
        new Thread(() -> {
            try {
                Thread.sleep(2000); // 模拟执行时间

                // 模拟执行成功
                commandInfo.setStatus("SUCCESS");
                commandInfo.setCompleteTime(LocalDateTime.now());

                Map<String, Object> result = new HashMap<>();
                result.put("executeTime", System.currentTimeMillis());
                result.put("message", "指令执行成功");
                commandInfo.setResult(result);

                log.info("指令执行完成: commandId={}, device={}",
                        commandInfo.getCommandId(), commandInfo.getDeviceCode());

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                commandInfo.setStatus("FAILED");
                commandInfo.setErrorMessage("指令执行被中断");
            }
        }).start();
    }

    /**
     * 获取状态描述
     */
    private String getStatusDescription(String status) {
        return switch (status) {
            case "PENDING" -> "等待执行";
            case "SENT" -> "已发送";
            case "EXECUTING" -> "执行中";
            case "SUCCESS" -> "执行成功";
            case "FAILED" -> "执行失败";
            case "CANCELLED" -> "已取消";
            case "TIMEOUT" -> "执行超时";
            default -> "未知状态";
        };
    }

    /**
     * 指令信息内部类
     */
    private static class CommandInfo {
        private String commandId;
        private String deviceCode;
        private String commandType;
        private Map<String, Object> parameters;
        private Integer priority;
        private Integer timeout;
        private Boolean requireResponse;
        private Integer retryCount;
        private String projectId;
        private String operatorId;
        private String description;
        private LocalDateTime createTime;
        private LocalDateTime sendTime;
        private LocalDateTime completeTime;
        private String status;
        private String errorMessage;
        private Map<String, Object> result;

        // Getters and Setters
        public String getCommandId() { return commandId; }
        public void setCommandId(String commandId) { this.commandId = commandId; }

        public String getDeviceCode() { return deviceCode; }
        public void setDeviceCode(String deviceCode) { this.deviceCode = deviceCode; }

        public String getCommandType() { return commandType; }
        public void setCommandType(String commandType) { this.commandType = commandType; }

        public Map<String, Object> getParameters() { return parameters; }
        public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }

        public Integer getPriority() { return priority; }
        public void setPriority(Integer priority) { this.priority = priority; }

        public Integer getTimeout() { return timeout; }
        public void setTimeout(Integer timeout) { this.timeout = timeout; }

        public Boolean getRequireResponse() { return requireResponse; }
        public void setRequireResponse(Boolean requireResponse) { this.requireResponse = requireResponse; }

        public Integer getRetryCount() { return retryCount; }
        public void setRetryCount(Integer retryCount) { this.retryCount = retryCount; }

        public String getProjectId() { return projectId; }
        public void setProjectId(String projectId) { this.projectId = projectId; }

        public String getOperatorId() { return operatorId; }
        public void setOperatorId(String operatorId) { this.operatorId = operatorId; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }

        public LocalDateTime getSendTime() { return sendTime; }
        public void setSendTime(LocalDateTime sendTime) { this.sendTime = sendTime; }

        public LocalDateTime getCompleteTime() { return completeTime; }
        public void setCompleteTime(LocalDateTime completeTime) { this.completeTime = completeTime; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        public Map<String, Object> getResult() { return result; }
        public void setResult(Map<String, Object> result) { this.result = result; }
    }
}
