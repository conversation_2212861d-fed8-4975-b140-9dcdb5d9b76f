package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.Task;
import com.iot.platform.common.mapper.TaskMapper;
import com.iot.platform.api.service.TaskService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 计算任务配置表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class TaskServiceImpl extends ServiceImpl<TaskMapper, Task> implements TaskService {

}
