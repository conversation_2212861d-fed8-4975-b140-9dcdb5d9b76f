package com.iot.platform.api.controller;

import com.iot.platform.common.archive.DataArchiveService;
import com.iot.platform.common.response.Result;
import com.iot.platform.common.sharding.ShardingService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统状态监控控制器
 * 提供系统各组件的状态信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/system")
@RequiredArgsConstructor
@Tag(name = "系统状态监控", description = "系统各组件状态查询接口")
public class SystemStatusController {

    private final ShardingService shardingService;
    private final DataArchiveService dataArchiveService;

    @GetMapping("/status")
    @Operation(summary = "获取系统整体状态", description = "返回系统各组件的运行状态信息")
    public Result<Map<String, Object>> getSystemStatus() {
        try {
            Map<String, Object> status = new HashMap<>();
            status.put("timestamp", LocalDateTime.now());
            status.put("version", "1.0.0");
            status.put("status", "RUNNING");

            // 分表状态
            ShardingService.ShardingStatus shardingStatus = shardingService.getShardingStatus();
            status.put("sharding", Map.of(
                "enabled", true,
                "currentTable", shardingStatus.getCurrentTableName(),
                "nextTable", shardingStatus.getNextTableName(),
                "totalTables", shardingStatus.getExistingTableCount(),
                "totalRows", shardingStatus.getTotalRows(),
                "totalSizeMb", shardingStatus.getTotalSizeMb()
            ));

            // 归档状态
            DataArchiveService.ArchiveStatus archiveStatus = dataArchiveService.getArchiveStatus();
            Map<String, Object> archiveInfo = new HashMap<>();
            archiveInfo.put("enabled", archiveStatus.isEnabled());
            archiveInfo.put("totalArchivedFiles", archiveStatus.getTotalArchivedFiles());
            archiveInfo.put("totalArchivedRows", archiveStatus.getTotalArchivedRows());
            archiveInfo.put("totalArchivedSizeMb", archiveStatus.getTotalArchivedSizeMb());
            archiveInfo.put("lastArchiveTime", archiveStatus.getLastArchiveTime() != null ?
                archiveStatus.getLastArchiveTime() : "从未归档");
            status.put("archive", archiveInfo);

            // 算法状态 (暂时移除，因为AlgorithmFactory在不同模块中)
            status.put("algorithms", Map.of(
                "supportedTypes", 5,
                "algorithmInfo", "算法工厂功能正常"
            ));

            return Result.success(status);

        } catch (Exception e) {
            log.error("获取系统状态失败", e);
            return Result.error("获取系统状态异常: " + e.getMessage());
        }
    }

    @GetMapping("/health")
    @Operation(summary = "健康检查", description = "简单的健康检查接口")
    public Result<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", LocalDateTime.now());
        health.put("uptime", System.currentTimeMillis());

        return Result.success(health);
    }

    @GetMapping("/sharding/status")
    @Operation(summary = "获取分表状态", description = "返回数据分表的详细状态信息")
    public Result<ShardingService.ShardingStatus> getShardingStatus() {
        try {
            ShardingService.ShardingStatus status = shardingService.getShardingStatus();
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取分表状态失败", e);
            return Result.error("获取分表状态异常: " + e.getMessage());
        }
    }

    @GetMapping("/archive/status")
    @Operation(summary = "获取归档状态", description = "返回数据归档的详细状态信息")
    public Result<DataArchiveService.ArchiveStatus> getArchiveStatus() {
        try {
            DataArchiveService.ArchiveStatus status = dataArchiveService.getArchiveStatus();
            return Result.success(status);
        } catch (Exception e) {
            log.error("获取归档状态失败", e);
            return Result.error("获取归档状态异常: " + e.getMessage());
        }
    }

    @GetMapping("/algorithms")
    @Operation(summary = "获取算法信息", description = "返回所有支持的计算算法信息")
    public Result<Object> getAlgorithmInfo() {
        try {
            Map<String, Object> algorithmInfo = new HashMap<>();
            algorithmInfo.put("supportedTypes", List.of("AVG", "MAX", "MIN", "COUNT", "STDDEV"));
            algorithmInfo.put("algorithms", Map.of(
                "AVG", "平均值算法",
                "MAX", "最大值算法",
                "MIN", "最小值算法",
                "COUNT", "计数算法",
                "STDDEV", "标准差算法"
            ));

            return Result.success(algorithmInfo);
        } catch (Exception e) {
            log.error("获取算法信息失败", e);
            return Result.error("获取算法信息异常: " + e.getMessage());
        }
    }
}
