package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.TaskResult;
import com.iot.platform.common.mapper.TaskResultMapper;
import com.iot.platform.api.service.TaskResultService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 计算结果表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class TaskResultServiceImpl extends ServiceImpl<TaskResultMapper, TaskResult> implements TaskResultService {

}
