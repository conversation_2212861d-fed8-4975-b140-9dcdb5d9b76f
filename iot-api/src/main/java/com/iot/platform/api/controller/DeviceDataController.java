package com.iot.platform.api.controller;

import com.iot.platform.api.dto.BatchDataRequest;
import com.iot.platform.api.dto.DeviceDataRequest;
import com.iot.platform.api.service.DeviceDataService;
import com.iot.platform.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 设备数据接收控制器
 * 处理设备数据上报相关的API请求
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/data")
@RequiredArgsConstructor
@Validated
@Tag(name = "设备数据管理", description = "设备数据上报和查询相关接口")
public class DeviceDataController {

    private final DeviceDataService deviceDataService;

    @PostMapping("/report")
    @Operation(summary = "单条数据上报", description = "接收单条设备数据上报")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "数据上报成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "401", description = "认证失败"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> reportData(
            @Valid @RequestBody DeviceDataRequest request,
            @Parameter(description = "边缘程序ID", example = "edge_001")
            @RequestHeader(value = "X-Edge-Program-Id", required = false) String edgeProgramId) {

        log.info("收到设备数据上报: device={}, dataCode={}, value={}",
                request.getDeviceCode(), request.getDataCode(), request.getDataValue());

        // 设置边缘程序ID
        if (edgeProgramId != null && !edgeProgramId.trim().isEmpty()) {
            request.setEdgeProgramId(edgeProgramId);
        }

        return deviceDataService.processDeviceData(request);
    }

    @PostMapping("/batch-report")
    @Operation(summary = "批量数据上报", description = "接收批量设备数据上报，最多支持1000条数据")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "批量数据上报成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "401", description = "认证失败"),
        @ApiResponse(responseCode = "413", description = "数据量超出限制"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Map<String, Object>> batchReportData(
            @Valid @RequestBody BatchDataRequest request,
            @Parameter(description = "边缘程序ID", example = "edge_001")
            @RequestHeader(value = "X-Edge-Program-Id", required = false) String edgeProgramId) {

        log.info("收到批量设备数据上报: count={}, devices={}, batchId={}",
                request.getDataCount(), request.getDeviceCount(), request.getBatchId());

        // 设置边缘程序ID
        if (edgeProgramId != null && !edgeProgramId.trim().isEmpty()) {
            request.setEdgeProgramId(edgeProgramId);
        }

        return deviceDataService.processBatchDeviceData(request);
    }

    @GetMapping("/latest/{deviceCode}")
    @Operation(summary = "获取设备最新数据", description = "获取指定设备的最新数据")
    public Result<List<Map<String, Object>>> getLatestData(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode,
            @Parameter(description = "数据类型码，不指定则返回所有类型")
            @RequestParam(required = false) @Min(1) @Max(8) Byte dataCode) {

        log.debug("获取设备最新数据: device={}, dataCode={}", deviceCode, dataCode);

        List<Map<String, Object>> data = deviceDataService.getLatestDeviceData(deviceCode, dataCode);
        return Result.success(data);
    }

    @GetMapping("/history/{deviceCode}")
    @Operation(summary = "获取设备历史数据", description = "获取指定设备的历史数据")
    public Result<List<Map<String, Object>>> getHistoryData(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode,
            @Parameter(description = "数据类型码", required = true)
            @RequestParam @Min(1) @Max(8) Byte dataCode,
            @Parameter(description = "开始时间戳（毫秒）", required = true)
            @RequestParam Long startTime,
            @Parameter(description = "结束时间戳（毫秒）", required = true)
            @RequestParam Long endTime,
            @Parameter(description = "限制条数，默认1000")
            @RequestParam(defaultValue = "1000") @Min(1) @Max(10000) Integer limit) {

        log.debug("获取设备历史数据: device={}, dataCode={}, startTime={}, endTime={}, limit={}",
                deviceCode, dataCode, startTime, endTime, limit);

        List<Map<String, Object>> data = deviceDataService.getDeviceHistoryData(
                deviceCode, dataCode, startTime, endTime, limit);
        return Result.success(data);
    }

    @GetMapping("/statistics/{deviceCode}")
    @Operation(summary = "获取设备数据统计", description = "获取指定设备的数据统计信息")
    public Result<Map<String, Object>> getDataStatistics(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode,
            @Parameter(description = "数据类型码", required = true)
            @RequestParam @Min(1) @Max(8) Byte dataCode,
            @Parameter(description = "时间范围（小时），默认24小时")
            @RequestParam(defaultValue = "24") @Min(1) @Max(168) Integer timeRange) {

        log.debug("获取设备数据统计: device={}, dataCode={}, timeRange={}h",
                deviceCode, dataCode, timeRange);

        Map<String, Object> statistics = deviceDataService.getDeviceDataStatistics(
                deviceCode, dataCode, timeRange);
        return Result.success(statistics);
    }

    @GetMapping("/online-status/{deviceCode}")
    @Operation(summary = "检查设备在线状态", description = "检查指定设备是否在线")
    public Result<Map<String, Object>> checkOnlineStatus(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode,
            @Parameter(description = "超时分钟数，默认10分钟")
            @RequestParam(defaultValue = "10") @Min(1) @Max(1440) Integer timeoutMinutes) {

        boolean isOnline = deviceDataService.isDeviceOnline(deviceCode, timeoutMinutes);

        Map<String, Object> result = Map.of(
                "deviceCode", deviceCode,
                "isOnline", isOnline,
                "checkTime", System.currentTimeMillis(),
                "timeoutMinutes", timeoutMinutes
        );

        return Result.success(result);
    }

}
