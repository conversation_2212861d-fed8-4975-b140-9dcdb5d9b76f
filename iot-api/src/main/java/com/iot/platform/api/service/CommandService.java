package com.iot.platform.api.service;

import com.iot.platform.api.dto.CommandRequest;
import com.iot.platform.api.dto.CommandResponse;
import com.iot.platform.common.response.Result;

import java.util.List;
import java.util.Map;

/**
 * 设备指令下发服务接口
 * 负责设备控制指令的下发、状态跟踪和响应处理
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface CommandService {

    /**
     * 发送设备控制指令
     *
     * @param request 指令请求
     * @return 指令发送结果
     */
    Result<CommandResponse> sendCommand(CommandRequest request);

    /**
     * 批量发送设备控制指令
     *
     * @param requests 批量指令请求
     * @return 批量发送结果
     */
    Result<Map<String, Object>> sendBatchCommands(List<CommandRequest> requests);

    /**
     * 查询指令执行状态
     *
     * @param commandId 指令ID
     * @return 指令状态信息
     */
    Result<Map<String, Object>> getCommandStatus(String commandId);

    /**
     * 查询设备指令历史
     *
     * @param deviceCode 设备编码
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param limit 限制条数
     * @return 指令历史列表
     */
    Result<List<Map<String, Object>>> getCommandHistory(String deviceCode, Long startTime, Long endTime, Integer limit);

    /**
     * 取消指令执行
     *
     * @param commandId 指令ID
     * @return 取消结果
     */
    Result<Void> cancelCommand(String commandId);

    /**
     * 处理指令执行响应
     *
     * @param commandId 指令ID
     * @param response 执行响应
     * @return 处理结果
     */
    Result<Void> handleCommandResponse(String commandId, Map<String, Object> response);

    /**
     * 获取设备支持的指令类型
     *
     * @param deviceCode 设备编码
     * @return 支持的指令类型列表
     */
    Result<List<Map<String, Object>>> getSupportedCommands(String deviceCode);

    /**
     * 检查指令是否超时
     *
     * @param commandId 指令ID
     * @return 是否超时
     */
    boolean isCommandTimeout(String commandId);

    /**
     * 重试失败的指令
     *
     * @param commandId 指令ID
     * @return 重试结果
     */
    Result<Void> retryCommand(String commandId);

    /**
     * 获取指令统计信息
     *
     * @param deviceCode 设备编码（可选）
     * @param timeRange 时间范围（小时）
     * @return 统计信息
     */
    Map<String, Object> getCommandStatistics(String deviceCode, Integer timeRange);
}
