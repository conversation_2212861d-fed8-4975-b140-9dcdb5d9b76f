package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;

/**
 * 设备创建请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Data
@Schema(name = "DeviceCreateRequest", description = "设备创建请求")
public class DeviceCreateRequest {

    @Schema(description = "项目ID", example = "demo_project_001")
    @NotBlank(message = "项目ID不能为空")
    @Size(max = 50, message = "项目ID长度不能超过50个字符")
    private String projectId;

    @Schema(description = "设备编码", example = "TEMP_001")
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 50, message = "设备编码长度不能超过50个字符")
    @Pattern(regexp = "^[A-Z0-9_]+$", message = "设备编码只能包含大写字母、数字和下划线")
    private String deviceCode;

    @Schema(description = "设备名称", example = "温度传感器01")
    @NotBlank(message = "设备名称不能为空")
    @Size(max = 100, message = "设备名称长度不能超过100个字符")
    private String deviceName;

    @Schema(description = "设备类型", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"})
    @NotNull(message = "设备类型不能为空")
    private Byte deviceType;

    @Schema(description = "厂商", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"})
    @NotNull(message = "厂商不能为空")
    private Byte vendor;

    @Schema(description = "型号", example = "DS18B20")
    @Size(max = 50, message = "型号长度不能超过50个字符")
    private String model;

    @Schema(description = "固件版本", example = "v1.0.0")
    @Size(max = 20, message = "固件版本长度不能超过20个字符")
    private String firmwareVersion;

    @Schema(description = "MAC地址", example = "00:11:22:33:44:55")
    @Pattern(regexp = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", message = "MAC地址格式不正确")
    private String macAddress;

    @Schema(description = "IP地址", example = "*************")
    @Pattern(regexp = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$", message = "IP地址格式不正确")
    private String ipAddress;

    @Schema(description = "位置信息", example = "车间A-1号位置")
    @Size(max = 200, message = "位置信息长度不能超过200个字符")
    private String location;

    @Schema(description = "边缘程序ID", example = "edge_001")
    @Size(max = 50, message = "边缘程序ID长度不能超过50个字符")
    private String edgeProgramId;

    @Schema(description = "设备描述", example = "用于监测车间温度的传感器")
    @Size(max = 500, message = "设备描述长度不能超过500个字符")
    private String description;
}
