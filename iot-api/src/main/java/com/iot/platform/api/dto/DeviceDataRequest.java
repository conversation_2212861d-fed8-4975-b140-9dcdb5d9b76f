package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 设备数据上报请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Schema(description = "设备数据上报请求")
public class DeviceDataRequest {

    @Schema(description = "项目ID", example = "project_001", required = true)
    @NotBlank(message = "项目ID不能为空")
    @Size(max = 50, message = "项目ID长度不能超过50个字符")
    private String projectId;

    @Schema(description = "设备编码", example = "DEVICE_001", required = true)
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 100, message = "设备编码长度不能超过100个字符")
    private String deviceCode;

    @Schema(description = "数据类型码：1-温度,2-湿度,3-PM2.5,4-二氧化碳,5-人体感应,6-光照强度,7-占用状态,8-噪音分贝",
            example = "1", required = true)
    @NotNull(message = "数据类型码不能为空")
    @Min(value = 1, message = "数据类型码最小值为1")
    @Max(value = 8, message = "数据类型码最大值为8")
    private Byte dataCode;

    @Schema(description = "数据值", example = "25.6", required = true)
    @NotNull(message = "数据值不能为空")
    @DecimalMin(value = "-999999.999999", message = "数据值超出范围")
    @DecimalMax(value = "999999.999999", message = "数据值超出范围")
    private BigDecimal dataValue;

    @Schema(description = "数据质量等级：1-优秀,2-良好,3-一般,4-较差", example = "1")
    @Min(value = 1, message = "数据质量等级最小值为1")
    @Max(value = 4, message = "数据质量等级最大值为4")
    private Byte quality = 1;

    @Schema(description = "设备上报时间戳（毫秒）", example = "1703664000000", required = true)
    @NotNull(message = "上报时间戳不能为空")
    @Positive(message = "上报时间戳必须为正数")
    private Long reportTime;

    @Schema(description = "边缘程序ID", example = "edge_001")
    @Size(max = 100, message = "边缘程序ID长度不能超过100个字符")
    private String edgeProgramId;

    @Schema(description = "扩展数据（JSON格式）", example = "{\"location\":\"room_101\"}")
    @Size(max = 1000, message = "扩展数据长度不能超过1000个字符")
    private String extData;

    /**
     * 验证数据值是否在合理范围内
     */
    public boolean isDataValueValid() {
        if (dataValue == null) {
            return false;
        }

        // 根据数据类型码验证数据值范围
        return switch (dataCode) {
            case 1 -> dataValue.compareTo(new BigDecimal("-50")) >= 0 && dataValue.compareTo(new BigDecimal("100")) <= 0; // 温度
            case 2 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(new BigDecimal("100")) <= 0; // 湿度
            case 3 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(new BigDecimal("1000")) <= 0; // PM2.5
            case 4 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(new BigDecimal("5000")) <= 0; // CO2
            case 5, 7 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(BigDecimal.ONE) <= 0; // 人体感应、占用状态
            case 6 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(new BigDecimal("100000")) <= 0; // 光照强度
            case 8 -> dataValue.compareTo(BigDecimal.ZERO) >= 0 && dataValue.compareTo(new BigDecimal("120")) <= 0; // 噪音分贝
            default -> false;
        };
    }

    /**
     * 获取批次ID（兼容性方法）
     */
    public String getBatchId() {
        return null; // DeviceDataRequest本身不包含批次ID，由BatchDataRequest管理
    }

    /**
     * 获取时间戳（兼容性方法）
     */
    public Long getTimestamp() {
        return reportTime; // 使用reportTime作为时间戳
    }

    /**
     * 获取数据类型描述
     */
    public String getDataTypeDescription() {
        return switch (dataCode) {
            case 1 -> "温度";
            case 2 -> "湿度";
            case 3 -> "PM2.5";
            case 4 -> "二氧化碳";
            case 5 -> "人体感应";
            case 6 -> "光照强度";
            case 7 -> "占用状态";
            case 8 -> "噪音分贝";
            default -> "未知";
        };
    }
}
