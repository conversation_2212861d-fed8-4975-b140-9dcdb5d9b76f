package com.iot.platform.api.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * Swagger API文档配置
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Configuration
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("IoT大数据平台API")
                        .description("自主可控的IoT大数据平台，支持多厂商设备数据采集、处理、展示和远程控制")
                        .version("1.0.0")
                        .contact(new Contact()
                                .name("IoT Platform Team")
                                .email("<EMAIL>")
                                .url("https://github.com/iot-platform"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://www.apache.org/licenses/LICENSE-2.0")))
                .servers(List.of(
                        new Server().url("http://localhost:8000/api").description("开发环境"),
                        new Server().url("https://iot.tenants.link/api").description("生产环境")))
                .addSecurityItem(new SecurityRequirement().addList("AKSK认证"))
                .components(new io.swagger.v3.oas.models.Components()
                        .addSecuritySchemes("AKSK认证", new SecurityScheme()
                                .type(SecurityScheme.Type.APIKEY)
                                .in(SecurityScheme.In.HEADER)
                                .name("X-Access-Key")
                                .description("使用Access Key进行API认证，需要配合Secret Key生成签名")));
    }
}
