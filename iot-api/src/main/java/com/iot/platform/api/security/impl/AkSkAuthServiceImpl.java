package com.iot.platform.api.security.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iot.platform.common.entity.ApiAuth;
import com.iot.platform.api.exception.BusinessException;
import com.iot.platform.common.mapper.ApiAuthMapper;
import com.iot.platform.api.security.AkSkAuthService;
import com.iot.platform.common.response.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Base64;

/**
 * AK/SK认证服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AkSkAuthServiceImpl implements AkSkAuthService {

    private final ApiAuthMapper apiAuthMapper;

    private static final String HMAC_SHA256 = "HmacSHA256";

    @Override
    public ApiAuth validateSignature(HttpServletRequest request) {
        try {
            // 1. 获取请求头中的认证信息
            String accessKey = request.getHeader("X-Access-Key");
            String signature = request.getHeader("X-Signature");
            String timestamp = request.getHeader("X-Timestamp");

            if (!StringUtils.hasText(accessKey) || !StringUtils.hasText(signature) || !StringUtils.hasText(timestamp)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "缺少必要的认证头信息");
            }

            // 2. 验证时间戳
            if (!isTimestampValid(timestamp, 300L)) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "请求时间戳无效或已过期");
            }

            // 3. 根据AccessKey获取API认证信息
            ApiAuth apiAuth = getApiAuthByAccessKey(accessKey);
            if (apiAuth == null) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "Access Key不存在");
            }

            // 4. 检查API认证状态
            if (apiAuth.getStatus() == null || apiAuth.getStatus() != 1) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "API认证已禁用");
            }

            // 5. 检查过期时间
            if (apiAuth.getExpiresAt() != null && apiAuth.getExpiresAt().isBefore(LocalDateTime.now())) {
                throw new BusinessException(ResultCode.UNAUTHORIZED, "API认证已过期");
            }

            // 6. 获取请求体
            String requestBody = getRequestBody(request);

            // 7. 生成期望的签名
            String expectedSignature = generateSignature(
                request.getMethod(),
                request.getRequestURI(),
                timestamp,
                requestBody,
                apiAuth.getSecretKey()
            );

            // 8. 验证签名
            if (!signature.equals(expectedSignature)) {
                log.warn("签名验证失败 - AccessKey: {}, Expected: {}, Actual: {}",
                    accessKey, expectedSignature, signature);
                throw new BusinessException(ResultCode.UNAUTHORIZED, "签名验证失败");
            }

            // 9. 更新使用统计
            updateUsageStatistics(apiAuth);

            log.info("API认证成功 - AccessKey: {}, AppName: {}", accessKey, apiAuth.getAppName());
            return apiAuth;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("API认证过程发生异常", e);
            throw new BusinessException(ResultCode.UNAUTHORIZED, "认证过程发生异常");
        }
    }

    @Override
    public ApiAuth getApiAuthByAccessKey(String accessKey) {
        LambdaQueryWrapper<ApiAuth> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ApiAuth::getAccessKey, accessKey);
        return apiAuthMapper.selectOne(queryWrapper);
    }

    @Override
    public String generateSignature(String method, String uri, String timestamp, String requestBody, String secretKey) {
        try {
            // 构建待签名字符串：HTTP方法 + URI + 时间戳 + 请求体
            StringBuilder stringToSign = new StringBuilder();
            stringToSign.append(method.toUpperCase()).append("\n");
            stringToSign.append(uri).append("\n");
            stringToSign.append(timestamp).append("\n");
            if (StringUtils.hasText(requestBody)) {
                stringToSign.append(requestBody);
            }

            // 使用HMAC-SHA256算法生成签名
            Mac mac = Mac.getInstance(HMAC_SHA256);
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), HMAC_SHA256);
            mac.init(secretKeySpec);

            byte[] signatureBytes = mac.doFinal(stringToSign.toString().getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(signatureBytes);

                 } catch (NoSuchAlgorithmException | InvalidKeyException e) {
             log.error("生成签名时发生异常", e);
             throw new BusinessException(ResultCode.ERROR, "签名生成失败");
         }
    }

    @Override
    public boolean isTimestampValid(String timestamp, long timeoutSeconds) {
        try {
            long requestTime = Long.parseLong(timestamp);
            long currentTime = System.currentTimeMillis() / 1000;
            return Math.abs(currentTime - requestTime) <= timeoutSeconds;
        } catch (NumberFormatException e) {
            log.warn("时间戳格式错误: {}", timestamp);
            return false;
        }
    }

    @Override
    public void updateUsageStatistics(ApiAuth apiAuth) {
        try {
            apiAuth.setLastUsedTime(System.currentTimeMillis());
            apiAuth.setUsageCount((apiAuth.getUsageCount() == null ? 0 : apiAuth.getUsageCount()) + 1);
            apiAuthMapper.updateById(apiAuth);
        } catch (Exception e) {
            log.error("更新API使用统计失败 - AccessKey: {}", apiAuth.getAccessKey(), e);
            // 统计更新失败不影响主流程，只记录日志
        }
    }

    /**
     * 获取请求体内容
     */
    private String getRequestBody(HttpServletRequest request) {
        try {
            StringBuilder requestBody = new StringBuilder();
            BufferedReader reader = request.getReader();
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
            return requestBody.toString();
        } catch (IOException e) {
            log.warn("读取请求体失败", e);
            return "";
        }
    }
}
