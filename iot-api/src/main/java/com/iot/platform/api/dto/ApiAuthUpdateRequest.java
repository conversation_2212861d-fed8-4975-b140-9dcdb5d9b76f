package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Future;
import java.time.LocalDateTime;

/**
 * API认证更新请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Data
@Schema(name = "ApiAuthUpdateRequest", description = "API认证更新请求")
public class ApiAuthUpdateRequest {

    @Schema(description = "应用名称", example = "IoT数据采集器")
    @Size(max = 100, message = "应用名称长度不能超过100个字符")
    private String appName;

    @Schema(description = "权限配置（JSON格式）", example = "{\"data\": [\"read\", \"write\"], \"device\": [\"read\"]}")
    @Size(max = 2000, message = "权限配置长度不能超过2000个字符")
    private String permissions;

    @Schema(description = "状态", example = "1", allowableValues = {"0", "1"})
    private Byte status;

    @Schema(description = "过期时间", example = "2025-12-31T23:59:59")
    @Future(message = "过期时间必须是未来时间")
    private LocalDateTime expiresAt;
}
