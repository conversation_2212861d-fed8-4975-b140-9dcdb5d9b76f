package com.iot.platform.api.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.iot.platform.api.dto.DeviceCreateRequest;
import com.iot.platform.api.dto.DeviceQueryRequest;
import com.iot.platform.api.dto.DeviceUpdateRequest;
import com.iot.platform.api.service.DeviceService;
import com.iot.platform.common.entity.Device;
import com.iot.platform.common.enums.DeviceStatus;
import com.iot.platform.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 设备管理控制器
 * 处理设备信息的CRUD操作
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/device")
@RequiredArgsConstructor
@Validated
@Tag(name = "设备管理", description = "设备信息的增删改查和状态管理")
public class DeviceController {

    private final DeviceService deviceService;

    @PostMapping
    @Operation(summary = "创建设备", description = "创建新的设备信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "创建成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "409", description = "设备编码已存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Device> createDevice(@Valid @RequestBody DeviceCreateRequest request) {
        log.info("创建设备: deviceCode={}, deviceName={}", request.getDeviceCode(), request.getDeviceName());

        // 检查设备编码是否已存在
        if (deviceService.isDeviceCodeExists(request.getDeviceCode())) {
            return Result.error("设备编码已存在: " + request.getDeviceCode());
        }

        // 创建设备对象
        Device device = new Device();
        BeanUtils.copyProperties(request, device);
        device.setStatus((byte) 2); // 默认离线状态
        device.setCreatedAt(LocalDateTime.now());
        device.setUpdatedAt(LocalDateTime.now());

        // 保存设备
        boolean success = deviceService.save(device);
        if (success) {
            log.info("设备创建成功: id={}, deviceCode={}", device.getId(), device.getDeviceCode());
            return Result.success(device);
        } else {
            log.error("设备创建失败: deviceCode={}", request.getDeviceCode());
            return Result.error("设备创建失败");
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取设备", description = "根据设备ID获取设备详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "设备不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Device> getDeviceById(
            @Parameter(description = "设备ID", required = true)
            @PathVariable @NotNull Long id) {

        log.debug("根据ID获取设备: id={}", id);

        Device device = deviceService.getById(id);
        if (device != null) {
            return Result.success(device);
        } else {
            return Result.error("设备不存在");
        }
    }

    @GetMapping("/code/{deviceCode}")
    @Operation(summary = "根据设备编码获取设备", description = "根据设备编码获取设备详细信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "设备不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Device> getDeviceByCode(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode) {

        log.debug("根据设备编码获取设备: deviceCode={}", deviceCode);

        Device device = deviceService.getByDeviceCode(deviceCode);
        if (device != null) {
            return Result.success(device);
        } else {
            return Result.error("设备不存在");
        }
    }

    @GetMapping("/list")
    @Operation(summary = "分页查询设备列表", description = "根据条件分页查询设备列表")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<IPage<Device>> getDeviceList(@Valid DeviceQueryRequest request) {
        log.debug("分页查询设备列表: {}", request);

        // 构建查询条件
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();

        if (request.getProjectId() != null && !request.getProjectId().trim().isEmpty()) {
            queryWrapper.eq("project_id", request.getProjectId());
        }
        if (request.getDeviceCode() != null && !request.getDeviceCode().trim().isEmpty()) {
            queryWrapper.eq("device_code", request.getDeviceCode());
        }
        if (request.getDeviceName() != null && !request.getDeviceName().trim().isEmpty()) {
            queryWrapper.like("device_name", request.getDeviceName());
        }
        if (request.getDeviceType() != null) {
            queryWrapper.eq("device_type", request.getDeviceType());
        }
        if (request.getVendor() != null) {
            queryWrapper.eq("vendor", request.getVendor());
        }
        if (request.getStatus() != null) {
            queryWrapper.eq("status", request.getStatus());
        }
        if (request.getEdgeProgramId() != null && !request.getEdgeProgramId().trim().isEmpty()) {
            queryWrapper.eq("edge_program_id", request.getEdgeProgramId());
        }
        if (request.getLocation() != null && !request.getLocation().trim().isEmpty()) {
            queryWrapper.like("location", request.getLocation());
        }

        // 排序
        if ("asc".equalsIgnoreCase(request.getSortOrder())) {
            queryWrapper.orderByAsc(request.getSortField());
        } else {
            queryWrapper.orderByDesc(request.getSortField());
        }

        // 分页查询
        Page<Device> page = new Page<>(request.getPageNum(), request.getPageSize());
        IPage<Device> result = deviceService.page(page, queryWrapper);

        return Result.success(result);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新设备信息", description = "根据设备ID更新设备信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "更新成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "设备不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Device> updateDevice(
            @Parameter(description = "设备ID", required = true)
            @PathVariable @NotNull Long id,
            @Valid @RequestBody DeviceUpdateRequest request) {

        log.info("更新设备信息: id={}, request={}", id, request);

        // 检查设备是否存在
        Device existingDevice = deviceService.getById(id);
        if (existingDevice == null) {
            return Result.error("设备不存在");
        }

        // 更新设备信息
        BeanUtils.copyProperties(request, existingDevice, "id", "projectId", "deviceCode", "createdAt");
        existingDevice.setUpdatedAt(LocalDateTime.now());

        boolean success = deviceService.updateById(existingDevice);
        if (success) {
            log.info("设备更新成功: id={}, deviceCode={}", id, existingDevice.getDeviceCode());
            return Result.success(existingDevice);
        } else {
            log.error("设备更新失败: id={}", id);
            return Result.error("设备更新失败");
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除设备", description = "根据设备ID删除设备")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "设备不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> deleteDevice(
            @Parameter(description = "设备ID", required = true)
            @PathVariable @NotNull Long id) {

        log.info("删除设备: id={}", id);

        // 检查设备是否存在
        Device existingDevice = deviceService.getById(id);
        if (existingDevice == null) {
            return Result.error("设备不存在");
        }

        boolean success = deviceService.removeById(id);
        if (success) {
            log.info("设备删除成功: id={}, deviceCode={}", id, existingDevice.getDeviceCode());
            return Result.success();
        } else {
            log.error("设备删除失败: id={}", id);
            return Result.error("设备删除失败");
        }
    }

    @PutMapping("/{deviceCode}/status")
    @Operation(summary = "更新设备状态", description = "更新指定设备的状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "状态更新成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "404", description = "设备不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> updateDeviceStatus(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode,
            @Parameter(description = "设备状态", required = true)
            @RequestParam @NotNull Byte status) {

        log.info("更新设备状态: deviceCode={}, status={}", deviceCode, status);

        // 验证状态值
        DeviceStatus deviceStatus;
        try {
            deviceStatus = DeviceStatus.fromCode(status).orElse(null);
            if (deviceStatus == null) {
                return Result.error("无效的设备状态: " + status);
            }
        } catch (Exception e) {
            return Result.error("无效的设备状态: " + status);
        }

        boolean success = deviceService.updateDeviceStatus(deviceCode, deviceStatus);
        if (success) {
            log.info("设备状态更新成功: deviceCode={}, status={}", deviceCode, deviceStatus.getDescription());
            return Result.success();
        } else {
            log.error("设备状态更新失败: deviceCode={}, status={}", deviceCode, status);
            return Result.error("设备状态更新失败，设备可能不存在");
        }
    }

    @GetMapping("/project/{projectId}")
    @Operation(summary = "根据项目ID获取设备列表", description = "获取指定项目下的所有设备")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<List<Device>> getDevicesByProject(
            @Parameter(description = "项目ID", required = true)
            @PathVariable @NotBlank String projectId) {

        log.debug("根据项目ID获取设备列表: projectId={}", projectId);

        List<Device> devices = deviceService.getDevicesByProjectId(projectId);
        return Result.success(devices);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取设备统计信息", description = "获取设备的统计信息")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Map<String, Object>> getDeviceStatistics(
            @Parameter(description = "项目ID（可选）")
            @RequestParam(required = false) String projectId) {

        log.debug("获取设备统计信息: projectId={}", projectId);

        // 构建查询条件
        QueryWrapper<Device> queryWrapper = new QueryWrapper<>();
        if (projectId != null && !projectId.trim().isEmpty()) {
            queryWrapper.eq("project_id", projectId);
        }

        // 总设备数
        long totalCount = deviceService.count(queryWrapper);

        // 在线设备数
        long onlineCount = deviceService.getOnlineDeviceCount(projectId);

        // 离线设备数
        long offlineCount = totalCount - onlineCount;

        // 按类型统计
        QueryWrapper<Device> typeWrapper = new QueryWrapper<>();
        if (projectId != null && !projectId.trim().isEmpty()) {
            typeWrapper.eq("project_id", projectId);
        }
        typeWrapper.select("device_type", "COUNT(*) as count")
                   .groupBy("device_type");
        List<Map<String, Object>> typeStats = deviceService.listMaps(typeWrapper);

        // 按厂商统计
        QueryWrapper<Device> vendorWrapper = new QueryWrapper<>();
        if (projectId != null && !projectId.trim().isEmpty()) {
            vendorWrapper.eq("project_id", projectId);
        }
        vendorWrapper.select("vendor", "COUNT(*) as count")
                     .groupBy("vendor");
        List<Map<String, Object>> vendorStats = deviceService.listMaps(vendorWrapper);

        Map<String, Object> statistics = Map.of(
            "totalCount", totalCount,
            "onlineCount", onlineCount,
            "offlineCount", offlineCount,
            "typeStatistics", typeStats,
            "vendorStatistics", vendorStats
        );

        return Result.success(statistics);
    }
}
