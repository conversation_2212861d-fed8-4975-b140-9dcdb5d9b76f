package com.iot.platform.api.controller;

import com.iot.platform.api.dto.CommandRequest;
import com.iot.platform.api.dto.CommandResponse;
import com.iot.platform.api.service.CommandService;
import com.iot.platform.common.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 设备控制指令控制器
 * 负责处理设备控制指令相关的API接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@RestController
@RequestMapping("/command")
@RequiredArgsConstructor
@Validated
@Tag(name = "设备控制管理", description = "设备控制指令下发和状态查询相关接口")
public class CommandController {

    private final CommandService commandService;

    @PostMapping("/send")
    @Operation(summary = "发送设备控制指令", description = "向指定设备发送控制指令")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "指令发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "401", description = "认证失败"),
        @ApiResponse(responseCode = "404", description = "设备不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<CommandResponse> sendCommand(@Valid @RequestBody CommandRequest request) {
        log.info("发送设备控制指令: device={}, command={}, priority={}",
                request.getDeviceCode(), request.getCommandType(), request.getPriorityDescription());

        return commandService.sendCommand(request);
    }

    @PostMapping("/batch-send")
    @Operation(summary = "批量发送设备控制指令", description = "批量向多个设备发送控制指令")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "批量指令发送成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "401", description = "认证失败"),
        @ApiResponse(responseCode = "413", description = "指令数量超出限制"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Map<String, Object>> sendBatchCommands(
            @Valid @RequestBody @Size(max = 100, message = "单次批量指令不能超过100条")
            List<CommandRequest> requests) {

        log.info("批量发送设备控制指令: count={}", requests.size());

        return commandService.sendBatchCommands(requests);
    }

    @GetMapping("/status/{commandId}")
    @Operation(summary = "查询指令执行状态", description = "根据指令ID查询指令执行状态")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "404", description = "指令不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Map<String, Object>> getCommandStatus(
            @Parameter(description = "指令ID", required = true)
            @PathVariable @NotBlank String commandId) {

        log.debug("查询指令执行状态: commandId={}", commandId);

        return commandService.getCommandStatus(commandId);
    }

    @GetMapping("/history/{deviceCode}")
    @Operation(summary = "查询设备指令历史", description = "查询指定设备的指令执行历史")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "查询成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<List<Map<String, Object>>> getCommandHistory(
            @Parameter(description = "设备编码", required = true)
            @PathVariable @NotBlank String deviceCode,
            @Parameter(description = "开始时间戳（毫秒）")
            @RequestParam(required = false) Long startTime,
            @Parameter(description = "结束时间戳（毫秒）")
            @RequestParam(required = false) Long endTime,
            @Parameter(description = "限制条数，默认100")
            @RequestParam(defaultValue = "100") Integer limit) {

        log.debug("查询设备指令历史: device={}, startTime={}, endTime={}, limit={}",
                deviceCode, startTime, endTime, limit);

        return commandService.getCommandHistory(deviceCode, startTime, endTime, limit);
    }

    @PostMapping("/cancel/{commandId}")
    @Operation(summary = "取消指令执行", description = "取消指定的待执行指令")
    @ApiResponses({
        @ApiResponse(responseCode = "200", description = "指令取消成功"),
        @ApiResponse(responseCode = "400", description = "指令状态不允许取消"),
        @ApiResponse(responseCode = "404", description = "指令不存在"),
        @ApiResponse(responseCode = "500", description = "服务器内部错误")
    })
    public Result<Void> cancelCommand(
            @Parameter(description = "指令ID", required = true)
            @PathVariable @NotBlank String commandId,
            @Parameter(description = "取消原因")
            @RequestParam(required = false) String reason) {

        log.info("取消指令执行: commandId={}, reason={}", commandId, reason);

        // TODO: 实现指令取消逻辑
        return Result.success("指令取消成功");
    }
}
