package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.SystemConfig;
import com.iot.platform.common.mapper.SystemConfigMapper;
import com.iot.platform.api.service.SystemConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 系统配置表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class SystemConfigServiceImpl extends ServiceImpl<SystemConfigMapper, SystemConfig> implements SystemConfigService {

}
