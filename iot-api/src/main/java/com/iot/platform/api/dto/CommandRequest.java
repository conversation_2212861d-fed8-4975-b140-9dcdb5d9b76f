package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.util.Map;

/**
 * 设备控制指令请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Schema(description = "设备控制指令请求")
public class CommandRequest {

    @Schema(description = "设备编码", example = "DEVICE_001", required = true)
    @NotBlank(message = "设备编码不能为空")
    @Size(max = 100, message = "设备编码长度不能超过100个字符")
    private String deviceCode;

    @Schema(description = "指令类型", example = "SET_TEMPERATURE", required = true)
    @NotBlank(message = "指令类型不能为空")
    @Size(max = 50, message = "指令类型长度不能超过50个字符")
    private String commandType;

    @Schema(description = "指令参数", example = "{\"temperature\": 25, \"mode\": \"auto\"}")
    private Map<String, Object> parameters;

    @Schema(description = "指令优先级：1-低,2-中,3-高,4-紧急", example = "2")
    @Min(value = 1, message = "指令优先级最小值为1")
    @Max(value = 4, message = "指令优先级最大值为4")
    private Integer priority = 2;

    @Schema(description = "超时时间（秒）", example = "30")
    @Min(value = 1, message = "超时时间最小值为1秒")
    @Max(value = 300, message = "超时时间最大值为300秒")
    private Integer timeout = 30;

    @Schema(description = "是否需要响应确认", example = "true")
    private Boolean requireResponse = true;

    @Schema(description = "重试次数", example = "3")
    @Min(value = 0, message = "重试次数最小值为0")
    @Max(value = 5, message = "重试次数最大值为5")
    private Integer retryCount = 3;

    @Schema(description = "项目ID", example = "project_001")
    @Size(max = 50, message = "项目ID长度不能超过50个字符")
    private String projectId;

    @Schema(description = "操作用户ID", example = "user_001")
    @Size(max = 50, message = "操作用户ID长度不能超过50个字符")
    private String operatorId;

    @Schema(description = "指令描述", example = "设置空调温度为25度")
    @Size(max = 200, message = "指令描述长度不能超过200个字符")
    private String description;

    /**
     * 验证指令参数
     */
    public boolean isParametersValid() {
        if (parameters == null || parameters.isEmpty()) {
            return true; // 允许空参数
        }

        // 验证参数数量
        if (parameters.size() > 20) {
            return false; // 参数数量不能超过20个
        }

        // 验证参数键值
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 键不能为空且长度不能超过50
            if (key == null || key.trim().isEmpty() || key.length() > 50) {
                return false;
            }

            // 值不能为null
            if (value == null) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取指令优先级描述
     */
    public String getPriorityDescription() {
        return switch (priority) {
            case 1 -> "低";
            case 2 -> "中";
            case 3 -> "高";
            case 4 -> "紧急";
            default -> "未知";
        };
    }
}
