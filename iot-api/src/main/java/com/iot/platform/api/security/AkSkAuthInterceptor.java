package com.iot.platform.api.security;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.iot.platform.api.config.AkSkProperties;
import com.iot.platform.common.entity.ApiAuth;
import com.iot.platform.api.exception.BusinessException;
import com.iot.platform.common.response.Result;
import com.iot.platform.common.response.ResultCode;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * AK/SK认证拦截器
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AkSkAuthInterceptor implements HandlerInterceptor {

    private final AkSkProperties akSkProperties;
    private final AkSkAuthService akSkAuthService;
    private final ObjectMapper objectMapper;

    private static final AntPathMatcher PATH_MATCHER = new AntPathMatcher();
    private static final String API_AUTH_ATTRIBUTE = "API_AUTH";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 1. 检查是否启用AK/SK认证
        if (!akSkProperties.getEnabled()) {
            log.debug("AK/SK认证已禁用，跳过认证");
            return true;
        }

        // 2. 检查是否为跳过认证的路径
        String requestPath = request.getRequestURI();
        if (isSkipPath(requestPath)) {
            log.debug("跳过认证路径: {}", requestPath);
            return true;
        }

        // 3. 检查是否为OPTIONS请求
        if ("OPTIONS".equals(request.getMethod())) {
            return true;
        }

        try {
            // 4. 进行AK/SK认证
            ApiAuth apiAuth = akSkAuthService.validateSignature(request);

            // 5. 将认证信息存储到请求属性中，供后续使用
            request.setAttribute(API_AUTH_ATTRIBUTE, apiAuth);

            log.debug("API认证成功 - URI: {}, AccessKey: {}, AppName: {}",
                requestPath, apiAuth.getAccessKey(), apiAuth.getAppName());

            return true;

        } catch (BusinessException e) {
            log.warn("API认证失败 - URI: {}, 原因: {}", requestPath, e.getMessage());
            writeErrorResponse(response, e.getCode(), e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("API认证过程发生异常 - URI: {}", requestPath, e);
            writeErrorResponse(response, ResultCode.UNAUTHORIZED.getCode(), "认证失败");
            return false;
        }
    }

    /**
     * 检查是否为跳过认证的路径
     */
    private boolean isSkipPath(String requestPath) {
        if (akSkProperties.getSkipPaths() == null) {
            return false;
        }

        for (String skipPath : akSkProperties.getSkipPaths()) {
            if (PATH_MATCHER.match(skipPath, requestPath)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, Integer code, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        Result<Object> result = Result.error(code, message);
        String responseBody = objectMapper.writeValueAsString(result);
        response.getWriter().write(responseBody);
    }

    /**
     * 从请求中获取API认证信息
     */
    public static ApiAuth getApiAuth(HttpServletRequest request) {
        return (ApiAuth) request.getAttribute(API_AUTH_ATTRIBUTE);
    }
}
