package com.iot.platform.api.service.impl;

import com.iot.platform.common.entity.ApiAuth;
import com.iot.platform.common.mapper.ApiAuthMapper;
import com.iot.platform.api.service.ApiAuthService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * API认证表 服务实现类
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Service
public class ApiAuthServiceImpl extends ServiceImpl<ApiAuthMapper, ApiAuth> implements ApiAuthService {

}
