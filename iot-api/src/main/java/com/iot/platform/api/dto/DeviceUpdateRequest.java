package com.iot.platform.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Pattern;

/**
 * 设备更新请求DTO
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-30
 */
@Data
@Schema(name = "DeviceUpdateRequest", description = "设备更新请求")
public class DeviceUpdateRequest {

    @Schema(description = "设备名称", example = "温度传感器01")
    @Size(max = 100, message = "设备名称长度不能超过100个字符")
    private String deviceName;

    @Schema(description = "设备类型", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"})
    private Byte deviceType;

    @Schema(description = "厂商", example = "1", allowableValues = {"1", "2", "3", "4", "5", "6", "7", "8"})
    private Byte vendor;

    @Schema(description = "型号", example = "DS18B20")
    @Size(max = 50, message = "型号长度不能超过50个字符")
    private String model;

    @Schema(description = "固件版本", example = "v1.0.1")
    @Size(max = 20, message = "固件版本长度不能超过20个字符")
    private String firmwareVersion;

    @Schema(description = "MAC地址", example = "00:11:22:33:44:55")
    @Pattern(regexp = "^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$", message = "MAC地址格式不正确")
    private String macAddress;

    @Schema(description = "IP地址", example = "*************")
    @Pattern(regexp = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$", message = "IP地址格式不正确")
    private String ipAddress;

    @Schema(description = "位置信息", example = "车间A-1号位置")
    @Size(max = 200, message = "位置信息长度不能超过200个字符")
    private String location;

    @Schema(description = "设备状态", example = "1", allowableValues = {"1", "2", "3"})
    private Byte status;

    @Schema(description = "边缘程序ID", example = "edge_001")
    @Size(max = 50, message = "边缘程序ID长度不能超过50个字符")
    private String edgeProgramId;

    @Schema(description = "设备描述", example = "用于监测车间温度的传感器")
    @Size(max = 500, message = "设备描述长度不能超过500个字符")
    private String description;
}
