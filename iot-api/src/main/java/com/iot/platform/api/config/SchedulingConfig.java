package com.iot.platform.api.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 任务调度配置类
 * 配置定时任务线程池和调度器
 * 用于计算任务的定时执行
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "iot.scheduling")
@Data
public class SchedulingConfig {

    /**
     * 核心线程数
     */
    private int corePoolSize = 10;

    /**
     * 最大线程数
     */
    private int maxPoolSize = 50;

    /**
     * 队列容量
     */
    private int queueCapacity = 200;

    /**
     * 线程空闲时间（秒）
     */
    private int keepAliveSeconds = 60;

    /**
     * 线程名前缀
     */
    private String threadNamePrefix = "IoT-Scheduler-";

    /**
     * 等待任务完成时间（秒）
     */
    private int awaitTerminationSeconds = 60;

    /**
     * 是否等待任务完成后关闭
     */
    private boolean waitForTasksToCompleteOnShutdown = true;

    /**
     * 任务调度器配置
     */
    @Bean("taskScheduler")
    public ThreadPoolTaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();

        // 基本配置
        scheduler.setPoolSize(corePoolSize);
        scheduler.setThreadNamePrefix(threadNamePrefix);
        scheduler.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        scheduler.setAwaitTerminationSeconds(awaitTerminationSeconds);

        // 拒绝策略：调用者运行
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 初始化
        scheduler.initialize();

        return scheduler;
    }

    /**
     * 计算任务专用调度器
     */
    @Bean("computeTaskScheduler")
    public ThreadPoolTaskScheduler computeTaskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();

        // 计算任务需要更多线程
        scheduler.setPoolSize(maxPoolSize);
        scheduler.setThreadNamePrefix("IoT-Compute-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(120); // 计算任务可能需要更长时间

        // 拒绝策略：丢弃最老的任务
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardOldestPolicy());

        scheduler.initialize();

        return scheduler;
    }

    /**
     * 数据处理专用调度器
     */
    @Bean("dataProcessScheduler")
    public ThreadPoolTaskScheduler dataProcessScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();

        // 数据处理任务配置
        scheduler.setPoolSize(20);
        scheduler.setThreadNamePrefix("IoT-DataProcess-");
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        scheduler.setAwaitTerminationSeconds(30);

        // 拒绝策略：阻塞等待
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        scheduler.initialize();

        return scheduler;
    }

    /**
     * 心跳检测专用调度器
     */
    @Bean("heartbeatScheduler")
    public ThreadPoolTaskScheduler heartbeatScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();

        // 心跳检测任务配置
        scheduler.setPoolSize(5);
        scheduler.setThreadNamePrefix("IoT-Heartbeat-");
        scheduler.setWaitForTasksToCompleteOnShutdown(false); // 心跳任务可以立即停止
        scheduler.setAwaitTerminationSeconds(10);

        // 拒绝策略：直接丢弃
        scheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.DiscardPolicy());

        scheduler.initialize();

        return scheduler;
    }
}
