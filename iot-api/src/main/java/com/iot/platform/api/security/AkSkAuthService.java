package com.iot.platform.api.security;

import com.iot.platform.common.entity.ApiAuth;

import jakarta.servlet.http.HttpServletRequest;

/**
 * AK/SK认证服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface AkSkAuthService {

    /**
     * 验证请求签名
     *
     * @param request HTTP请求
     * @return 认证结果，成功返回ApiAuth对象，失败抛出异常
     */
    ApiAuth validateSignature(HttpServletRequest request);

    /**
     * 根据Access Key获取API认证信息
     *
     * @param accessKey Access Key
     * @return API认证信息
     */
    ApiAuth getApiAuthByAccessKey(String accessKey);

    /**
     * 生成签名
     *
     * @param method HTTP方法
     * @param uri 请求URI
     * @param timestamp 时间戳
     * @param requestBody 请求体
     * @param secretKey Secret Key
     * @return 签名字符串
     */
    String generateSignature(String method, String uri, String timestamp, String requestBody, String secretKey);

    /**
     * 验证时间戳是否有效
     *
     * @param timestamp 时间戳字符串
     * @param timeoutSeconds 超时秒数
     * @return 是否有效
     */
    boolean isTimestampValid(String timestamp, long timeoutSeconds);

    /**
     * 更新API使用统计
     *
     * @param apiAuth API认证信息
     */
    void updateUsageStatistics(ApiAuth apiAuth);
}
