# Test环境配置
spring:
  datasource:
    url: ***********************************************************************************************************************************************
    username: root
    password: e_,HUH58FT
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      auto-commit: true
      idle-timeout: 30000
      pool-name: HikariCP-Test
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1

  data:
    redis:
      database: 0
      host: 127.0.0.1
      password: E6I6ABAOrEWntV9HaF6C
      port: 6379
      timeout: 6000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0

  security:
    user:
      name: swagger
      password: 2gz9AbJBciinsurvVGd0
      roles: ADMIN


# 日志配置
logging:
  level:
    root: info
    com.iot.platform: debug

# 自定义配置
iot:
  security:
    ak-sk:
      enabled: false  # 开发环境禁用AK/SK认证
