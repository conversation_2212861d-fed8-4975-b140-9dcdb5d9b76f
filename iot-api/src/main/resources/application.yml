spring:
  profiles:
    active: test
  application:
    name: iot-platform-api

# 公共配置
server:
  port: 8000
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain,text/css,text/javascript,application/javascript

# 日志配置
logging:
  file:
    name: logs/iot-api.log
  level:
    com.iot.platform: debug
    org.springframework.security: debug
    org.mybatis: debug
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"

# MyBatis-Plus 配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.iot.platform.common.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      table-prefix: ""

# SpringDoc 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: IoT大数据平台API
    description: 自主可控的IoT大数据平台，支持多厂商设备数据采集、处理、展示和远程控制
    version: 1.0.0
    contact:
      name: IoT平台开发团队
      email: <EMAIL>

# 自定义配置
iot:
  security:
    ak-sk:
      enabled: true
      header-access-key: X-Access-Key
      header-signature: X-Signature
      header-timestamp: X-Timestamp
      signature-timeout: 300
  data:
    batch-size: 1000
    max-batch-size: 5000
  mqtt:
    enabled: true
    broker-url: tcp://localhost:1883
    client-id: iot-api-server
    username:
    password:
    keep-alive: 60
    connection-timeout: 30
