# IoT大数据平台 - Git忽略文件配置

# ===== Java相关 =====
# 编译输出
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*
replay_pid*

# ===== Maven相关 =====
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# ===== IDE相关 =====
# IntelliJ IDEA
.idea/
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# ===== 操作系统相关 =====
# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
*.swp
*.swo
*~
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ===== 日志文件 =====
*.log
logs/
log/

# ===== 配置文件（敏感信息） =====
# 数据库配置
application-prod.yml
application-dev.yml
application-local.yml
# 保留示例配置
!application-example.yml

# 环境变量文件
.env
.env.local
.env.*.local

# ===== Docker相关 =====
# Docker数据目录
docker/mysql/data/
docker/redis/data/
docker/mosquitto/data/
docker/mosquitto/log/

# ===== 缓存和临时文件 =====
# Spring Boot
spring-boot-*.log
*.pid

# 测试覆盖率报告
coverage/
.nyc_output/

# ===== 构建和部署相关 =====
# 构建输出
build/
dist/
*.tgz
*.tar.gz

# 部署脚本生成的文件
deploy-*.sh
*.backup

# ===== 开发工具相关 =====
# JProfiler
*.jprofiler

# YourKit
*.yjp

# JMeter
*.jmx.bak

# ===== 项目特定 =====
# 边缘程序生成的jar包
iot-edge/target/*.jar
!iot-edge/target/iot-edge-*.jar

# 代码生成器输出（临时）
generated/
temp/

# 测试数据
test-data/
*.test

# 文档生成
docs/generated/
*.pdf

# ===== 安全相关 =====
# 密钥文件
*.key
*.pem
*.p12
*.jks
*.keystore
*.truststore

# 证书文件
*.crt
*.cer

# ===== 性能分析 =====
# JVM堆转储
*.hprof

# 性能分析报告
*.prof
profiler/

# ===== 数据库相关 =====
# H2数据库文件
*.db
*.mv.db
*.trace.db

# SQLite
*.sqlite
*.sqlite3

# ===== 其他 =====
# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.rar
*.7z

# 临时目录
tmp/
temp/
cache/

# Node.js (如果前端使用)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# ===== 保留重要文件 =====
# 确保重要的配置文件不被忽略
!docker-compose.yml
!docker-compose.test.yml
!Dockerfile
!pom.xml
!README.md
!.editorconfig
!.gitignore
