<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.ApiAuthMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.ApiAuth">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="app_name" property="appName" />
        <result column="access_key" property="accessKey" />
        <result column="secret_key" property="secretKey" />
        <result column="permissions" property="permissions" />
        <result column="status" property="status" />
        <result column="last_used_time" property="lastUsedTime" />
        <result column="usage_count" property="usageCount" />
        <result column="expires_at" property="expiresAt" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, app_name, access_key, secret_key, permissions, status, last_used_time, usage_count, expires_at, created_at, updated_at
    </sql>

</mapper>
