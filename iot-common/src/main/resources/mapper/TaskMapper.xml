<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.TaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.Task">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="task_name" property="taskName" />
        <result column="device_code" property="deviceCode" />
        <result column="group_id" property="groupId" />
        <result column="data_code" property="dataCode" />
        <result column="calc_type" property="calcType" />
        <result column="time_window" property="timeWindow" />
        <result column="description" property="description" />
        <result column="last_execute_time" property="lastExecuteTime" />
        <result column="next_execute_time" property="nextExecuteTime" />
        <result column="execute_count" property="executeCount" />
        <result column="status" property="status" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, task_name, device_code, group_id, data_code, calc_type, time_window, description, last_execute_time, next_execute_time, execute_count, status, created_at, updated_at
    </sql>

</mapper>
