<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.DeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.Device">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="device_code" property="deviceCode" />
        <result column="device_name" property="deviceName" />
        <result column="device_type" property="deviceType" />
        <result column="vendor" property="vendor" />
        <result column="model" property="model" />
        <result column="firmware_version" property="firmwareVersion" />
        <result column="mac_address" property="macAddress" />
        <result column="ip_address" property="ipAddress" />
        <result column="location" property="location" />
        <result column="status" property="status" />
        <result column="last_report_time" property="lastReportTime" />
        <result column="edge_program_id" property="edgeProgramId" />
        <result column="description" property="description" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, device_code, device_name, device_type, vendor, model, firmware_version, mac_address, ip_address, location, status, last_report_time, edge_program_id, description, created_at, updated_at
    </sql>

</mapper>
