<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.SystemLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.SystemLog">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="log_level" property="logLevel" />
        <result column="log_type" property="logType" />
        <result column="module" property="module" />
        <result column="operation" property="operation" />
        <result column="details" property="details" />
        <result column="object_id" property="objectId" />
        <result column="object_type" property="objectType" />
        <result column="user_id" property="userId" />
        <result column="client_ip" property="clientIp" />
        <result column="user_agent" property="userAgent" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, log_level, log_type, module, operation, details, object_id, object_type, user_id, client_ip, user_agent, created_at
    </sql>

</mapper>
