<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.TaskResultMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.TaskResult">
        <id column="id" property="id" />
        <result column="task_id" property="taskId" />
        <result column="project_id" property="projectId" />
        <result column="device_code" property="deviceCode" />
        <result column="group_id" property="groupId" />
        <result column="data_code" property="dataCode" />
        <result column="calc_type" property="calcType" />
        <result column="time_window" property="timeWindow" />
        <result column="result_value" property="resultValue" />
        <result column="data_count" property="dataCount" />
        <result column="window_start" property="windowStart" />
        <result column="window_end" property="windowEnd" />
        <result column="created_at" property="createdAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, task_id, project_id, device_code, group_id, data_code, calc_type, time_window, result_value, data_count, window_start, window_end, created_at
    </sql>

</mapper>
