<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.SystemConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.SystemConfig">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="category" property="category" />
        <result column="config_key" property="configKey" />
        <result column="config_value" property="configValue" />
        <result column="description" property="description" />
        <result column="editable" property="editable" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, category, config_key, config_value, description, editable, created_at, updated_at
    </sql>

</mapper>
