<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.EdgeProgramMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.EdgeProgram">
        <id column="id" property="id" />
        <result column="program_id" property="programId" />
        <result column="program_name" property="programName" />
        <result column="project_id" property="projectId" />
        <result column="version" property="version" />
        <result column="ip_address" property="ipAddress" />
        <result column="port" property="port" />
        <result column="config_json" property="configJson" />
        <result column="collect_interval" property="collectInterval" />
        <result column="cache_max_size" property="cacheMaxSize" />
        <result column="retry_times" property="retryTimes" />
        <result column="heartbeat_interval" property="heartbeatInterval" />
        <result column="status" property="status" />
        <result column="last_heartbeat" property="lastHeartbeat" />
        <result column="config_version" property="configVersion" />
        <result column="description" property="description" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, program_id, program_name, project_id, version, ip_address, port, config_json, collect_interval, cache_max_size, retry_times, heartbeat_interval, status, last_heartbeat, config_version, description, created_at, updated_at
    </sql>

</mapper>
