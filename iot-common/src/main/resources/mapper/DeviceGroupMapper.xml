<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iot.platform.common.mapper.DeviceGroupMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.iot.platform.common.entity.DeviceGroup">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="name" property="name" />
        <result column="parent_id" property="parentId" />
        <result column="group_type" property="groupType" />
        <result column="level" property="level" />
        <result column="sort_order" property="sortOrder" />
        <result column="group_path" property="groupPath" />
        <result column="description" property="description" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, project_id, name, parent_id, group_type, level, sort_order, group_path, description, created_at, updated_at
    </sql>

</mapper>
