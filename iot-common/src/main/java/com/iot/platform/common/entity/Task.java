package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 计算任务配置表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("task")
@Schema(name = "Task", description = "计算任务配置表")
public class Task implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "任务ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "任务名称")
    @TableField("task_name")
    private String taskName;

    @Schema(description = "设备编码")
    @TableField("device_code")
    private String deviceCode;

    @Schema(description = "设备分组ID")
    @TableField("group_id")
    private Long groupId;

    @Schema(description = "数据编码")
    @TableField("data_code")
    private Byte dataCode;

    @Schema(description = "计算类型")
    @TableField("calc_type")
    private Byte calcType;

    @Schema(description = "时间窗口")
    @TableField("time_window")
    private Byte timeWindow;

    @Schema(description = "任务描述")
    @TableField("description")
    private String description;

    @Schema(description = "最后执行时间戳")
    @TableField("last_execute_time")
    private Long lastExecuteTime;

    @Schema(description = "下次执行时间戳")
    @TableField("next_execute_time")
    private Long nextExecuteTime;

    @Schema(description = "执行次数")
    @TableField("execute_count")
    private Long executeCount;

    @Schema(description = "任务状态(0:停用 1:启用)")
    @TableField("status")
    private Byte status;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
