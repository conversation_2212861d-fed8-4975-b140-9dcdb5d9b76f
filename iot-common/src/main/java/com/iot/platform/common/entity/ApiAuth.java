package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * API认证表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("api_auth")
@Schema(name = "ApiAuth", description = "API认证表")
public class ApiAuth implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "认证ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "应用名称")
    @TableField("app_name")
    private String appName;

    @Schema(description = "访问密钥")
    @TableField("access_key")
    private String accessKey;

    @Schema(description = "秘密密钥")
    @TableField("secret_key")
    private String secretKey;

    @Schema(description = "权限配置(JSON格式)")
    @TableField("permissions")
    private String permissions;

    @Schema(description = "状态(0:禁用 1:启用)")
    @TableField("status")
    private Byte status;

    @Schema(description = "最后使用时间戳")
    @TableField("last_used_time")
    private Long lastUsedTime;

    @Schema(description = "使用次数")
    @TableField("usage_count")
    private Long usageCount;

    @Schema(description = "过期时间")
    @TableField("expires_at")
    private LocalDateTime expiresAt;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
