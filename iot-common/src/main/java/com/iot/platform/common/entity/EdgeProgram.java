package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 边缘程序配置表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("edge_program")
@Schema(name = "EdgeProgram", description = "边缘程序配置表")
public class EdgeProgram implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "边缘程序ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "边缘程序唯一标识")
    @TableField("program_id")
    private String programId;

    @Schema(description = "边缘程序名称")
    @TableField("program_name")
    private String programName;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "版本号")
    @TableField("version")
    private String version;

    @Schema(description = "IP地址")
    @TableField("ip_address")
    private String ipAddress;

    @Schema(description = "端口号")
    @TableField("port")
    private Integer port;

    @Schema(description = "配置JSON")
    @TableField("config_json")
    private String configJson;

    @Schema(description = "采集间隔(秒)")
    @TableField("collect_interval")
    private Integer collectInterval;

    @Schema(description = "缓存最大大小")
    @TableField("cache_max_size")
    private Integer cacheMaxSize;

    @Schema(description = "重试次数")
    @TableField("retry_times")
    private Integer retryTimes;

    @Schema(description = "心跳间隔(秒)")
    @TableField("heartbeat_interval")
    private Integer heartbeatInterval;

    @Schema(description = "状态(0:停用 1:启用)")
    @TableField("status")
    private Byte status;

    @Schema(description = "最后心跳时间戳")
    @TableField("last_heartbeat")
    private Long lastHeartbeat;

    @Schema(description = "配置版本")
    @TableField("config_version")
    private String configVersion;

    @Schema(description = "描述")
    @TableField("description")
    private String description;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
