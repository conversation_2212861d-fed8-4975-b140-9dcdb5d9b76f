package com.iot.platform.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.Duration;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 时间窗口枚举
 * 数据库存储数字编码，JSON序列化为描述信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum TimeWindow {

    /**
     * 5分钟时间窗口
     */
    MIN_5((byte) 1, 5 * 60L, Duration.ofMinutes(5), "5分钟", "高频实时统计"),

    /**
     * 15分钟时间窗口
     */
    MIN_15((byte) 2, 15 * 60L, Duration.ofMinutes(15), "15分钟", "中频数据统计"),

    /**
     * 1小时时间窗口
     */
    HOUR_1((byte) 3, 60 * 60L, Duration.ofHours(1), "1小时", "小时级数据统计"),

    /**
     * 1天时间窗口
     */
    DAY_1((byte) 4, 24 * 60 * 60L, Duration.ofDays(1), "1天", "日级数据统计");

    /**
     * 数据库存储值（MyBatis-Plus使用）
     */
    @EnumValue
    private final Byte code;

    /**
     * 时间窗口秒数
     */
    private final Long seconds;

    /**
     * Duration对象
     */
    private final Duration duration;

    /**
     * 中文描述（JSON序列化使用）
     */
    @JsonValue
    private final String description;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 缓存映射，提高查询效率
     */
    private static final Map<Byte, TimeWindow> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(TimeWindow::getCode, Function.identity()));

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static Optional<TimeWindow> fromCode(Byte code) {
        return Optional.ofNullable(CODE_MAP.get(code));
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 获取毫秒数
     *
     * @return 毫秒数
     */
    public long getMilliseconds() {
        return seconds * 1000L;
    }

    /**
     * 获取分钟数
     *
     * @return 分钟数
     */
    public long getMinutes() {
        return seconds / 60L;
    }

    /**
     * 获取小时数
     *
     * @return 小时数
     */
    public long getHours() {
        return seconds / 3600L;
    }

    /**
     * 获取天数
     *
     * @return 天数
     */
    public long getDays() {
        return seconds / 86400L;
    }

    /**
     * 判断是否为分钟级时间窗口
     *
     * @return 是否为分钟级
     */
    public boolean isMinuteLevel() {
        return this == MIN_5 || this == MIN_15;
    }

    /**
     * 判断是否为小时级时间窗口
     *
     * @return 是否为小时级
     */
    public boolean isHourLevel() {
        return this == HOUR_1;
    }

    /**
     * 判断是否为天级时间窗口
     *
     * @return 是否为天级
     */
    public boolean isDayLevel() {
        return this == DAY_1;
    }

    /**
     * 计算指定时间戳对应的窗口开始时间
     *
     * @param timestamp 时间戳（毫秒）
     * @return 窗口开始时间戳（毫秒）
     */
    public long getWindowStart(long timestamp) {
        long windowMillis = getMilliseconds();
        return (timestamp / windowMillis) * windowMillis;
    }

    /**
     * 计算指定时间戳对应的窗口结束时间
     *
     * @param timestamp 时间戳（毫秒）
     * @return 窗口结束时间戳（毫秒）
     */
    public long getWindowEnd(long timestamp) {
        return getWindowStart(timestamp) + getMilliseconds();
    }
}
