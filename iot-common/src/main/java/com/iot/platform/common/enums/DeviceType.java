package com.iot.platform.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备类型枚举
 * 数据库存储数字编码，JSON序列化为描述信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum DeviceType {

    /**
     * 传感器设备
     */
    SENSOR((byte) 1, "传感器", "sensor", "各类环境传感器设备"),

    /**
     * 摄像头设备
     */
    CAMERA((byte) 2, "摄像头", "camera", "监控摄像头设备"),

    /**
     * 网关设备
     */
    GATEWAY((byte) 3, "网关", "gateway", "数据采集网关设备"),

    /**
     * 控制器设备
     */
    CONTROLLER((byte) 4, "控制器", "controller", "设备控制器");

    /**
     * 数据库存储值（MyBatis-Plus使用）
     */
    @EnumValue
    private final Byte code;

    /**
     * 中文描述（JSON序列化使用）
     */
    @JsonValue
    private final String description;

    /**
     * 英文标识
     */
    private final String identifier;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 缓存映射，提高查询效率
     */
    private static final Map<Byte, DeviceType> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DeviceType::getCode, Function.identity()));

    private static final Map<String, DeviceType> IDENTIFIER_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DeviceType::getIdentifier, Function.identity()));

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static Optional<DeviceType> fromCode(Byte code) {
        return Optional.ofNullable(CODE_MAP.get(code));
    }

    /**
     * 根据标识获取枚举
     *
     * @param identifier 标识
     * @return 枚举值
     */
    public static Optional<DeviceType> fromIdentifier(String identifier) {
        return Optional.ofNullable(IDENTIFIER_MAP.get(identifier));
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return CODE_MAP.containsKey(code);
    }
}
