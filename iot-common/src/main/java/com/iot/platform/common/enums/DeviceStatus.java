package com.iot.platform.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备状态枚举
 * 数据库存储数字编码，JSON序列化为描述信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum DeviceStatus {

    /**
     * 设备正常工作状态
     */
    ACTIVE((byte) 1, "正常", "设备正常工作状态"),

    /**
     * 设备离线或无响应
     */
    INACTIVE((byte) 2, "离线", "设备离线或无响应"),

    /**
     * 设备故障需要维修
     */
    FAULT((byte) 3, "故障", "设备故障需要维修");

    /**
     * 数据库存储值（MyBatis-Plus使用）
     */
    @EnumValue
    private final Byte code;

    /**
     * 中文描述（JSON序列化使用）
     */
    @JsonValue
    private final String description;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 缓存映射，提高查询效率
     */
    private static final Map<Byte, DeviceStatus> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DeviceStatus::getCode, Function.identity()));

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static Optional<DeviceStatus> fromCode(Byte code) {
        return Optional.ofNullable(CODE_MAP.get(code));
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 判断设备是否在线
     *
     * @return 是否在线
     */
    public boolean isOnline() {
        return this == ACTIVE;
    }

    /**
     * 判断设备是否离线
     *
     * @return 是否离线
     */
    public boolean isOffline() {
        return this == INACTIVE;
    }

    /**
     * 判断设备是否故障
     *
     * @return 是否故障
     */
    public boolean isFault() {
        return this == FAULT;
    }
}
