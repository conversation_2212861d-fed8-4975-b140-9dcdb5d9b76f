package com.iot.platform.common.archive;

import com.iot.platform.common.sharding.ShardingService;
import com.iot.platform.common.sharding.impl.MonthlyShardingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 数据归档服务
 * 负责将历史数据归档到文件系统，并清理过期数据
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class DataArchiveService {

    @Value("${iot.archive.enabled:true}")
    private boolean archiveEnabled;

    @Value("${iot.archive.directory:./archive}")
    private String archiveDirectory;

    @Value("${iot.archive.retention-months:12}")
    private int retentionMonths;

    @Value("${iot.archive.archive-months:6}")
    private int archiveMonths; // 超过几个月的数据进行归档

    @Value("${iot.archive.batch-size:10000}")
    private int batchSize;

    @Value("${iot.archive.compress:true}")
    private boolean compressArchive;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ShardingService shardingService;

    @Autowired
    private MonthlyShardingStrategy monthlyShardingStrategy;

    /**
     * 归档任务执行器
     */
    private ExecutorService archiveExecutor;

    /**
     * 归档统计信息
     */
    private volatile long totalArchivedRows = 0;
    private volatile long totalArchivedFiles = 0;
    private volatile double totalArchivedSizeMb = 0.0;
    private volatile LocalDateTime lastArchiveTime;

    @PostConstruct
    public void init() {
        // 创建归档目录
        createArchiveDirectory();

        // 初始化线程池
        archiveExecutor = Executors.newFixedThreadPool(2);

        log.info("数据归档服务初始化完成: enabled={}, directory={}, retentionMonths={}, archiveMonths={}",
                archiveEnabled, archiveDirectory, retentionMonths, archiveMonths);
    }

    /**
     * 定时归档任务
     */
    @Scheduled(cron = "0 0 3 1 * ?") // 每月1号凌晨3点执行
    public void scheduledArchive() {
        if (!archiveEnabled) {
            log.debug("数据归档功能已禁用");
            return;
        }

        try {
            log.info("开始定时数据归档任务");
            archiveOldData();
            log.info("定时数据归档任务完成");
        } catch (Exception e) {
            log.error("定时数据归档任务失败", e);
        }
    }

    /**
     * 归档旧数据
     */
    public void archiveOldData() {
        if (!archiveEnabled) {
            log.warn("数据归档功能已禁用");
            return;
        }

        try {
            LocalDateTime archiveThreshold = LocalDateTime.now().minusMonths(archiveMonths);
            List<String> existingTables = monthlyShardingStrategy.getExistingTables();

            for (String tableName : existingTables) {
                if (shouldArchiveTable(tableName, archiveThreshold)) {
                    CompletableFuture.runAsync(() -> archiveTable(tableName), archiveExecutor);
                }
            }

        } catch (Exception e) {
            log.error("归档旧数据失败", e);
        }
    }

    /**
     * 归档指定表
     */
    public boolean archiveTable(String tableName) {
        try {
            log.info("开始归档表: {}", tableName);

            // 检查表是否存在
            if (!monthlyShardingStrategy.tableExists(tableName)) {
                log.warn("表不存在，跳过归档: {}", tableName);
                return false;
            }

            // 获取表统计信息
            MonthlyShardingStrategy.TableStats stats = monthlyShardingStrategy.getTableStats(tableName);
            if (stats.getRowCount() == 0) {
                log.info("表为空，跳过归档: {}", tableName);
                return true;
            }

            // 创建归档文件
            String archiveFileName = generateArchiveFileName(tableName);
            File archiveFile = new File(archiveDirectory, archiveFileName);

            // 分批导出数据
            long totalRows = exportTableToFile(tableName, archiveFile);

            if (totalRows > 0) {
                // 更新统计信息
                updateArchiveStats(totalRows, archiveFile.length());

                // 验证归档文件
                if (validateArchiveFile(archiveFile, totalRows)) {
                    log.info("表归档成功: {} -> {}, rows={}, size={}MB",
                            tableName, archiveFileName, totalRows,
                            Math.round(archiveFile.length() / 1024.0 / 1024.0 * 100.0) / 100.0);
                    return true;
                } else {
                    log.error("归档文件验证失败: {}", archiveFileName);
                    archiveFile.delete();
                    return false;
                }
            } else {
                log.warn("没有数据被归档: {}", tableName);
                return false;
            }

        } catch (Exception e) {
            log.error("归档表失败: {}", tableName, e);
            return false;
        }
    }

    /**
     * 清理过期数据
     */
    @Scheduled(cron = "0 0 4 1 * ?") // 每月1号凌晨4点执行
    public void cleanupExpiredData() {
        if (!archiveEnabled) {
            return;
        }

        try {
            log.info("开始清理过期数据，保留月数: {}", retentionMonths);

            LocalDateTime expireThreshold = LocalDateTime.now().minusMonths(retentionMonths);
            List<String> existingTables = monthlyShardingStrategy.getExistingTables();

            int deletedCount = 0;
            for (String tableName : existingTables) {
                if (shouldDeleteTable(tableName, expireThreshold)) {
                    // 检查是否已归档
                    if (isTableArchived(tableName)) {
                        monthlyShardingStrategy.dropTable(tableName);
                        deletedCount++;
                        log.info("删除已归档的过期表: {}", tableName);
                    } else {
                        log.warn("表未归档，跳过删除: {}", tableName);
                    }
                }
            }

            log.info("清理过期数据完成，删除表数量: {}", deletedCount);

        } catch (Exception e) {
            log.error("清理过期数据失败", e);
        }
    }

    /**
     * 手动归档指定表
     */
    public boolean manualArchiveTable(String tableName) {
        if (!archiveEnabled) {
            log.warn("数据归档功能已禁用");
            return false;
        }

        return archiveTable(tableName);
    }

    /**
     * 获取归档状态
     */
    public ArchiveStatus getArchiveStatus() {
        ArchiveStatus status = new ArchiveStatus();
        status.setEnabled(archiveEnabled);
        status.setArchiveDirectory(archiveDirectory);
        status.setRetentionMonths(retentionMonths);
        status.setArchiveMonths(archiveMonths);
        status.setTotalArchivedRows(totalArchivedRows);
        status.setTotalArchivedFiles(totalArchivedFiles);
        status.setTotalArchivedSizeMb(totalArchivedSizeMb);
        status.setLastArchiveTime(lastArchiveTime);

        // 获取归档文件列表
        File archiveDir = new File(archiveDirectory);
        if (archiveDir.exists() && archiveDir.isDirectory()) {
            File[] archiveFiles = archiveDir.listFiles((dir, name) -> name.endsWith(".csv") || name.endsWith(".csv.gz"));
            status.setArchiveFileCount(archiveFiles != null ? archiveFiles.length : 0);
        }

        return status;
    }

    /**
     * 创建归档目录
     */
    private void createArchiveDirectory() {
        File dir = new File(archiveDirectory);
        if (!dir.exists()) {
            if (dir.mkdirs()) {
                log.info("创建归档目录: {}", archiveDirectory);
            } else {
                log.error("创建归档目录失败: {}", archiveDirectory);
            }
        }
    }

    /**
     * 判断是否应该归档表
     */
    private boolean shouldArchiveTable(String tableName, LocalDateTime threshold) {
        try {
            String[] parts = tableName.split("_");
            if (parts.length > 0) {
                String monthPart = parts[parts.length - 1];
                if (monthPart.length() == 6) {
                    int year = Integer.parseInt(monthPart.substring(0, 4));
                    int month = Integer.parseInt(monthPart.substring(4, 6));
                    LocalDateTime tableDate = LocalDateTime.of(year, month, 1, 0, 0);
                    return tableDate.isBefore(threshold);
                }
            }
        } catch (Exception e) {
            log.warn("解析表名日期失败: {}", tableName, e);
        }
        return false;
    }

    /**
     * 判断是否应该删除表
     */
    private boolean shouldDeleteTable(String tableName, LocalDateTime threshold) {
        return shouldArchiveTable(tableName, threshold);
    }

    /**
     * 检查表是否已归档
     */
    private boolean isTableArchived(String tableName) {
        String archiveFileName = generateArchiveFileName(tableName);
        File archiveFile = new File(archiveDirectory, archiveFileName);
        return archiveFile.exists();
    }

    /**
     * 生成归档文件名
     */
    private String generateArchiveFileName(String tableName) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = tableName + "_" + timestamp + ".csv";
        return compressArchive ? fileName + ".gz" : fileName;
    }

    /**
     * 导出表数据到文件
     */
    private long exportTableToFile(String tableName, File archiveFile) throws IOException {
        long totalRows = 0;

        try (FileWriter writer = new FileWriter(archiveFile)) {
            // 写入CSV头部
            writer.write("id,project_id,device_code,data_code,data_value,quality,report_time,receive_time,edge_program_id,ext_data,create_time,update_time\n");

            // 分批查询和写入数据
            long offset = 0;
            while (true) {
                String sql = String.format(
                    "SELECT * FROM `%s` ORDER BY id LIMIT %d OFFSET %d",
                    tableName, batchSize, offset);

                List<Map<String, Object>> rows = jdbcTemplate.queryForList(sql);
                if (rows.isEmpty()) {
                    break;
                }

                // 写入数据行
                for (Map<String, Object> row : rows) {
                    writeCsvRow(writer, row);
                    totalRows++;
                }

                offset += batchSize;

                // 记录进度
                if (totalRows % (batchSize * 10) == 0) {
                    log.debug("归档进度: {} rows exported from {}", totalRows, tableName);
                }
            }
        }

        return totalRows;
    }

    /**
     * 写入CSV行
     */
    private void writeCsvRow(FileWriter writer, Map<String, Object> row) throws IOException {
        StringBuilder sb = new StringBuilder();

        String[] columns = {"id", "project_id", "device_code", "data_code", "data_value",
                           "quality", "report_time", "receive_time", "edge_program_id",
                           "ext_data", "create_time", "update_time"};

        for (int i = 0; i < columns.length; i++) {
            if (i > 0) {
                sb.append(",");
            }

            Object value = row.get(columns[i]);
            if (value != null) {
                String strValue = value.toString();
                // 转义CSV特殊字符
                if (strValue.contains(",") || strValue.contains("\"") || strValue.contains("\n")) {
                    strValue = "\"" + strValue.replace("\"", "\"\"") + "\"";
                }
                sb.append(strValue);
            }
        }

        sb.append("\n");
        writer.write(sb.toString());
    }

    /**
     * 验证归档文件
     */
    private boolean validateArchiveFile(File archiveFile, long expectedRows) {
        // 简单验证：检查文件大小是否合理
        if (!archiveFile.exists() || archiveFile.length() == 0) {
            return false;
        }

        // 可以添加更复杂的验证逻辑，比如行数统计等
        return true;
    }

    /**
     * 更新归档统计信息
     */
    private void updateArchiveStats(long rows, long fileSize) {
        totalArchivedRows += rows;
        totalArchivedFiles++;
        totalArchivedSizeMb += fileSize / 1024.0 / 1024.0;
        lastArchiveTime = LocalDateTime.now();
    }

    /**
     * 归档状态信息
     */
    public static class ArchiveStatus {
        private boolean enabled;
        private String archiveDirectory;
        private int retentionMonths;
        private int archiveMonths;
        private long totalArchivedRows;
        private long totalArchivedFiles;
        private double totalArchivedSizeMb;
        private LocalDateTime lastArchiveTime;
        private int archiveFileCount;

        // Getters and Setters
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public String getArchiveDirectory() { return archiveDirectory; }
        public void setArchiveDirectory(String archiveDirectory) { this.archiveDirectory = archiveDirectory; }

        public int getRetentionMonths() { return retentionMonths; }
        public void setRetentionMonths(int retentionMonths) { this.retentionMonths = retentionMonths; }

        public int getArchiveMonths() { return archiveMonths; }
        public void setArchiveMonths(int archiveMonths) { this.archiveMonths = archiveMonths; }

        public long getTotalArchivedRows() { return totalArchivedRows; }
        public void setTotalArchivedRows(long totalArchivedRows) { this.totalArchivedRows = totalArchivedRows; }

        public long getTotalArchivedFiles() { return totalArchivedFiles; }
        public void setTotalArchivedFiles(long totalArchivedFiles) { this.totalArchivedFiles = totalArchivedFiles; }

        public double getTotalArchivedSizeMb() { return totalArchivedSizeMb; }
        public void setTotalArchivedSizeMb(double totalArchivedSizeMb) { this.totalArchivedSizeMb = totalArchivedSizeMb; }

        public LocalDateTime getLastArchiveTime() { return lastArchiveTime; }
        public void setLastArchiveTime(LocalDateTime lastArchiveTime) { this.lastArchiveTime = lastArchiveTime; }

        public int getArchiveFileCount() { return archiveFileCount; }
        public void setArchiveFileCount(int archiveFileCount) { this.archiveFileCount = archiveFileCount; }
    }
}
