package com.iot.platform.common.sharding.impl;

import com.iot.platform.common.sharding.ShardingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 按月分表策略实现
 * 按照年月格式（YYYYMM）进行分表
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class MonthlyShardingStrategy implements ShardingStrategy {

    private static final String BASE_TABLE_NAME = "iot_data";
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyyMM");

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getTableName(long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
        return getTableName(dateTime);
    }

    @Override
    public String getTableName(LocalDateTime dateTime) {
        String monthSuffix = dateTime.format(MONTH_FORMATTER);
        return BASE_TABLE_NAME + "_" + monthSuffix;
    }

    @Override
    public List<String> getTableNames(long startTime, long endTime) {
        List<String> tableNames = new ArrayList<>();

        LocalDateTime startDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(startTime), ZoneId.systemDefault());
        LocalDateTime endDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(endTime), ZoneId.systemDefault());

        // 从开始月份遍历到结束月份
        LocalDateTime current = startDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime end = endDateTime.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);

        while (!current.isAfter(end)) {
            String tableName = getTableName(current);
            tableNames.add(tableName);
            current = current.plusMonths(1);
        }

        return tableNames;
    }

    @Override
    public String getCurrentTableName() {
        return getTableName(LocalDateTime.now());
    }

    @Override
    public String getNextTableName() {
        LocalDateTime nextMonth = LocalDateTime.now().plusMonths(1);
        return getTableName(nextMonth);
    }

    @Override
    public boolean tableExists(String tableName) {
        try {
            String sql = "SELECT COUNT(*) FROM information_schema.tables " +
                        "WHERE table_schema = DATABASE() AND table_name = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, tableName);
            return count != null && count > 0;
        } catch (Exception e) {
            log.error("检查表是否存在失败: {}", tableName, e);
            return false;
        }
    }

    @Override
    public boolean createTable(String tableName) {
        try {
            if (tableExists(tableName)) {
                log.info("表已存在，无需创建: {}", tableName);
                return true;
            }

            String createTableSql = buildCreateTableSql(tableName);
            jdbcTemplate.execute(createTableSql);

            // 创建索引
            createIndexes(tableName);

            log.info("成功创建分表: {}", tableName);
            return true;

        } catch (Exception e) {
            log.error("创建分表失败: {}", tableName, e);
            return false;
        }
    }

    @Override
    public String getBaseTableName() {
        return BASE_TABLE_NAME;
    }

    @Override
    public ShardingType getShardingType() {
        return ShardingType.MONTHLY;
    }

    /**
     * 构建创建表的SQL语句
     */
    private String buildCreateTableSql(String tableName) {
        return String.format("""
            CREATE TABLE `%s` (
                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `project_id` varchar(50) NOT NULL COMMENT '项目ID',
                `device_code` varchar(100) NOT NULL COMMENT '设备编码',
                `data_code` tinyint NOT NULL COMMENT '数据类型码',
                `data_value` decimal(20,6) NOT NULL COMMENT '数据值',
                `quality` tinyint DEFAULT '1' COMMENT '数据质量等级',
                `report_time` bigint NOT NULL COMMENT '设备上报时间戳',
                `receive_time` bigint NOT NULL DEFAULT (unix_timestamp(now()) * 1000) COMMENT '平台接收时间戳',
                `edge_program_id` varchar(100) DEFAULT NULL COMMENT '边缘程序ID',
                `ext_data` varchar(1000) DEFAULT NULL COMMENT '扩展数据',
                `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                PRIMARY KEY (`id`),
                KEY `idx_device_time` (`device_code`, `report_time`),
                KEY `idx_project_time` (`project_id`, `report_time`),
                KEY `idx_data_code_time` (`data_code`, `report_time`),
                KEY `idx_report_time` (`report_time`),
                KEY `idx_receive_time` (`receive_time`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='IoT设备数据表_%s'
            """, tableName, extractMonthFromTableName(tableName));
    }

    /**
     * 创建额外的索引
     */
    private void createIndexes(String tableName) {
        try {
            // 创建复合索引用于常见查询场景
            String[] indexSqls = {
                String.format("CREATE INDEX `idx_%s_device_data_time` ON `%s` (`device_code`, `data_code`, `report_time`)",
                        tableName, tableName),
                String.format("CREATE INDEX `idx_%s_project_device_time` ON `%s` (`project_id`, `device_code`, `report_time`)",
                        tableName, tableName)
            };

            for (String indexSql : indexSqls) {
                try {
                    jdbcTemplate.execute(indexSql);
                    log.debug("创建索引成功: {}", indexSql);
                } catch (Exception e) {
                    log.warn("创建索引失败，可能已存在: {}", indexSql);
                }
            }

        } catch (Exception e) {
            log.error("创建索引失败: {}", tableName, e);
        }
    }

    /**
     * 从表名中提取月份信息
     */
    private String extractMonthFromTableName(String tableName) {
        if (tableName.contains("_")) {
            String[] parts = tableName.split("_");
            if (parts.length > 0) {
                String monthPart = parts[parts.length - 1];
                if (monthPart.length() == 6) {
                    return monthPart.substring(0, 4) + "年" + monthPart.substring(4, 6) + "月";
                }
            }
        }
        return "未知";
    }

    /**
     * 自动创建当前月份和下个月份的表
     */
    public void autoCreateTables() {
        try {
            // 创建当前月份的表
            String currentTable = getCurrentTableName();
            if (!tableExists(currentTable)) {
                createTable(currentTable);
            }

            // 创建下个月份的表
            String nextTable = getNextTableName();
            if (!tableExists(nextTable)) {
                createTable(nextTable);
            }

            log.info("自动创建分表完成: current={}, next={}", currentTable, nextTable);

        } catch (Exception e) {
            log.error("自动创建分表失败", e);
        }
    }

    /**
     * 获取所有已存在的分表
     */
    public List<String> getExistingTables() {
        try {
            String sql = "SELECT table_name FROM information_schema.tables " +
                        "WHERE table_schema = DATABASE() AND table_name LIKE ? " +
                        "ORDER BY table_name";

            return jdbcTemplate.queryForList(sql, String.class, BASE_TABLE_NAME + "_%");

        } catch (Exception e) {
            log.error("获取已存在的分表失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 删除过期的分表
     */
    public void dropExpiredTables(int retentionMonths) {
        try {
            LocalDateTime expireDate = LocalDateTime.now().minusMonths(retentionMonths);
            List<String> existingTables = getExistingTables();

            for (String tableName : existingTables) {
                if (isTableExpired(tableName, expireDate)) {
                    dropTable(tableName);
                }
            }

        } catch (Exception e) {
            log.error("删除过期分表失败", e);
        }
    }

    /**
     * 检查表是否过期
     */
    private boolean isTableExpired(String tableName, LocalDateTime expireDate) {
        try {
            String[] parts = tableName.split("_");
            if (parts.length > 0) {
                String monthPart = parts[parts.length - 1];
                if (monthPart.length() == 6) {
                    int year = Integer.parseInt(monthPart.substring(0, 4));
                    int month = Integer.parseInt(monthPart.substring(4, 6));
                    LocalDateTime tableDate = LocalDateTime.of(year, month, 1, 0, 0);
                    return tableDate.isBefore(expireDate);
                }
            }
        } catch (Exception e) {
            log.warn("解析表名日期失败: {}", tableName, e);
        }
        return false;
    }

    /**
     * 删除表
     */
    public void dropTable(String tableName) {
        try {
            String sql = "DROP TABLE IF EXISTS `" + tableName + "`";
            jdbcTemplate.execute(sql);
            log.info("删除过期分表: {}", tableName);
        } catch (Exception e) {
            log.error("删除分表失败: {}", tableName, e);
        }
    }

    /**
     * 获取表的统计信息
     */
    public TableStats getTableStats(String tableName) {
        try {
            String countSql = "SELECT COUNT(*) FROM `" + tableName + "`";
            String sizeSql = "SELECT ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb' " +
                           "FROM information_schema.tables " +
                           "WHERE table_schema = DATABASE() AND table_name = ?";

            Long rowCount = jdbcTemplate.queryForObject(countSql, Long.class);
            Double sizeMb = jdbcTemplate.queryForObject(sizeSql, Double.class, tableName);

            TableStats stats = new TableStats();
            stats.setTableName(tableName);
            stats.setRowCount(rowCount != null ? rowCount : 0);
            stats.setSizeMb(sizeMb != null ? sizeMb : 0.0);

            return stats;

        } catch (Exception e) {
            log.error("获取表统计信息失败: {}", tableName, e);
            return new TableStats(tableName, 0, 0.0);
        }
    }

    /**
     * 表统计信息
     */
    public static class TableStats {
        private String tableName;
        private long rowCount;
        private double sizeMb;

        public TableStats() {}

        public TableStats(String tableName, long rowCount, double sizeMb) {
            this.tableName = tableName;
            this.rowCount = rowCount;
            this.sizeMb = sizeMb;
        }

        // Getters and Setters
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }

        public long getRowCount() { return rowCount; }
        public void setRowCount(long rowCount) { this.rowCount = rowCount; }

        public double getSizeMb() { return sizeMb; }
        public void setSizeMb(double sizeMb) { this.sizeMb = sizeMb; }
    }
}
