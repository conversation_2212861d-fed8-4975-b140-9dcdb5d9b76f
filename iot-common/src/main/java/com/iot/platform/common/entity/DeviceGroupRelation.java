package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备分组关联表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("device_group_relation")
@Schema(name = "DeviceGroupRelation", description = "设备分组关联表")
public class DeviceGroupRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "设备ID")
    @TableField("device_id")
    private Long deviceId;

    @Schema(description = "分组ID")
    @TableField("group_id")
    private Long groupId;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;
}
