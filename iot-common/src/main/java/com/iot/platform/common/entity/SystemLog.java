package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 系统日志表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("system_log")
@Schema(name = "SystemLog", description = "系统日志表")
public class SystemLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "日志ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "日志级别")
    @TableField("log_level")
    private String logLevel;

    @Schema(description = "日志类型")
    @TableField("log_type")
    private String logType;

    @Schema(description = "模块名称")
    @TableField("module")
    private String module;

    @Schema(description = "操作名称")
    @TableField("operation")
    private String operation;

    @Schema(description = "详细信息")
    @TableField("details")
    private String details;

    @Schema(description = "操作对象ID")
    @TableField("object_id")
    private String objectId;

    @Schema(description = "操作对象类型")
    @TableField("object_type")
    private String objectType;

    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    @Schema(description = "客户端IP")
    @TableField("client_ip")
    private String clientIp;

    @Schema(description = "用户代理")
    @TableField("user_agent")
    private String userAgent;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;
}
