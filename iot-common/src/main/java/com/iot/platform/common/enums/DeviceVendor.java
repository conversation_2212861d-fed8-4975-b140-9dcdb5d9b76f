package com.iot.platform.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 设备厂商枚举
 * 数据库存储数字编码，JSON序列化为描述信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum DeviceVendor {

    /**
     * 星纵物联网设备
     */
    YEASTAR((byte) 1, "星纵", "YEASTAR", "星纵物联网设备"),

    /**
     * 涂鸦智能设备
     */
    TUYA((byte) 2, "涂鸦", "TUYA", "涂鸦智能设备"),

    /**
     * 海康威视监控设备
     */
    HIKVISION((byte) 3, "海康威视", "HIKVISION", "海康威视监控设备"),

    /**
     * 大华监控设备
     */
    DAHUA((byte) 4, "大华", "DAHUA", "大华监控设备"),

    /**
     * 宇视监控设备
     */
    UNIVIEW((byte) 5, "宇视", "UNIVIEW", "宇视监控设备");

    /**
     * 数据库存储值（MyBatis-Plus使用）
     */
    @EnumValue
    private final Byte code;

    /**
     * 中文描述（JSON序列化使用）
     */
    @JsonValue
    private final String description;

    /**
     * 英文标识
     */
    private final String identifier;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 缓存映射，提高查询效率
     */
    private static final Map<Byte, DeviceVendor> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DeviceVendor::getCode, Function.identity()));

    private static final Map<String, DeviceVendor> IDENTIFIER_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DeviceVendor::getIdentifier, Function.identity()));

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static Optional<DeviceVendor> fromCode(Byte code) {
        return Optional.ofNullable(CODE_MAP.get(code));
    }

    /**
     * 根据标识获取枚举
     *
     * @param identifier 标识
     * @return 枚举值
     */
    public static Optional<DeviceVendor> fromIdentifier(String identifier) {
        return Optional.ofNullable(IDENTIFIER_MAP.get(identifier));
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return CODE_MAP.containsKey(code);
    }
}
