package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 计算结果表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("task_result")
@Schema(name = "TaskResult", description = "计算结果表")
public class TaskResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "结果ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "任务ID")
    @TableField("task_id")
    private Long taskId;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "设备编码")
    @TableField("device_code")
    private String deviceCode;

    @Schema(description = "设备分组ID")
    @TableField("group_id")
    private Long groupId;

    @Schema(description = "数据编码")
    @TableField("data_code")
    private Byte dataCode;

    @Schema(description = "计算类型")
    @TableField("calc_type")
    private Byte calcType;

    @Schema(description = "时间窗口")
    @TableField("time_window")
    private Byte timeWindow;

    @Schema(description = "计算结果值")
    @TableField("result_value")
    private BigDecimal resultValue;

    @Schema(description = "数据点数量")
    @TableField("data_count")
    private Integer dataCount;

    @Schema(description = "窗口开始时间戳")
    @TableField("window_start")
    private Long windowStart;

    @Schema(description = "窗口结束时间戳")
    @TableField("window_end")
    private Long windowEnd;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;
}
