package com.iot.platform.common.config;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.lang.reflect.Method;

/**
 * 通用枚举类型处理器
 * 支持带有code字段的枚举类型与数据库的转换
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@MappedTypes({})
public class EnumTypeHandler<E extends Enum<E>> extends BaseTypeHandler<E> {

    private final Class<E> enumType;
    private final Method getCodeMethod;
    private final Method fromCodeMethod;

    public EnumTypeHandler(Class<E> enumType) {
        if (enumType == null) {
            throw new IllegalArgumentException("枚举类型不能为空");
        }
        this.enumType = enumType;

        try {
            // 获取getCode方法
            this.getCodeMethod = enumType.getMethod("getCode");

            // 获取fromCode静态方法
            this.fromCodeMethod = enumType.getMethod("fromCode", getCodeMethod.getReturnType());

        } catch (NoSuchMethodException e) {
            throw new IllegalArgumentException(
                String.format("枚举类 %s 必须包含 getCode() 方法和 fromCode(code) 静态方法",
                    enumType.getSimpleName()), e);
        }
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, E parameter, JdbcType jdbcType) throws SQLException {
        try {
            Object code = getCodeMethod.invoke(parameter);
            if (code instanceof Byte) {
                ps.setByte(i, (Byte) code);
            } else if (code instanceof Integer) {
                ps.setInt(i, (Integer) code);
            } else if (code instanceof String) {
                ps.setString(i, (String) code);
            } else {
                ps.setObject(i, code);
            }
        } catch (Exception e) {
            throw new SQLException("设置枚举参数失败: " + parameter, e);
        }
    }

    @Override
    public E getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Object code = rs.getObject(columnName);
        return code == null ? null : getEnumByCode(code);
    }

    @Override
    public E getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Object code = rs.getObject(columnIndex);
        return code == null ? null : getEnumByCode(code);
    }

    @Override
    public E getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Object code = cs.getObject(columnIndex);
        return code == null ? null : getEnumByCode(code);
    }

    /**
     * 根据code值获取枚举实例
     */
    @SuppressWarnings("unchecked")
    private E getEnumByCode(Object code) throws SQLException {
        try {
            Object result = fromCodeMethod.invoke(null, code);
            if (result instanceof java.util.Optional) {
                java.util.Optional<E> optional = (java.util.Optional<E>) result;
                return optional.orElse(null);
            }
            return (E) result;
        } catch (Exception e) {
            throw new SQLException(
                String.format("根据code值 %s 获取枚举 %s 失败", code, enumType.getSimpleName()), e);
        }
    }
}
