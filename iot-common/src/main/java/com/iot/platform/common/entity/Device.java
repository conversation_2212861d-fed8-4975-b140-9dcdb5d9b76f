package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备信息表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("device")
@Schema(name = "Device", description = "设备信息表")
public class Device implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "设备ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "设备编码")
    @TableField("device_code")
    private String deviceCode;

    @Schema(description = "设备名称")
    @TableField("device_name")
    private String deviceName;

    @Schema(description = "设备类型")
    @TableField("device_type")
    private Byte deviceType;

    @Schema(description = "厂商")
    @TableField("vendor")
    private Byte vendor;

    @Schema(description = "型号")
    @TableField("model")
    private String model;

    @Schema(description = "固件版本")
    @TableField("firmware_version")
    private String firmwareVersion;

    @Schema(description = "MAC地址")
    @TableField("mac_address")
    private String macAddress;

    @Schema(description = "IP地址")
    @TableField("ip_address")
    private String ipAddress;

    @Schema(description = "位置信息")
    @TableField("location")
    private String location;

    @Schema(description = "设备状态(0:离线 1:在线 2:故障)")
    @TableField("status")
    private Byte status;

    @Schema(description = "最后上报时间戳")
    @TableField("last_report_time")
    private Long lastReportTime;

    @Schema(description = "边缘程序ID")
    @TableField("edge_program_id")
    private String edgeProgramId;

    @Schema(description = "设备描述")
    @TableField("description")
    private String description;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
