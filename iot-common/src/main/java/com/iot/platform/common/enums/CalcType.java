package com.iot.platform.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算类型枚举
 * 数据库存储数字编码，JSON序列化为描述信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum CalcType {

    /**
     * 计算指定时间窗口内的平均值
     */
    AVG((byte) 1, "平均值", "average", "计算指定时间窗口内的平均值"),

    /**
     * 计算指定时间窗口内的最大值
     */
    MAX((byte) 2, "最大值", "maximum", "计算指定时间窗口内的最大值"),

    /**
     * 计算指定时间窗口内的最小值
     */
    MIN((byte) 3, "最小值", "minimum", "计算指定时间窗口内的最小值"),

    /**
     * 统计数据记录数量
     */
    COUNT((byte) 4, "计数", "count", "统计数据记录数量"),

    /**
     * 计算指定时间窗口内的总和
     */
    SUM((byte) 5, "求和", "sum", "计算指定时间窗口内的总和"),

    /**
     * 计算状态为1的时间占比
     */
    OCCUPANCY_RATE((byte) 6, "占用率", "occupancy_rate", "计算状态为1的时间占比"),

    /**
     * 计算标准差
     */
    STDDEV((byte) 7, "标准差", "standard_deviation", "计算指定时间窗口内的标准差");

    /**
     * 数据库存储值（MyBatis-Plus使用）
     */
    @EnumValue
    private final Byte code;

    /**
     * 中文描述（JSON序列化使用）
     */
    @JsonValue
    private final String description;

    /**
     * 英文标识
     */
    private final String identifier;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 缓存映射，提高查询效率
     */
    private static final Map<Byte, CalcType> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(CalcType::getCode, Function.identity()));

    private static final Map<String, CalcType> IDENTIFIER_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(CalcType::getIdentifier, Function.identity()));

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static Optional<CalcType> fromCode(Byte code) {
        return Optional.ofNullable(CODE_MAP.get(code));
    }

    /**
     * 根据标识获取枚举
     *
     * @param identifier 标识
     * @return 枚举值
     */
    public static Optional<CalcType> fromIdentifier(String identifier) {
        return Optional.ofNullable(IDENTIFIER_MAP.get(identifier));
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 判断是否为聚合函数类型
     *
     * @return 是否为聚合函数类型
     */
    public boolean isAggregateFunction() {
        return this == AVG || this == MAX || this == MIN || this == SUM;
    }

    /**
     * 判断是否为统计类型
     *
     * @return 是否为统计类型
     */
    public boolean isStatisticalType() {
        return this == COUNT || this == OCCUPANCY_RATE;
    }

    /**
     * 判断是否适用于数值型数据
     *
     * @return 是否适用于数值型数据
     */
    public boolean isApplicableToNumericData() {
        return this != OCCUPANCY_RATE;
    }

    /**
     * 判断是否适用于状态型数据
     *
     * @return 是否适用于状态型数据
     */
    public boolean isApplicableToStatusData() {
        return this == COUNT || this == OCCUPANCY_RATE;
    }
}
