package com.iot.platform.common.config;

import com.iot.platform.common.enums.*;
import org.apache.ibatis.type.MappedTypes;

/**
 * 具体的枚举类型处理器实现
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public class EnumTypeHandlers {

    /**
     * 设备类型枚举处理器
     */
    @MappedTypes(DeviceType.class)
    public static class DeviceTypeHandler extends EnumTypeHandler<DeviceType> {
        public DeviceTypeHandler() {
            super(DeviceType.class);
        }
    }

    /**
     * 设备厂商枚举处理器
     */
    @MappedTypes(DeviceVendor.class)
    public static class DeviceVendorHandler extends EnumTypeHandler<DeviceVendor> {
        public DeviceVendorHandler() {
            super(DeviceVendor.class);
        }
    }

    /**
     * 设备状态枚举处理器
     */
    @MappedTypes(DeviceStatus.class)
    public static class DeviceStatusHandler extends EnumTypeHandler<DeviceStatus> {
        public DeviceStatusHandler() {
            super(DeviceStatus.class);
        }
    }

    /**
     * 数据类型码枚举处理器
     */
    @MappedTypes(DataCode.class)
    public static class DataCodeHandler extends EnumTypeHandler<DataCode> {
        public DataCodeHandler() {
            super(DataCode.class);
        }
    }

    /**
     * 计算类型枚举处理器
     */
    @MappedTypes(CalcType.class)
    public static class CalcTypeHandler extends EnumTypeHandler<CalcType> {
        public CalcTypeHandler() {
            super(CalcType.class);
        }
    }

    /**
     * 时间窗口枚举处理器
     */
    @MappedTypes(TimeWindow.class)
    public static class TimeWindowHandler extends EnumTypeHandler<TimeWindow> {
        public TimeWindowHandler() {
            super(TimeWindow.class);
        }
    }
}
