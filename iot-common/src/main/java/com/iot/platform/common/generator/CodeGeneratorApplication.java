package com.iot.platform.common.generator;

import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MyBatis-Plus 代码生成器
 * 根据数据库表结构自动生成实体类、Mapper接口、XML映射文件和Service层代码
 *
 * 生成路径说明：
 * - 实体类(Entity)、Mapper接口、XML文件 → iot-common 模块
 * - Service接口、ServiceImpl、Controller → iot-api 模块
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public class CodeGeneratorApplication {

    /**
     * 数据库连接配置
     */
    private static final String DB_URL = "**************************************************************************************************************************************************************************";
    private static final String DB_USERNAME = "root";
    private static final String DB_PASSWORD = "e_,HUH58FT";

    /**
     * 包路径配置
     */
    private static final String COMMON_PACKAGE = "com.iot.platform.common";
    private static final String API_PACKAGE = "com.iot.platform.api";
    private static final String MODULE_NAME = ""; // 如果有模块名称，在这里设置

    /**
     * 项目路径配置
     * 获取项目根目录路径（向上查找到包含所有模块的根目录）
     */
    private static final String PROJECT_ROOT_PATH = getProjectRootPath();

    // Common 模块路径 - 存放实体、Mapper接口、XML文件
    private static final String COMMON_JAVA_PATH = PROJECT_ROOT_PATH + "/iot-common/src/main/java";
    private static final String COMMON_RESOURCES_PATH = PROJECT_ROOT_PATH + "/iot-common/src/main/resources";

    // API 模块路径 - 存放Service、Controller
    private static final String API_JAVA_PATH = PROJECT_ROOT_PATH + "/iot-api/src/main/java";

    /**
     * 需要生成的表名列表
     */
    private static final List<String> TABLE_NAMES = List.of(
        "device_group",           // 设备分组表
        "device",                 // 设备信息表
        "device_group_relation",  // 设备分组关联表
        "edge_program",           // 边缘程序配置表
        "task",                   // 计算任务表
        "task_result",            // 计算结果表
        "api_auth",               // API认证表
        "system_config",          // 系统配置表
        "system_log"              // 系统日志表
    );

    /**
     * 获取项目根目录路径
     * 从当前工作目录向上查找，直到找到包含 pom.xml 和多个模块目录的根目录
     *
     * @return 项目根目录的绝对路径
     */
    private static String getProjectRootPath() {
        String currentDir = System.getProperty("user.dir");
        java.io.File current = new java.io.File(currentDir);

        // 如果当前目录就是项目根目录（包含 iot-api, iot-common 等模块）
        if (isProjectRoot(current)) {
            return current.getAbsolutePath();
        }

        // 向上查找项目根目录
        java.io.File parent = current.getParentFile();
        while (parent != null) {
            if (isProjectRoot(parent)) {
                return parent.getAbsolutePath();
            }
            parent = parent.getParentFile();
        }

        // 如果没找到，返回当前目录
        System.out.println("警告: 未找到项目根目录，使用当前目录: " + currentDir);
        return currentDir;
    }

    /**
     * 判断是否为项目根目录
     * 项目根目录应该包含 pom.xml 文件和 iot-api, iot-common 等模块目录
     *
     * @param dir 要检查的目录
     * @return 如果是项目根目录返回 true，否则返回 false
     */
    private static boolean isProjectRoot(java.io.File dir) {
        if (!dir.isDirectory()) {
            return false;
        }

        // 检查是否包含根 pom.xml
        java.io.File pomFile = new java.io.File(dir, "pom.xml");
        if (!pomFile.exists()) {
            return false;
        }

        // 检查是否包含主要模块目录
        java.io.File iotApiDir = new java.io.File(dir, "iot-api");
        java.io.File iotCommonDir = new java.io.File(dir, "iot-common");

        return iotApiDir.isDirectory() && iotCommonDir.isDirectory();
    }

    public static void main(String[] args) {
        System.out.println("=== IoT大数据平台代码生成器启动 ===");
        System.out.println("项目根目录: " + PROJECT_ROOT_PATH);
        System.out.println("数据库连接: " + DB_URL);
        System.out.println("Common模块路径: " + COMMON_JAVA_PATH);
        System.out.println("API模块路径: " + API_JAVA_PATH);
        System.out.println("待生成表: " + TABLE_NAMES);
        System.out.println("==================================");

        // 配置不同文件类型的输出路径
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.xml, COMMON_RESOURCES_PATH + "/mapper");
        pathInfo.put(OutputFile.entity, COMMON_JAVA_PATH + "/" + COMMON_PACKAGE.replace(".", "/") + "/entity");
        pathInfo.put(OutputFile.mapper, COMMON_JAVA_PATH + "/" + COMMON_PACKAGE.replace(".", "/") + "/mapper");
        pathInfo.put(OutputFile.service, API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/service");
        pathInfo.put(OutputFile.serviceImpl, API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/service/impl");
        pathInfo.put(OutputFile.controller, API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/controller");

        FastAutoGenerator.create(DB_URL, DB_USERNAME, DB_PASSWORD)
                // 全局配置
                .globalConfig(builder -> {
                    builder.author("IoT Platform Team")                    // 设置作者
                            .enableSpringdoc()
                            .dateType(DateType.TIME_PACK)                  // 时间策略
                            .commentDate("yyyy-MM-dd")                     // 注释日期
                            .outputDir(COMMON_JAVA_PATH)                   // 默认输出目录(会被pathInfo覆盖)
                            .disableOpenDir();                             // 禁止打开输出目录
                })
                // 包配置
                .packageConfig(builder -> {
                    builder.parent("")                                     // 清空父包名，使用完整包名
                            .moduleName(MODULE_NAME)                       // 设置父包模块名
                            .entity(COMMON_PACKAGE + ".entity")           // 实体类包名 - Common模块
                            .mapper(COMMON_PACKAGE + ".mapper")           // Mapper 包名 - Common模块
                            .service(API_PACKAGE + ".service")            // Service 包名 - API模块
                            .serviceImpl(API_PACKAGE + ".service.impl")   // Service Impl 包名 - API模块
                            .controller(API_PACKAGE + ".controller")      // Controller 包名 - API模块
                            .pathInfo(pathInfo);                          // 自定义文件输出路径
                })
                // 策略配置
                .strategyConfig(builder -> {
                    builder.addInclude(TABLE_NAMES)                       // 设置需要生成的表名
                            .addTablePrefix("t_", "c_")                    // 设置过滤表前缀（如果有的话）

                            // 实体类策略配置
                            .entityBuilder()
                            .enableLombok()                                // 开启 lombok 模式
                            .enableTableFieldAnnotation()                 // 开启生成实体时生成字段注解
                            .naming(NamingStrategy.underline_to_camel)     // 数据库表映射到实体的命名策略
                            .columnNaming(NamingStrategy.underline_to_camel) // 数据库表字段映射到实体的命名策略
                            .enableFileOverride()                         // 覆盖已生成文件

                            // Mapper策略配置
                            .mapperBuilder()
                            .enableMapperAnnotation()                     // 开启 @Mapper 注解
                            .enableBaseResultMap()                        // 生成基本的resultMap
                            .enableBaseColumnList()                       // 生成基本的SQL片段
                            .enableFileOverride()                         // 覆盖已生成文件

                            // Service策略配置
                            .serviceBuilder()
                            .formatServiceFileName("%sService")           // 格式化 service 接口文件名称
                            .formatServiceImplFileName("%sServiceImpl")   // 格式化 service 实现类文件名称
                            .enableFileOverride()                         // 覆盖已生成文件

                            // Controller策略配置
                            .controllerBuilder()
                            .enableHyphenStyle()                          // 开启驼峰转连字符
                            .enableRestStyle()                            // 开启生成@RestController 控制器
                            .enableFileOverride();                        // 覆盖已生成文件
                })
                // 模板引擎配置，使用 Freemarker 引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();

        System.out.println("=== 代码生成完成 ===");
        System.out.println("生成文件分布:");
        System.out.println("【iot-common 模块】");
        System.out.println("- 实体类: " + COMMON_JAVA_PATH + "/" + COMMON_PACKAGE.replace(".", "/") + "/entity/");
        System.out.println("- Mapper: " + COMMON_JAVA_PATH + "/" + COMMON_PACKAGE.replace(".", "/") + "/mapper/");
        System.out.println("- XML映射: " + COMMON_RESOURCES_PATH + "/mapper/");
        System.out.println("【iot-api 模块】");
        System.out.println("- Service: " + API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/service/");
        System.out.println("- ServiceImpl: " + API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/service/impl/");
        System.out.println("- Controller: " + API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/controller/");
        System.out.println("====================");
    }

    /**
     * 配置数据库连接信息并生成代码
     *
     * @param dbUrl 数据库连接URL
     * @param username 数据库用户名
     * @param password 数据库密码
     * @param tables 需要生成的表名列表
     */
    public static void generateCode(String dbUrl, String username, String password, List<String> tables) {
        System.out.println("=== 自定义代码生成器启动 ===");
        System.out.println("项目根目录: " + PROJECT_ROOT_PATH);
        System.out.println("数据库连接: " + dbUrl);
        System.out.println("Common模块路径: " + COMMON_JAVA_PATH);
        System.out.println("API模块路径: " + API_JAVA_PATH);
        System.out.println("待生成表: " + tables);
        System.out.println("=============================");

        // 配置不同文件类型的输出路径
        Map<OutputFile, String> pathInfo = new HashMap<>();
        pathInfo.put(OutputFile.xml, COMMON_RESOURCES_PATH + "/mapper");
        pathInfo.put(OutputFile.entity, COMMON_JAVA_PATH + "/" + COMMON_PACKAGE.replace(".", "/") + "/entity");
        pathInfo.put(OutputFile.mapper, COMMON_JAVA_PATH + "/" + COMMON_PACKAGE.replace(".", "/") + "/mapper");
        pathInfo.put(OutputFile.service, API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/service");
        pathInfo.put(OutputFile.serviceImpl, API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/service/impl");
        pathInfo.put(OutputFile.controller, API_JAVA_PATH + "/" + API_PACKAGE.replace(".", "/") + "/controller");

        FastAutoGenerator.create(dbUrl, username, password)
                .globalConfig(builder -> {
                    builder.author("IoT Platform Team")
                            .enableSwagger()
                            .dateType(DateType.TIME_PACK)
                            .commentDate("yyyy-MM-dd")
                            .outputDir(COMMON_JAVA_PATH)
                            .disableOpenDir();
                })
                .packageConfig(builder -> {
                    builder.parent("")
                            .moduleName(MODULE_NAME)
                            .entity(COMMON_PACKAGE + ".entity")
                            .mapper(COMMON_PACKAGE + ".mapper")
                            .service(API_PACKAGE + ".service")
                            .serviceImpl(API_PACKAGE + ".service.impl")
                            .controller(API_PACKAGE + ".controller")
                            .pathInfo(pathInfo);
                })
                .strategyConfig(builder -> {
                    builder.addInclude(tables)
                            .addTablePrefix("t_", "c_")

                            .entityBuilder()
                            .enableLombok()
                            .enableTableFieldAnnotation()
                            .naming(NamingStrategy.underline_to_camel)
                            .columnNaming(NamingStrategy.underline_to_camel)
                            .enableFileOverride()

                            .mapperBuilder()
                            .enableMapperAnnotation()
                            .enableBaseResultMap()
                            .enableBaseColumnList()
                            .enableFileOverride()

                            .serviceBuilder()
                            .formatServiceFileName("%sService")
                            .formatServiceImplFileName("%sServiceImpl")
                            .enableFileOverride()

                            .controllerBuilder()
                            .enableHyphenStyle()
                            .enableRestStyle()
                            .enableFileOverride();
                })
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();

        System.out.println("=== 自定义代码生成完成 ===");
    }
}
