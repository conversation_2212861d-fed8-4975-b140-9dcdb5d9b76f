package com.iot.platform.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * IoT数据类型码枚举
 * 数据库存储数字编码，JSON序列化为描述信息
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum DataCode {

    /**
     * 环境温度传感器数据
     */
    TEMPERATURE((byte) 1, "temperature", "温度", "°C", "环境温度传感器数据"),

    /**
     * 相对湿度传感器数据
     */
    HUMIDITY((byte) 2, "humidity", "湿度", "%", "相对湿度传感器数据"),

    /**
     * PM2.5颗粒物浓度
     */
    PM25((byte) 3, "pm25", "PM2.5", "μg/m³", "PM2.5颗粒物浓度"),

    /**
     * CO2浓度数据
     */
    CO2((byte) 4, "co2", "二氧化碳", "ppm", "CO2浓度数据"),

    /**
     * 人体移动检测
     */
    MOTION((byte) 5, "motion", "人体感应", "0/1", "人体移动检测"),

    /**
     * 环境光照强度
     */
    LIGHT((byte) 6, "light", "光照强度", "lux", "环境光照强度"),

    /**
     * 空间占用状态
     */
    OCCUPANCY((byte) 7, "occupancy", "占用状态", "0/1", "空间占用状态"),

    /**
     * 环境噪音值
     */
    NOISE((byte) 8, "noise", "噪音分贝", "dB", "环境噪音值"),

    BATTERY((byte) 9, "battery", "电量", "%", "设备电量"),

    HCHO((byte) 10, "hcho", "甲醛", "mg/m³", "甲醛"),

    PM10((byte) 11, "pm10", "PM10", "μg/m³", "PM10颗粒物浓度"),

    O3((byte) 12, "o3", "臭氧", "ppm", "臭氧"),

    PIR((byte) 13, "pir", "人体感应", "0/1", "人体移动检测")
    ;



    /**
     * 数据库存储值（MyBatis-Plus使用）
     */
    @EnumValue
    private final Byte code;

    /**
     * 英文标识
     */
    private final String identifier;

    /**
     * 中文描述（JSON序列化使用）
     */
    @JsonValue
    private final String description;

    /**
     * 单位
     */
    private final String unit;

    /**
     * 详细说明
     */
    private final String detail;

    /**
     * 缓存映射，提高查询效率
     */
    private static final Map<Byte, DataCode> CODE_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DataCode::getCode, Function.identity()));

    private static final Map<String, DataCode> IDENTIFIER_MAP = Arrays.stream(values())
            .collect(Collectors.toMap(DataCode::getIdentifier, Function.identity()));

    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static Optional<DataCode> fromCode(Byte code) {
        return Optional.ofNullable(CODE_MAP.get(code));
    }

    /**
     * 根据标识获取枚举
     *
     * @param identifier 标识
     * @return 枚举值
     */
    public static Optional<DataCode> fromIdentifier(String identifier) {
        return Optional.ofNullable(IDENTIFIER_MAP.get(identifier));
    }

    /**
     * 检查编码是否有效
     *
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(Byte code) {
        return CODE_MAP.containsKey(code);
    }

    /**
     * 判断是否为数值型数据
     *
     * @return 是否为数值型数据
     */
    public boolean isNumericData() {
        return this != MOTION && this != OCCUPANCY;
    }

    /**
     * 判断是否为状态型数据
     *
     * @return 是否为状态型数据
     */
    public boolean isStatusData() {
        return this == MOTION || this == OCCUPANCY;
    }

    /**
     * 获取数据单位（带格式化）
     *
     * @return 格式化后的单位
     */
    public String getFormattedUnit() {
        if (unit == null || unit.isEmpty()) {
            return "";
        }
        return "(" + unit + ")";
    }
}
