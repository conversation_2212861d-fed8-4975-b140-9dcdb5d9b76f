package com.iot.platform.common.sharding;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 分表策略接口
 * 定义数据分表的基本操作
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface ShardingStrategy {

    /**
     * 根据时间戳获取表名
     *
     * @param timestamp 时间戳（毫秒）
     * @return 表名
     */
    String getTableName(long timestamp);

    /**
     * 根据日期时间获取表名
     *
     * @param dateTime 日期时间
     * @return 表名
     */
    String getTableName(LocalDateTime dateTime);

    /**
     * 获取指定时间范围内的所有表名
     *
     * @param startTime 开始时间戳（毫秒）
     * @param endTime 结束时间戳（毫秒）
     * @return 表名列表
     */
    List<String> getTableNames(long startTime, long endTime);

    /**
     * 获取当前表名
     *
     * @return 当前表名
     */
    String getCurrentTableName();

    /**
     * 获取下一个表名
     *
     * @return 下一个表名
     */
    String getNextTableName();

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 是否存在
     */
    boolean tableExists(String tableName);

    /**
     * 创建表
     *
     * @param tableName 表名
     * @return 是否创建成功
     */
    boolean createTable(String tableName);

    /**
     * 获取基础表名
     *
     * @return 基础表名
     */
    String getBaseTableName();

    /**
     * 获取分表策略类型
     *
     * @return 策略类型
     */
    ShardingType getShardingType();

    /**
     * 分表策略类型
     */
    enum ShardingType {
        MONTHLY("按月分表"),
        DAILY("按日分表"),
        WEEKLY("按周分表"),
        YEARLY("按年分表");

        private final String description;

        ShardingType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }
}
