package com.iot.platform.common.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.time.LocalDateTime;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 设备分组表
 * </p>
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@Setter
@TableName("device_group")
@Schema(name = "DeviceGroup", description = "设备分组表")
public class DeviceGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分组ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "分组名称")
    @TableField("name")
    private String name;

    @Schema(description = "父分组ID")
    @TableField("parent_id")
    private Long parentId;

    @Schema(description = "分组类型")
    @TableField("group_type")
    private String groupType;

    @Schema(description = "分组层级")
    @TableField("level")
    private Integer level;

    @Schema(description = "排序顺序")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "分组路径")
    @TableField("group_path")
    private String groupPath;

    @Schema(description = "分组描述")
    @TableField("description")
    private String description;

    @Schema(description = "创建时间")
    @TableField("created_at")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField("updated_at")
    private LocalDateTime updatedAt;
}
