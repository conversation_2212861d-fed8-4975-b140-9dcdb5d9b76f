package com.iot.platform.common.sharding;

import com.iot.platform.common.sharding.impl.MonthlyShardingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分表管理服务
 * 负责自动创建分表、清理过期表等操作
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class ShardingService {

    @Value("${iot.sharding.retention-months:12}")
    private int retentionMonths;

    @Value("${iot.sharding.auto-create:true}")
    private boolean autoCreate;

    @Value("${iot.sharding.auto-cleanup:true}")
    private boolean autoCleanup;

    @Autowired
    private MonthlyShardingStrategy monthlyShardingStrategy;

    /**
     * 分表策略缓存
     */
    private final Map<String, ShardingStrategy> strategyCache = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        // 注册分表策略
        registerStrategy("iot_data", monthlyShardingStrategy);

        // 自动创建当前和下个月的表
        if (autoCreate) {
            monthlyShardingStrategy.autoCreateTables();
        }

        log.info("分表管理服务初始化完成: retentionMonths={}, autoCreate={}, autoCleanup={}",
                retentionMonths, autoCreate, autoCleanup);
    }

    /**
     * 注册分表策略
     */
    public void registerStrategy(String baseTableName, ShardingStrategy strategy) {
        strategyCache.put(baseTableName, strategy);
        log.info("注册分表策略: {} -> {}", baseTableName, strategy.getClass().getSimpleName());
    }

    /**
     * 获取分表策略
     */
    public ShardingStrategy getStrategy(String baseTableName) {
        return strategyCache.get(baseTableName);
    }

    /**
     * 根据时间戳获取表名
     */
    public String getTableName(String baseTableName, long timestamp) {
        ShardingStrategy strategy = getStrategy(baseTableName);
        if (strategy != null) {
            return strategy.getTableName(timestamp);
        }
        return baseTableName;
    }

    /**
     * 获取指定时间范围内的所有表名
     */
    public List<String> getTableNames(String baseTableName, long startTime, long endTime) {
        ShardingStrategy strategy = getStrategy(baseTableName);
        if (strategy != null) {
            return strategy.getTableNames(startTime, endTime);
        }
        return List.of(baseTableName);
    }

    /**
     * 确保表存在，如果不存在则创建
     */
    public boolean ensureTableExists(String baseTableName, long timestamp) {
        ShardingStrategy strategy = getStrategy(baseTableName);
        if (strategy != null) {
            String tableName = strategy.getTableName(timestamp);
            if (!strategy.tableExists(tableName)) {
                return strategy.createTable(tableName);
            }
            return true;
        }
        return false;
    }

    /**
     * 定时自动创建下个月的表
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每月1号凌晨执行
    public void autoCreateNextMonthTable() {
        if (!autoCreate) {
            return;
        }

        try {
            log.info("开始自动创建下个月分表");
            monthlyShardingStrategy.autoCreateTables();
            log.info("自动创建下个月分表完成");
        } catch (Exception e) {
            log.error("自动创建下个月分表失败", e);
        }
    }

    /**
     * 定时清理过期表
     */
    @Scheduled(cron = "0 0 2 1 * ?") // 每月1号凌晨2点执行
    public void autoCleanupExpiredTables() {
        if (!autoCleanup) {
            return;
        }

        try {
            log.info("开始自动清理过期分表，保留月数: {}", retentionMonths);
            monthlyShardingStrategy.dropExpiredTables(retentionMonths);
            log.info("自动清理过期分表完成");
        } catch (Exception e) {
            log.error("自动清理过期分表失败", e);
        }
    }

    /**
     * 获取所有分表的统计信息
     */
    public List<MonthlyShardingStrategy.TableStats> getAllTableStats() {
        List<String> existingTables = monthlyShardingStrategy.getExistingTables();
        return existingTables.stream()
                .map(monthlyShardingStrategy::getTableStats)
                .toList();
    }

    /**
     * 手动创建指定月份的表
     */
    public boolean createTableForMonth(int year, int month) {
        try {
            LocalDateTime dateTime = LocalDateTime.of(year, month, 1, 0, 0);
            String tableName = monthlyShardingStrategy.getTableName(dateTime);

            if (monthlyShardingStrategy.tableExists(tableName)) {
                log.info("表已存在: {}", tableName);
                return true;
            }

            boolean success = monthlyShardingStrategy.createTable(tableName);
            if (success) {
                log.info("手动创建分表成功: {}", tableName);
            } else {
                log.error("手动创建分表失败: {}", tableName);
            }

            return success;

        } catch (Exception e) {
            log.error("手动创建分表异常: year={}, month={}", year, month, e);
            return false;
        }
    }

    /**
     * 手动删除指定表
     */
    public boolean dropTable(String tableName) {
        try {
            if (!monthlyShardingStrategy.tableExists(tableName)) {
                log.info("表不存在: {}", tableName);
                return true;
            }

            // 安全检查：只允许删除分表格式的表
            if (!tableName.startsWith("iot_data_") || tableName.length() != 15) {
                log.error("不允许删除非分表格式的表: {}", tableName);
                return false;
            }

            monthlyShardingStrategy.dropTable(tableName);
            log.info("手动删除分表成功: {}", tableName);
            return true;

        } catch (Exception e) {
            log.error("手动删除分表失败: {}", tableName, e);
            return false;
        }
    }

    /**
     * 获取分表管理状态
     */
    public ShardingStatus getShardingStatus() {
        ShardingStatus status = new ShardingStatus();
        status.setRetentionMonths(retentionMonths);
        status.setAutoCreate(autoCreate);
        status.setAutoCleanup(autoCleanup);
        status.setCurrentTableName(monthlyShardingStrategy.getCurrentTableName());
        status.setNextTableName(monthlyShardingStrategy.getNextTableName());

        List<String> existingTables = monthlyShardingStrategy.getExistingTables();
        status.setExistingTableCount(existingTables.size());
        status.setExistingTables(existingTables);

        // 计算总数据量
        List<MonthlyShardingStrategy.TableStats> allStats = getAllTableStats();
        long totalRows = allStats.stream().mapToLong(MonthlyShardingStrategy.TableStats::getRowCount).sum();
        double totalSizeMb = allStats.stream().mapToDouble(MonthlyShardingStrategy.TableStats::getSizeMb).sum();

        status.setTotalRows(totalRows);
        status.setTotalSizeMb(totalSizeMb);

        return status;
    }

    /**
     * 分表状态信息
     */
    public static class ShardingStatus {
        private int retentionMonths;
        private boolean autoCreate;
        private boolean autoCleanup;
        private String currentTableName;
        private String nextTableName;
        private int existingTableCount;
        private List<String> existingTables;
        private long totalRows;
        private double totalSizeMb;

        // Getters and Setters
        public int getRetentionMonths() { return retentionMonths; }
        public void setRetentionMonths(int retentionMonths) { this.retentionMonths = retentionMonths; }

        public boolean isAutoCreate() { return autoCreate; }
        public void setAutoCreate(boolean autoCreate) { this.autoCreate = autoCreate; }

        public boolean isAutoCleanup() { return autoCleanup; }
        public void setAutoCleanup(boolean autoCleanup) { this.autoCleanup = autoCleanup; }

        public String getCurrentTableName() { return currentTableName; }
        public void setCurrentTableName(String currentTableName) { this.currentTableName = currentTableName; }

        public String getNextTableName() { return nextTableName; }
        public void setNextTableName(String nextTableName) { this.nextTableName = nextTableName; }

        public int getExistingTableCount() { return existingTableCount; }
        public void setExistingTableCount(int existingTableCount) { this.existingTableCount = existingTableCount; }

        public List<String> getExistingTables() { return existingTables; }
        public void setExistingTables(List<String> existingTables) { this.existingTables = existingTables; }

        public long getTotalRows() { return totalRows; }
        public void setTotalRows(long totalRows) { this.totalRows = totalRows; }

        public double getTotalSizeMb() { return totalSizeMb; }
        public void setTotalSizeMb(double totalSizeMb) { this.totalSizeMb = totalSizeMb; }
    }
}
