package com.iot.platform.common.response;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // ========== 通用响应码 ==========
    /**
     * 操作成功
     */
    SUCCESS(200, "操作成功"),

    /**
     * 系统错误
     */
    ERROR(500, "系统错误"),

    /**
     * 参数校验失败
     */
    VALIDATION_ERROR(400, "参数校验失败"),

    /**
     * 参数错误
     */
    PARAM_ERROR(400, "参数错误"),

    /**
     * 内部错误
     */
    INTERNAL_ERROR(500, "内部错误"),

    /**
     * 未授权
     */
    UNAUTHORIZED(401, "未授权"),

    /**
     * 无权限
     */
    FORBIDDEN(403, "无权限"),

    /**
     * 资源未找到
     */
    NOT_FOUND(404, "资源未找到"),

    /**
     * 请求方法不支持
     */
    METHOD_NOT_ALLOWED(405, "请求方法不支持"),

    /**
     * 请求过于频繁
     */
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // ========== 认证相关 ==========
    /**
     * Access Key不存在
     */
    ACCESS_KEY_NOT_EXISTS(1001, "Access Key不存在"),

    /**
     * 签名验证失败
     */
    SIGNATURE_INVALID(1002, "签名验证失败"),

    /**
     * 请求时间戳过期
     */
    TIMESTAMP_EXPIRED(1003, "请求时间戳过期"),

    /**
     * API权限不足
     */
    API_PERMISSION_DENIED(1004, "API权限不足"),

    // ========== 设备相关 ==========
    /**
     * 设备不存在
     */
    DEVICE_NOT_EXISTS(2001, "设备不存在"),

    /**
     * 设备已存在
     */
    DEVICE_ALREADY_EXISTS(2002, "设备已存在"),

    /**
     * 设备状态异常
     */
    DEVICE_STATUS_ABNORMAL(2003, "设备状态异常"),

    /**
     * 设备离线
     */
    DEVICE_OFFLINE(2004, "设备离线"),

    /**
     * 设备类型不支持
     */
    DEVICE_TYPE_NOT_SUPPORTED(2005, "设备类型不支持"),

    /**
     * 设备厂商不支持
     */
    DEVICE_VENDOR_NOT_SUPPORTED(2006, "设备厂商不支持"),

    // ========== 数据相关 ==========
    /**
     * 数据格式错误
     */
    DATA_FORMAT_ERROR(3001, "数据格式错误"),

    /**
     * 数据类型不支持
     */
    DATA_TYPE_NOT_SUPPORTED(3002, "数据类型不支持"),

    /**
     * 数据超出范围
     */
    DATA_OUT_OF_RANGE(3003, "数据超出范围"),

    /**
     * 批量数据超限
     */
    BATCH_DATA_EXCEEDED(3004, "批量数据超限"),

    /**
     * 数据重复
     */
    DATA_DUPLICATE(3005, "数据重复"),

    // ========== 任务相关 ==========
    /**
     * 任务不存在
     */
    TASK_NOT_EXISTS(4001, "任务不存在"),

    /**
     * 任务已存在
     */
    TASK_ALREADY_EXISTS(4002, "任务已存在"),

    /**
     * 任务执行失败
     */
    TASK_EXECUTION_FAILED(4003, "任务执行失败"),

    /**
     * 计算类型不支持
     */
    CALC_TYPE_NOT_SUPPORTED(4004, "计算类型不支持"),

    /**
     * 时间窗口不支持
     */
    TIME_WINDOW_NOT_SUPPORTED(4005, "时间窗口不支持"),

    // ========== 边缘程序相关 ==========
    /**
     * 边缘程序不存在
     */
    EDGE_PROGRAM_NOT_EXISTS(5001, "边缘程序不存在"),

    /**
     * 边缘程序离线
     */
    EDGE_PROGRAM_OFFLINE(5002, "边缘程序离线"),

    /**
     * 配置下发失败
     */
    CONFIG_PUSH_FAILED(5003, "配置下发失败"),

    /**
     * 指令下发失败
     */
    COMMAND_SEND_FAILED(5004, "指令下发失败"),

    // ========== 分组相关 ==========
    /**
     * 分组不存在
     */
    GROUP_NOT_EXISTS(6001, "分组不存在"),

    /**
     * 分组已存在
     */
    GROUP_ALREADY_EXISTS(6002, "分组已存在"),

    /**
     * 分组层级超限
     */
    GROUP_LEVEL_EXCEEDED(6003, "分组层级超限"),

    /**
     * 无法删除非空分组
     */
    GROUP_NOT_EMPTY(6004, "无法删除非空分组"),

    // ========== 新增响应码 ==========
    /**
     * 设备未找到
     */
    DEVICE_NOT_FOUND(7001, "设备未找到"),

    /**
     * 数据处理错误
     */
    DATA_PROCESS_ERROR(7002, "数据处理错误");

    /**
     * 响应码
     */
    private final Integer code;

    /**
     * 响应消息
     */
    private final String message;
}
