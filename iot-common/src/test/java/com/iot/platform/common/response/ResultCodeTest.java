package com.iot.platform.common.response;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ResultCode枚举测试
 * 
 * <AUTHOR> Platform Team
 * @since 2024-12-16
 */
public class ResultCodeTest {

    @Test
    public void testResultCodeValues() {
        // 测试基本响应码
        assertEquals(200, ResultCode.SUCCESS.getCode());
        assertEquals("操作成功", ResultCode.SUCCESS.getMessage());
        
        assertEquals(500, ResultCode.ERROR.getCode());
        assertEquals("系统错误", ResultCode.ERROR.getMessage());
        
        assertEquals(404, ResultCode.NOT_FOUND.getCode());
        assertEquals("资源未找到", ResultCode.NOT_FOUND.getMessage());
        
        // 测试新增的响应码
        assertEquals(7001, ResultCode.DEVICE_NOT_FOUND.getCode());
        assertEquals("设备未找到", ResultCode.DEVICE_NOT_FOUND.getMessage());
        
        assertEquals(7002, ResultCode.DATA_PROCESS_ERROR.getCode());
        assertEquals("数据处理错误", ResultCode.DATA_PROCESS_ERROR.getMessage());
    }

    @Test
    public void testAllResultCodesHaveValidValues() {
        for (ResultCode code : ResultCode.values()) {
            assertNotNull(code.getCode(), "响应码不能为空: " + code.name());
            assertNotNull(code.getMessage(), "响应消息不能为空: " + code.name());
            assertTrue(code.getCode() >= 200 && code.getCode() < 8000, 
                    "响应码应在合理范围内: " + code.getCode());
        }
    }
}
