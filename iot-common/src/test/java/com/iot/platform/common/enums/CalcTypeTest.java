package com.iot.platform.common.enums;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * CalcType枚举测试
 *
 * <AUTHOR> Platform Team
 * @since 2024-12-16
 */
public class CalcTypeTest {

    @Test
    public void testCalcTypeValues() {
        // 测试基本计算类型
        assertEquals((byte) 1, CalcType.AVG.getCode());
        assertEquals("平均值", CalcType.AVG.getDescription());
        assertEquals("average", CalcType.AVG.getIdentifier());

        assertEquals((byte) 2, CalcType.MAX.getCode());
        assertEquals("最大值", CalcType.MAX.getDescription());

        assertEquals((byte) 3, CalcType.MIN.getCode());
        assertEquals("最小值", CalcType.MIN.getDescription());

        assertEquals((byte) 4, CalcType.COUNT.getCode());
        assertEquals("计数", CalcType.COUNT.getDescription());

        assertEquals((byte) 7, CalcType.STDDEV.getCode());
        assertEquals("标准差", CalcType.STDDEV.getDescription());
    }

    @Test
    public void testFromCodeMethod() {
        // 测试fromCode方法
        assertEquals(CalcType.AVG, CalcType.fromCode((byte) 1).orElse(null));
        assertEquals(CalcType.MAX, CalcType.fromCode((byte) 2).orElse(null));
        assertEquals(CalcType.MIN, CalcType.fromCode((byte) 3).orElse(null));
        assertEquals(CalcType.COUNT, CalcType.fromCode((byte) 4).orElse(null));
        assertEquals(CalcType.STDDEV, CalcType.fromCode((byte) 7).orElse(null));

        // 测试不存在的code
        assertTrue(CalcType.fromCode((byte) 99).isEmpty());
    }

    @Test
    public void testAllCalcTypesHaveUniqueCode() {
        CalcType[] values = CalcType.values();
        for (int i = 0; i < values.length; i++) {
            for (int j = i + 1; j < values.length; j++) {
                assertNotEquals(values[i].getCode(), values[j].getCode(),
                        "计算类型编码不能重复: " + values[i].name() + " 和 " + values[j].name());
            }
        }
    }
}
