# IoT大数据平台 - Docker Compose配置
# 用于本地开发和测试环境部署

version: '3.8'

services:

  # MQTT Broker (Eclipse Mosquitto)
  mqtt-broker:
    image: eclipse-mosquitto:2.0
    container_name: iot-mqtt
    restart: unless-stopped
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./docker/mosquitto/config:/mosquitto/config
      - ./docker/mosquitto/data:/mosquitto/data
      - ./docker/mosquitto/log:/mosquitto/log
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "mosquitto_pub", "-h", "localhost", "-t", "test", "-m", "health_check"]
      timeout: 10s
      retries: 5

  # API服务
  iot-api:
    build:
      context: .
      dockerfile: iot-api/Dockerfile
    container_name: iot-api
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *********************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: iot_user
      SPRING_DATASOURCE_PASSWORD: iot_pass123
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      IOT_MQTT_BROKER_URL: tcp://mqtt-broker:1883
      JVM_OPTS: "-Xms512m -Xmx1024m"
    volumes:
      - ./logs/api:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      mqtt-broker:
        condition: service_healthy
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      timeout: 30s
      retries: 5
      start_period: 60s

  # 计算引擎服务
  iot-compute:
    build:
      context: .
      dockerfile: iot-compute/Dockerfile
    container_name: iot-compute
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: *********************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: iot_user
      SPRING_DATASOURCE_PASSWORD: iot_pass123
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      IOT_MQTT_BROKER_URL: tcp://mqtt-broker:1883
      JVM_OPTS: "-Xms512m -Xmx1024m"
    volumes:
      - ./logs/compute:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      mqtt-broker:
        condition: service_healthy
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/actuator/health"]
      timeout: 30s
      retries: 5
      start_period: 60s

  # 边缘程序服务
  iot-edge:
    build:
      context: .
      dockerfile: iot-edge/Dockerfile
    container_name: iot-edge
    restart: unless-stopped
    ports:
      - "8082:8082"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      IOT_EDGE_PLATFORM_API_URL: http://iot-api:8080/api
      IOT_EDGE_MQTT_BROKER_URL: tcp://mqtt-broker:1883
      IOT_EDGE_EDGE_ID: docker_edge_001
      IOT_EDGE_EDGE_NAME: Docker边缘程序
      JVM_OPTS: "-Xms256m -Xmx512m"
    volumes:
      - ./logs/edge:/app/logs
    depends_on:
      iot-api:
        condition: service_healthy
      mqtt-broker:
        condition: service_healthy
    networks:
      - iot-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/actuator/health"]
      timeout: 30s
      retries: 5
      start_period: 60s

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: iot-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - iot-network

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: iot-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: iot_grafana_2024
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      - prometheus
    networks:
      - iot-network

# 网络配置
networks:
  iot-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
