#!/bin/bash

# 固定JDK 17
export JAVA_HOME=/Users/<USER>/work/opt/jdk/zulu17.30.15-ca-jdk17.0.1-macosx_aarch64/zulu-17.jdk/Contents/Home
export PATH=$JAVA_HOME/bin:$PATH

echo "🚀 启动IoT平台代码生成器..."

cd $(dirname $0)/..

echo "📋 代码生成器配置说明："
echo "【iot-common 模块】"
echo "  - 实体类(Entity): iot-common/src/main/java/com/iot/platform/common/entity/"
echo "  - Mapper接口: iot-common/src/main/java/com/iot/platform/common/mapper/"
echo "  - XML映射文件: iot-common/src/main/resources/mapper/"
echo ""
echo "【iot-api 模块】"
echo "  - Service接口: iot-api/src/main/java/com/iot/platform/api/service/"
echo "  - ServiceImpl实现: iot-api/src/main/java/com/iot/platform/api/service/impl/"
echo "  - Controller控制器: iot-api/src/main/java/com/iot/platform/api/controller/"
echo ""

echo "⚠️  请确认以下条件："
echo "1. MySQL数据库已启动 (localhost:3306)"
echo "2. 数据库 'iot' 已创建"
echo "3. 用户 'root'"
echo "4. 需要生成的表已存在"
echo ""

read -p "是否继续执行代码生成? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]
then
    echo "🔧 开始生成代码..."

    # 编译并运行代码生成器
    mvn clean compile -q
    if [ $? -eq 0 ]; then
        cd iot-common
        mvn exec:java -Dexec.mainClass="com.iot.platform.common.generator.CodeGeneratorApplication" -q
        cd ..

        if [ $? -eq 0 ]; then
            echo "✅ 代码生成完成！"
            echo ""
            echo "📂 请检查生成的文件："
            echo "【iot-common 模块】"
            find iot-common/src -name "*.java" -path "*/entity/*" -o -path "*/mapper/*" 2>/dev/null | head -5
            find iot-common/src -name "*.xml" 2>/dev/null | head -5
            echo "【iot-api 模块】"
            find iot-api/src -name "*Service*.java" -o -name "*Controller*.java" 2>/dev/null | head -10
            echo ""
            echo "💡 提示：生成后需要在 iot-api 模块中添加对 iot-common 的依赖"
        else
            echo "❌ 代码生成失败，请检查数据库连接和配置"
        fi
    else
        echo "❌ 编译失败，请检查代码"
    fi
else
    echo "❌ 用户取消操作"
fi
