#!/bin/bash

# IoT平台项目构建脚本
# 作者: IoT Platform Team
# 日期: 2025-07-22

set -e

echo "========================================="
echo "🚀 开始构建IoT大数据平台"
echo "========================================="

# 检查Java和Maven环境
echo "📋 检查构建环境..."
java -version
mvn -version

# 清理并编译项目
echo "🧹 清理项目..."
mvn clean

echo "📦 编译项目..."
mvn compile

echo "🔧 执行测试..."
mvn test

echo "📦 打包项目..."
mvn package -DskipTests

# 检查构建结果
echo "✅ 检查构建结果..."
if [ ! -f "iot-api/target/iot-api-1.0.0.jar" ]; then
    echo "❌ iot-api模块构建失败"
    exit 1
fi

if [ ! -f "iot-edge/target/iot-edge-1.0.0.jar" ]; then
    echo "❌ iot-edge模块构建失败"
    exit 1
fi

if [ ! -f "iot-compute/target/iot-compute-1.0.0.jar" ]; then
    echo "❌ iot-compute模块构建失败"
    exit 1
fi

echo "========================================="
echo "✅ 构建完成！"
echo "📦 iot-api: iot-api/target/iot-api-1.0.0.jar"
echo "📦 iot-edge: iot-edge/target/iot-edge-1.0.0.jar"
echo "📦 iot-compute: iot-compute/target/iot-compute-1.0.0.jar"
echo "========================================="
