# Mosquitto MQTT Broker 配置文件

# 网络设置
listener 1883
listener 9001
protocol websockets

# 允许匿名连接（生产环境请配置认证）
allow_anonymous true

# 持久化设置
persistence true
persistence_location /mosquitto/data/

# 日志设置
log_dest file /mosquitto/log/mosquitto.log
log_type error
log_type warning
log_type notice
log_type information
log_timestamp true

# 连接设置
max_connections 1000
max_inflight_messages 20
max_queued_messages 100

# 消息设置
message_size_limit 268435456
max_packet_size 268435456

# WebSocket设置
websockets_log_level 255

# 客户端超时设置
keepalive_interval 60
retry_interval 20

# QoS设置
max_inflight_bytes 0
max_queued_bytes 0 