spring:
  profiles:
    active: test
  application:
    name: iot-platform-compute

# 服务器配置
server:
  port: 8002
  servlet:
    context-path: /compute

# 日志配置
logging:
  level:
    com.iot.platform: debug
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"

# 自定义配置
iot:
  compute:
    engine-id: compute-001
    engine-name: 默认计算引擎
    realtime-enabled: true
    batch-enabled: true
    thread-pool:
      core-size: 4
      max-size: 20
      queue-capacity: 100
      keep-alive-seconds: 60
    realtime:
      process-interval: 1000    # 处理间隔（毫秒）
      batch-size: 100           # 批处理大小
      max-wait-time: 5000       # 最大等待时间（毫秒）
    batch:
      cron-expression: "0 */5 * * * ?"  # 每5分钟执行一次
      batch-size: 1000          # 批量处理大小
      parallelism: 4            # 并行度
    cache:
      expire-seconds: 3600      # 缓存过期时间（秒）
      max-size: 10000           # 最大缓存大小
      enabled: true             # 是否启用缓存


