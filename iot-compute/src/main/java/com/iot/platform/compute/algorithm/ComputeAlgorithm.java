package com.iot.platform.compute.algorithm;

import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;

import java.util.List;
import java.util.Map;

/**
 * 计算算法接口
 * 定义各种数据计算算法的统一接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface ComputeAlgorithm {

    /**
     * 获取算法类型
     *
     * @return 算法类型标识
     */
    String getAlgorithmType();

    /**
     * 获取算法名称
     *
     * @return 算法名称
     */
    String getAlgorithmName();

    /**
     * 获取算法描述
     *
     * @return 算法描述
     */
    String getDescription();

    /**
     * 获取计算类型
     *
     * @return 计算类型
     */
    com.iot.platform.common.enums.CalcType getCalcType();

    /**
     * 检查算法是否支持指定的计算任务
     *
     * @param task 计算任务
     * @return 是否支持
     */
    boolean supports(ComputeTask task);

    /**
     * 执行计算
     *
     * @param task 计算任务
     * @param dataPoints 数据点列表
     * @return 计算结果
     */
    ComputeResult compute(ComputeTask task, List<Map<String, Object>> dataPoints);

    /**
     * 验证输入数据
     *
     * @param dataPoints 数据点列表
     * @return 验证结果
     */
    boolean validateInput(List<Map<String, Object>> dataPoints);

    /**
     * 获取算法配置参数
     *
     * @return 配置参数说明
     */
    Map<String, Object> getConfigParameters();

    /**
     * 设置算法配置
     *
     * @param config 配置参数
     * @return 设置结果
     */
    boolean configure(Map<String, Object> config);

    /**
     * 获取算法性能指标
     *
     * @return 性能指标
     */
    Map<String, Object> getPerformanceMetrics();

    /**
     * 重置算法状态
     */
    void reset();
}
