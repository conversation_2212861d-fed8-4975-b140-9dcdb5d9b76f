package com.iot.platform.compute.service;

import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 批量计算服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface BatchComputeService {

    /**
     * 执行批量计算任务
     *
     * @param task 计算任务
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 计算结果
     */
    ComputeResult executeBatchTask(ComputeTask task, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 执行所有批量计算任务
     *
     * @return 计算结果列表
     */
    List<ComputeResult> executeAllBatchTasks();

    /**
     * 按计划执行批量计算
     *
     * @param taskId 任务ID
     * @return 计算结果
     */
    ComputeResult executeScheduledTask(Long taskId);

    /**
     * 获取历史计算结果
     *
     * @param taskId 任务ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 历史结果列表
     */
    List<ComputeResult> getHistoryResults(Long taskId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 重新计算历史数据
     *
     * @param taskId 任务ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 重新计算的结果
     */
    ComputeResult recalculateHistory(Long taskId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取任务的下次执行时间
     *
     * @param task 计算任务
     * @return 下次执行时间
     */
    LocalDateTime getNextExecutionTime(ComputeTask task);
}
