package com.iot.platform.compute.model;

import com.iot.platform.common.enums.CalcType;
import com.iot.platform.common.enums.TimeWindow;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 计算任务模型
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
public class ComputeTask {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 设备识别码（null表示所有设备）
     */
    private String deviceCode;

    /**
     * 设备分组ID（null表示所有分组）
     */
    private Long groupId;

    /**
     * 数据类型码（null表示所有数据类型）
     */
    private Byte dataCode;

    /**
     * 计算类型
     */
    private CalcType calcType;

    /**
     * 时间窗口
     */
    private TimeWindow timeWindow;

    /**
     * 任务描述
     */
    private String description;

    /**
     * 任务状态：1-启用，0-禁用
     */
    private Byte status;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 扩展参数
     */
    private Map<String, Object> parameters;

    /**
     * 最后执行时间
     */
    private LocalDateTime lastExecuteTime;

    /**
     * 下次执行时间
     */
    private LocalDateTime nextExecuteTime;

    /**
     * 执行结果
     */
    private Object lastResult;

    /**
     * 构造函数
     */
    public ComputeTask() {
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.status = 1;
    }
}
