package com.iot.platform.compute.scheduler;

import com.iot.platform.compute.config.ComputeProperties;
import com.iot.platform.compute.model.ComputeTask;
import com.iot.platform.compute.service.ComputeEngineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 计算任务调度器
 * 负责定时执行计算任务和任务管理
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ComputeTaskScheduler {

    private final ComputeEngineService computeEngineService;
    private final ComputeProperties computeProperties;

    /**
     * 调度统计信息
     */
    private final AtomicLong totalScheduledTasks = new AtomicLong(0);
    private final AtomicLong successfulTasks = new AtomicLong(0);
    private final AtomicLong failedTasks = new AtomicLong(0);

    /**
     * 任务执行历史
     */
    private final ConcurrentHashMap<Long, LocalDateTime> taskExecutionHistory = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        log.info("计算任务调度器初始化完成");
    }

    /**
     * 每分钟执行的实时计算任务调度
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void scheduleRealtimeTasks() {
        if (!computeProperties.getRealtimeEnabled()) {
            return;
        }

        try {
            List<ComputeTask> activeTasks = computeEngineService.getActiveTasks();

            if (activeTasks.isEmpty()) {
                log.debug("没有活跃的实时计算任务");
                return;
            }

            log.debug("开始调度实时计算任务: count={}", activeTasks.size());

            for (ComputeTask task : activeTasks) {
                try {
                    if (shouldExecuteTask(task)) {
                        scheduleTask(task);
                    }
                } catch (Exception e) {
                    log.error("调度实时计算任务失败: taskId={}", task.getTaskId(), e);
                    failedTasks.incrementAndGet();
                }
            }

        } catch (Exception e) {
            log.error("实时计算任务调度异常", e);
        }
    }

    /**
     * 每5分钟执行的批量计算任务调度
     */
    @Scheduled(cron = "0 */5 * * * ?") // 每5分钟执行一次
    public void scheduleBatchTasks() {
        if (!computeProperties.getBatchEnabled()) {
            return;
        }

        try {
            log.info("开始执行批量计算任务调度");

            // TODO: 从数据库获取需要执行的批量计算任务
            List<ComputeTask> batchTasks = getBatchTasksFromDatabase();

            if (batchTasks.isEmpty()) {
                log.debug("没有需要执行的批量计算任务");
                return;
            }

            log.info("发现批量计算任务: count={}", batchTasks.size());

            // 批量执行任务
            computeEngineService.executeBatchTasks(batchTasks);

            // 更新统计信息
            totalScheduledTasks.addAndGet(batchTasks.size());

        } catch (Exception e) {
            log.error("批量计算任务调度异常", e);
        }
    }

    /**
     * 每小时执行的任务清理
     */
    @Scheduled(cron = "0 0 * * * ?") // 每小时执行一次
    public void cleanupTasks() {
        try {
            log.info("开始清理过期任务和结果");

            // 清理过期的计算结果
            computeEngineService.cleanExpiredResults();

            // 清理过期的执行历史
            cleanupExecutionHistory();

            log.info("任务清理完成");

        } catch (Exception e) {
            log.error("任务清理异常", e);
        }
    }

    /**
     * 每天执行的统计报告
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void generateDailyReport() {
        try {
            log.info("生成每日调度统计报告");

            long total = totalScheduledTasks.get();
            long success = successfulTasks.get();
            long failed = failedTasks.get();
            double successRate = total > 0 ? (double) success / total * 100 : 0;

            log.info("每日调度统计: 总任务数={}, 成功数={}, 失败数={}, 成功率={:.2f}%",
                    total, success, failed, successRate);

            // TODO: 可以将统计信息保存到数据库或发送到监控系统

        } catch (Exception e) {
            log.error("生成每日统计报告异常", e);
        }
    }

    /**
     * 调度单个任务
     */
    private void scheduleTask(ComputeTask task) {
        try {
            log.debug("调度计算任务: taskId={}, type={}", task.getTaskId(), task.getCalcType());

            // 异步执行任务
            computeEngineService.executeTaskAsync(task)
                    .thenAccept(result -> {
                        if (result.getStatus() == 1) {
                            successfulTasks.incrementAndGet();
                            log.debug("计算任务执行成功: taskId={}", task.getTaskId());
                        } else {
                            failedTasks.incrementAndGet();
                            log.warn("计算任务执行失败: taskId={}, error={}",
                                    task.getTaskId(), result.getErrorMessage());
                        }
                    })
                    .exceptionally(throwable -> {
                        failedTasks.incrementAndGet();
                        log.error("计算任务执行异常: taskId={}", task.getTaskId(), throwable);
                        return null;
                    });

            // 记录执行历史
            taskExecutionHistory.put(task.getTaskId(), LocalDateTime.now());
            totalScheduledTasks.incrementAndGet();

        } catch (Exception e) {
            log.error("调度计算任务失败: taskId={}", task.getTaskId(), e);
            failedTasks.incrementAndGet();
        }
    }

    /**
     * 判断任务是否应该执行
     */
    private boolean shouldExecuteTask(ComputeTask task) {
        if (task.getStatus() != 1) {
            return false; // 任务已禁用
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastExecution = taskExecutionHistory.get(task.getTaskId());

        if (lastExecution == null) {
            return true; // 从未执行过
        }

        // 根据时间窗口判断是否需要执行
        long intervalMinutes = getTaskIntervalMinutes(task);
        return now.isAfter(lastExecution.plusMinutes(intervalMinutes));
    }

    /**
     * 获取任务执行间隔（分钟）
     */
    private long getTaskIntervalMinutes(ComputeTask task) {
        // 根据时间窗口类型确定执行间隔
        return switch (task.getTimeWindow()) {
            case MIN_5 -> 5;
            case MIN_15 -> 15;
            case HOUR_1 -> 60;
            case DAY_1 -> 1440;
        };
    }

    /**
     * 从数据库获取批量计算任务
     */
    private List<ComputeTask> getBatchTasksFromDatabase() {
        // TODO: 实现从数据库查询需要执行的批量计算任务
        // 这里返回空列表作为示例
        return List.of();
    }

    /**
     * 清理过期的执行历史
     */
    private void cleanupExecutionHistory() {
        LocalDateTime expireTime = LocalDateTime.now().minusDays(7); // 保留7天

        taskExecutionHistory.entrySet().removeIf(entry ->
                entry.getValue().isBefore(expireTime));

        log.debug("清理过期执行历史完成: remaining={}", taskExecutionHistory.size());
    }

    /**
     * 获取调度统计信息
     */
    public SchedulerStatistics getStatistics() {
        SchedulerStatistics stats = new SchedulerStatistics();
        stats.setTotalScheduledTasks(totalScheduledTasks.get());
        stats.setSuccessfulTasks(successfulTasks.get());
        stats.setFailedTasks(failedTasks.get());
        stats.setActiveTaskCount(computeEngineService.getActiveTasks().size());
        stats.setExecutionHistorySize(taskExecutionHistory.size());

        long total = totalScheduledTasks.get();
        stats.setSuccessRate(total > 0 ? (double) successfulTasks.get() / total * 100 : 0);

        return stats;
    }

    /**
     * 调度器统计信息
     */
    public static class SchedulerStatistics {
        private long totalScheduledTasks;
        private long successfulTasks;
        private long failedTasks;
        private int activeTaskCount;
        private int executionHistorySize;
        private double successRate;

        // Getters and Setters
        public long getTotalScheduledTasks() { return totalScheduledTasks; }
        public void setTotalScheduledTasks(long totalScheduledTasks) { this.totalScheduledTasks = totalScheduledTasks; }

        public long getSuccessfulTasks() { return successfulTasks; }
        public void setSuccessfulTasks(long successfulTasks) { this.successfulTasks = successfulTasks; }

        public long getFailedTasks() { return failedTasks; }
        public void setFailedTasks(long failedTasks) { this.failedTasks = failedTasks; }

        public int getActiveTaskCount() { return activeTaskCount; }
        public void setActiveTaskCount(int activeTaskCount) { this.activeTaskCount = activeTaskCount; }

        public int getExecutionHistorySize() { return executionHistorySize; }
        public void setExecutionHistorySize(int executionHistorySize) { this.executionHistorySize = executionHistorySize; }

        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }
    }
}
