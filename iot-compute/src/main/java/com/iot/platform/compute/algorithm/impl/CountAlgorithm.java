package com.iot.platform.compute.algorithm.impl;

import com.iot.platform.compute.algorithm.ComputeAlgorithm;
import com.iot.platform.compute.model.ComputeContext;
import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;
import com.iot.platform.common.enums.CalcType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计数算法
 * 计算指定时间窗口内的数据条数
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class CountAlgorithm implements ComputeAlgorithm {

    @Override
    public String getAlgorithmType() {
        return "COUNT";
    }

    @Override
    public String getAlgorithmName() {
        return "计数算法";
    }

    @Override
    public String getDescription() {
        return "计算指定时间窗口内数据的条数";
    }

    @Override
    public CalcType getCalcType() {
        return CalcType.COUNT;
    }

    @Override
    public boolean supports(ComputeTask task) {
        return task != null && CalcType.COUNT.equals(task.getCalcType());
    }

    @Override
    public void reset() {
        // 计数算法是无状态的，无需重置
        log.debug("重置计数算法状态");
    }

    @Override
    public Map<String, Object> getConfigParameters() {
        Map<String, Object> params = new HashMap<>();
        params.put("includeNull", "是否包含空值，默认false");
        params.put("distinct", "是否去重计数，默认false");
        return params;
    }

    @Override
    public boolean configure(Map<String, Object> config) {
        // 计数算法无需特殊配置
        log.debug("配置计数算法: {}", config);
        return true;
    }

    @Override
    public boolean validateInput(List<Map<String, Object>> dataPoints) {
        // 计数算法对输入数据要求较宽松，即使为空也可以返回0
        return true;
    }

    @Override
    public ComputeResult compute(ComputeTask task, List<Map<String, Object>> dataPoints) {
        try {
            // 计算总数和有效数据数
            int totalCount = dataPoints != null ? dataPoints.size() : 0;
            long validCount = 0;

            if (dataPoints != null) {
                validCount = dataPoints.stream()
                        .filter(point -> point != null && point.containsKey("value"))
                        .filter(point -> point.get("value") != null)
                        .count();
            }

            // 构建结果
            ComputeResult result = new ComputeResult();
            result.setTaskId(task.getTaskId());
            result.setProjectId(task.getProjectId());
            result.setDeviceCode(task.getDeviceCode());
            result.setDataCode(task.getDataCode());
            result.setCalcType(getCalcType());
            result.setCalcValue(new BigDecimal(validCount));
            result.setDataCount(totalCount);
            result.setComputeTime(System.currentTimeMillis());

            log.debug("计数计算完成: task={}, totalCount={}, validCount={}",
                    task.getTaskId(), totalCount, validCount);

            return result;

        } catch (Exception e) {
            log.error("计算计数时发生错误: task={}", task.getTaskId(), e);
            return ComputeResult.error(task.getTaskId().toString(), getCalcType(), e.getMessage());
        }
    }

    public ComputeResult compute(ComputeContext context) {
        try {
            List<BigDecimal> values = context.getDataValues();

            if (values == null) {
                log.warn("计算计数时数据为空: task={}", context.getTaskId());
                return ComputeResult.empty(context.getTaskId(), getCalcType());
            }

            // 计算总数和有效数据数
            int totalCount = values.size();
            long validCount = values.stream()
                    .filter(value -> value != null)
                    .count();

            // 构建结果
            ComputeResult result = new ComputeResult();
            result.setTaskId(Long.parseLong(context.getTaskId()));
            result.setDeviceCode(context.getDeviceCode());
            result.setDataCode(context.getDataCode());
            result.setCalcType(getCalcType());
            result.setCalcValue(new BigDecimal(validCount));
            result.setStartTime(context.getStartTime());
            result.setEndTime(context.getEndTime());
            result.setDataCount(totalCount);
            result.setComputeTime(System.currentTimeMillis());

            // 添加扩展信息
            Map<String, Object> extInfo = result.getExtInfo();
            extInfo.put("algorithm", getAlgorithmName());
            extInfo.put("totalCount", totalCount);
            extInfo.put("validCount", validCount);
            extInfo.put("nullCount", totalCount - validCount);

            // 计算数据完整性百分比
            if (totalCount > 0) {
                double completeness = (double) validCount / totalCount * 100;
                extInfo.put("completenessPercent", Math.round(completeness * 100.0) / 100.0);
            }

            // 计算时间跨度
            if (context.getStartTime() != null && context.getEndTime() != null) {
                Duration duration = Duration.between(context.getStartTime(), context.getEndTime());
                long timeSpan = duration.toMillis();
                extInfo.put("timeSpanMs", timeSpan);
                extInfo.put("timeSpanMinutes", timeSpan / (1000 * 60));
            }

            log.debug("计数计算完成: device={}, dataCode={}, totalCount={}, validCount={}",
                    context.getDeviceCode(), context.getDataCode(), totalCount, validCount);

            return result;

        } catch (Exception e) {
            log.error("计数计算失败: task={}", context.getTaskId(), e);
            return ComputeResult.error(context.getTaskId(), getCalcType(), "计算异常: " + e.getMessage());
        }
    }

    public boolean validate(ComputeContext context) {
        if (context == null) {
            log.warn("计算上下文为空");
            return false;
        }

        // 计数算法对数据要求较宽松，即使数据为空也可以返回0
        return true;
    }

    @Override
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("algorithmType", getAlgorithmType());
        metrics.put("algorithmName", getAlgorithmName());
        metrics.put("complexity", "O(n)");
        metrics.put("memoryUsage", "O(1)");
        return metrics;
    }
}
