package com.iot.platform.compute.algorithm;

import com.iot.platform.common.enums.CalcType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 计算算法工厂
 * 负责管理和提供各种计算算法实现
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class AlgorithmFactory {

    /**
     * 算法注册表
     */
    private final Map<CalcType, ComputeAlgorithm> algorithmRegistry = new ConcurrentHashMap<>();

    /**
     * 算法统计信息
     */
    private final Map<CalcType, AlgorithmStats> algorithmStats = new ConcurrentHashMap<>();

    /**
     * 自动注入所有算法实现
     */
    @Autowired
    private List<ComputeAlgorithm> algorithms;

    @PostConstruct
    public void init() {
        // 注册所有算法
        for (ComputeAlgorithm algorithm : algorithms) {
            registerAlgorithm(algorithm);
        }

        log.info("算法工厂初始化完成，注册算法数量: {}", algorithmRegistry.size());

        // 打印所有注册的算法
        algorithmRegistry.forEach((calcType, algorithm) -> {
            log.info("注册算法: {} - {} - {}",
                    calcType.getDescription(),
                    algorithm.getAlgorithmName(),
                    algorithm.getDescription());
        });
    }

    /**
     * 注册算法
     */
    public void registerAlgorithm(ComputeAlgorithm algorithm) {
        if (algorithm == null) {
            log.warn("尝试注册空算法");
            return;
        }

        CalcType calcType = algorithm.getCalcType();
        if (calcType == null) {
            log.warn("算法计算类型为空: {}", algorithm.getClass().getSimpleName());
            return;
        }

        if (algorithmRegistry.containsKey(calcType)) {
            log.warn("算法类型已存在，将被覆盖: {} - {}",
                    calcType.getDescription(), algorithm.getAlgorithmName());
        }

        algorithmRegistry.put(calcType, algorithm);
        algorithmStats.put(calcType, new AlgorithmStats());

        log.debug("算法注册成功: {} - {}", calcType.getDescription(), algorithm.getAlgorithmName());
    }

    /**
     * 获取算法实现
     */
    public ComputeAlgorithm getAlgorithm(CalcType calcType) {
        if (calcType == null) {
            log.warn("计算类型为空");
            return null;
        }

        ComputeAlgorithm algorithm = algorithmRegistry.get(calcType);
        if (algorithm == null) {
            log.warn("未找到算法实现: {}", calcType.getDescription());
        }

        return algorithm;
    }

    /**
     * 检查算法是否存在
     */
    public boolean hasAlgorithm(CalcType calcType) {
        return calcType != null && algorithmRegistry.containsKey(calcType);
    }

    /**
     * 获取所有支持的计算类型
     */
    public Set<CalcType> getSupportedCalcTypes() {
        return new HashSet<>(algorithmRegistry.keySet());
    }

    /**
     * 获取所有算法信息
     */
    public List<AlgorithmInfo> getAllAlgorithmInfo() {
        List<AlgorithmInfo> algorithmInfos = new ArrayList<>();

        algorithmRegistry.forEach((calcType, algorithm) -> {
            AlgorithmInfo info = new AlgorithmInfo();
            info.setCalcType(calcType);
            info.setAlgorithmName(algorithm.getAlgorithmName());
            info.setDescription(algorithm.getDescription());
            info.setClassName(algorithm.getClass().getSimpleName());

            AlgorithmStats stats = algorithmStats.get(calcType);
            if (stats != null) {
                info.setExecuteCount(stats.getExecuteCount());
                info.setSuccessCount(stats.getSuccessCount());
                info.setFailureCount(stats.getFailureCount());
                info.setTotalExecuteTime(stats.getTotalExecuteTime());
                info.setAverageExecuteTime(stats.getAverageExecuteTime());
            }

            algorithmInfos.add(info);
        });

        return algorithmInfos;
    }

    /**
     * 记录算法执行统计
     */
    public void recordExecution(CalcType calcType, boolean success, long executeTime) {
        AlgorithmStats stats = algorithmStats.get(calcType);
        if (stats != null) {
            stats.recordExecution(success, executeTime);
        }
    }

    /**
     * 获取算法统计信息
     */
    public AlgorithmStats getAlgorithmStats(CalcType calcType) {
        return algorithmStats.get(calcType);
    }

    /**
     * 重置算法统计信息
     */
    public void resetStats(CalcType calcType) {
        AlgorithmStats stats = algorithmStats.get(calcType);
        if (stats != null) {
            stats.reset();
            log.info("重置算法统计信息: {}", calcType.getDescription());
        }
    }

    /**
     * 重置所有算法统计信息
     */
    public void resetAllStats() {
        algorithmStats.values().forEach(AlgorithmStats::reset);
        log.info("重置所有算法统计信息");
    }

    /**
     * 算法信息
     */
    public static class AlgorithmInfo {
        private CalcType calcType;
        private String algorithmName;
        private String description;
        private String className;
        private long executeCount;
        private long successCount;
        private long failureCount;
        private long totalExecuteTime;
        private double averageExecuteTime;

        // Getters and Setters
        public CalcType getCalcType() { return calcType; }
        public void setCalcType(CalcType calcType) { this.calcType = calcType; }

        public String getAlgorithmName() { return algorithmName; }
        public void setAlgorithmName(String algorithmName) { this.algorithmName = algorithmName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public String getClassName() { return className; }
        public void setClassName(String className) { this.className = className; }

        public long getExecuteCount() { return executeCount; }
        public void setExecuteCount(long executeCount) { this.executeCount = executeCount; }

        public long getSuccessCount() { return successCount; }
        public void setSuccessCount(long successCount) { this.successCount = successCount; }

        public long getFailureCount() { return failureCount; }
        public void setFailureCount(long failureCount) { this.failureCount = failureCount; }

        public long getTotalExecuteTime() { return totalExecuteTime; }
        public void setTotalExecuteTime(long totalExecuteTime) { this.totalExecuteTime = totalExecuteTime; }

        public double getAverageExecuteTime() { return averageExecuteTime; }
        public void setAverageExecuteTime(double averageExecuteTime) { this.averageExecuteTime = averageExecuteTime; }
    }

    /**
     * 算法统计信息
     */
    public static class AlgorithmStats {
        private volatile long executeCount = 0;
        private volatile long successCount = 0;
        private volatile long failureCount = 0;
        private volatile long totalExecuteTime = 0;

        public synchronized void recordExecution(boolean success, long executeTime) {
            executeCount++;
            totalExecuteTime += executeTime;

            if (success) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        public synchronized void reset() {
            executeCount = 0;
            successCount = 0;
            failureCount = 0;
            totalExecuteTime = 0;
        }

        public long getExecuteCount() { return executeCount; }
        public long getSuccessCount() { return successCount; }
        public long getFailureCount() { return failureCount; }
        public long getTotalExecuteTime() { return totalExecuteTime; }

        public double getAverageExecuteTime() {
            return executeCount > 0 ? (double) totalExecuteTime / executeCount : 0.0;
        }

        public double getSuccessRate() {
            return executeCount > 0 ? (double) successCount / executeCount * 100 : 0.0;
        }
    }
}
