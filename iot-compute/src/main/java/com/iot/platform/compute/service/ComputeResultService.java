package com.iot.platform.compute.service;

import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.common.enums.CalcType;
import com.iot.platform.common.enums.TimeWindow;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 计算结果服务接口
 * 负责计算结果的存储、查询和管理
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface ComputeResultService {

    /**
     * 保存计算结果
     *
     * @param result 计算结果
     * @return 保存结果
     */
    boolean saveResult(ComputeResult result);

    /**
     * 批量保存计算结果
     *
     * @param results 计算结果列表
     * @return 保存成功的数量
     */
    int saveBatchResults(List<ComputeResult> results);

    /**
     * 根据任务ID查询计算结果
     *
     * @param taskId 任务ID
     * @param limit 限制条数
     * @return 计算结果列表
     */
    List<ComputeResult> getResultsByTaskId(Long taskId, Integer limit);

    /**
     * 根据设备编码查询计算结果
     *
     * @param deviceCode 设备编码
     * @param calcType 计算类型
     * @param timeWindow 时间窗口
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 计算结果列表
     */
    List<ComputeResult> getResultsByDevice(String deviceCode, CalcType calcType,
                                          TimeWindow timeWindow, LocalDateTime startTime,
                                          LocalDateTime endTime, Integer limit);

    /**
     * 根据项目ID查询计算结果
     *
     * @param projectId 项目ID
     * @param calcType 计算类型
     * @param timeWindow 时间窗口
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param limit 限制条数
     * @return 计算结果列表
     */
    List<ComputeResult> getResultsByProject(String projectId, CalcType calcType,
                                           TimeWindow timeWindow, LocalDateTime startTime,
                                           LocalDateTime endTime, Integer limit);

    /**
     * 获取最新的计算结果
     *
     * @param deviceCode 设备编码
     * @param dataCode 数据类型码
     * @param calcType 计算类型
     * @param timeWindow 时间窗口
     * @return 最新计算结果
     */
    ComputeResult getLatestResult(String deviceCode, Byte dataCode, CalcType calcType, TimeWindow timeWindow);

    /**
     * 获取计算结果统计信息
     *
     * @param projectId 项目ID（可选）
     * @param deviceCode 设备编码（可选）
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getResultStatistics(String projectId, String deviceCode,
                                           LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 删除过期的计算结果
     *
     * @param retentionDays 保留天数
     * @return 删除的记录数
     */
    long deleteExpiredResults(int retentionDays);

    /**
     * 更新计算结果状态
     *
     * @param resultId 结果ID
     * @param status 新状态
     * @return 更新结果
     */
    boolean updateResultStatus(Long resultId, Byte status);

    /**
     * 检查计算结果是否存在
     *
     * @param taskId 任务ID
     * @param executeTime 执行时间
     * @return 是否存在
     */
    boolean existsResult(Long taskId, LocalDateTime executeTime);

    /**
     * 获取计算结果趋势数据
     *
     * @param deviceCode 设备编码
     * @param dataCode 数据类型码
     * @param calcType 计算类型
     * @param timeWindow 时间窗口
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 趋势数据
     */
    List<Map<String, Object>> getResultTrend(String deviceCode, Byte dataCode, CalcType calcType,
                                            TimeWindow timeWindow, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出计算结果
     *
     * @param projectId 项目ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param format 导出格式（CSV, EXCEL, JSON）
     * @return 导出文件路径
     */
    String exportResults(String projectId, LocalDateTime startTime, LocalDateTime endTime, String format);
}
