package com.iot.platform.compute.service.impl;

import com.iot.platform.compute.algorithm.ComputeAlgorithm;
import com.iot.platform.compute.config.ComputeProperties;
import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;
import com.iot.platform.compute.service.ComputeEngineService;
import com.iot.platform.compute.service.TimeWindowService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 计算引擎服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ComputeEngineServiceImpl implements ComputeEngineService {

    private final ComputeProperties computeProperties;
    private final TimeWindowService timeWindowService;
    private final List<ComputeAlgorithm> algorithms;

    /**
     * 引擎运行状态
     */
    private final AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 实时计算任务缓存
     */
    private final Map<Long, ComputeTask> realtimeTasks = new ConcurrentHashMap<>();

    /**
     * 计算结果缓存
     */
    private final Map<String, ComputeResult> resultCache = new ConcurrentHashMap<>();

    /**
     * 算法映射
     */
    private final Map<String, ComputeAlgorithm> algorithmMap = new HashMap<>();

    /**
     * 线程池
     */
    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        // 初始化算法映射
        for (ComputeAlgorithm algorithm : algorithms) {
            algorithmMap.put(algorithm.getAlgorithmType(), algorithm);
            log.info("注册计算算法: {} - {}", algorithm.getAlgorithmType(), algorithm.getAlgorithmName());
        }

        // 初始化线程池
        int corePoolSize = computeProperties.getThreadPoolSize();
        int maxPoolSize = corePoolSize * 2;
        executorService = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(1000),
                r -> new Thread(r, "compute-engine-" + System.currentTimeMillis()),
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        log.info("计算引擎初始化完成: algorithms={}, threadPool={}", algorithmMap.size(), corePoolSize);
    }

    @PreDestroy
    public void destroy() {
        stopEngine();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("计算引擎已销毁");
    }

    @Override
    public void startEngine() {
        if (running.compareAndSet(false, true)) {
            log.info("计算引擎启动成功");
        } else {
            log.warn("计算引擎已在运行中");
        }
    }

    @Override
    public void stopEngine() {
        if (running.compareAndSet(true, false)) {
            log.info("计算引擎已停止");
        } else {
            log.warn("计算引擎未在运行");
        }
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public ComputeResult executeTask(ComputeTask task) {
        if (!isRunning()) {
            log.warn("计算引擎未启动，无法执行任务: {}", task.getTaskId());
            return createErrorResult(task, "计算引擎未启动");
        }

        try {
            log.debug("开始执行计算任务: taskId={}, type={}", task.getTaskId(), task.getCalcType());

            // 选择合适的算法
            ComputeAlgorithm algorithm = selectAlgorithm(task);
            if (algorithm == null) {
                return createErrorResult(task, "未找到合适的计算算法");
            }

            // 获取数据
            List<Map<String, Object>> dataPoints = fetchDataForTask(task);
            if (dataPoints.isEmpty()) {
                return createErrorResult(task, "没有找到计算数据");
            }

            // 执行计算
            ComputeResult result = algorithm.compute(task, dataPoints);

            // 缓存结果
            if (result != null && result.getStatus() == 1) {
                cacheResult(result);
            }

            log.debug("计算任务执行完成: taskId={}, result={}", task.getTaskId(), result.getResult());
            return result;

        } catch (Exception e) {
            log.error("计算任务执行失败: taskId={}", task.getTaskId(), e);
            return createErrorResult(task, "计算执行异常: " + e.getMessage());
        }
    }

    @Override
    @Async
    public CompletableFuture<ComputeResult> executeTaskAsync(ComputeTask task) {
        return CompletableFuture.supplyAsync(() -> executeTask(task), executorService);
    }

    @Override
    public List<ComputeResult> executeBatchTasks(List<ComputeTask> tasks) {
        if (!isRunning()) {
            log.warn("计算引擎未启动，无法执行批量任务");
            return tasks.stream()
                    .map(task -> createErrorResult(task, "计算引擎未启动"))
                    .collect(Collectors.toList());
        }

        log.info("开始执行批量计算任务: count={}", tasks.size());

        List<CompletableFuture<ComputeResult>> futures = tasks.stream()
                .map(this::executeTaskAsync)
                .collect(Collectors.toList());

        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            // 等待所有任务完成，最多等待5分钟
            allFutures.get(5, TimeUnit.MINUTES);

            List<ComputeResult> results = futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList());

            log.info("批量计算任务执行完成: total={}, success={}",
                    tasks.size(),
                    results.stream().mapToLong(r -> r.getStatus() == 1 ? 1 : 0).sum());

            return results;

        } catch (Exception e) {
            log.error("批量计算任务执行失败", e);
            return tasks.stream()
                    .map(task -> createErrorResult(task, "批量执行异常: " + e.getMessage()))
                    .collect(Collectors.toList());
        }
    }

    @Override
    public void addRealtimeTask(ComputeTask task) {
        if (task.getTaskId() == null) {
            log.warn("实时任务ID不能为空");
            return;
        }

        realtimeTasks.put(task.getTaskId(), task);
        log.info("添加实时计算任务: taskId={}, type={}", task.getTaskId(), task.getCalcType());
    }

    @Override
    public void removeRealtimeTask(Long taskId) {
        ComputeTask removed = realtimeTasks.remove(taskId);
        if (removed != null) {
            log.info("移除实时计算任务: taskId={}", taskId);
        } else {
            log.warn("实时计算任务不存在: taskId={}", taskId);
        }
    }

    @Override
    public List<ComputeTask> getActiveTasks() {
        return new ArrayList<>(realtimeTasks.values());
    }

    @Override
    @Scheduled(fixedRate = 300000) // 每5分钟执行一次
    public void cleanExpiredResults() {
        if (!isRunning()) {
            return;
        }

        try {
            long expireTime = System.currentTimeMillis() - computeProperties.getResultCacheExpireMinutes() * 60 * 1000L;

            Iterator<Map.Entry<String, ComputeResult>> iterator = resultCache.entrySet().iterator();
            int cleanedCount = 0;

            while (iterator.hasNext()) {
                Map.Entry<String, ComputeResult> entry = iterator.next();
                ComputeResult result = entry.getValue();

                if (result.getExecuteTime() != null && result.getExecuteTime().getTime() < expireTime) {
                    iterator.remove();
                    cleanedCount++;
                }
            }

            if (cleanedCount > 0) {
                log.info("清理过期计算结果: count={}, remaining={}", cleanedCount, resultCache.size());
            }

        } catch (Exception e) {
            log.error("清理过期计算结果失败", e);
        }
    }

    /**
     * 定时执行实时计算任务
     */
    @Scheduled(fixedRate = 60000) // 每分钟执行一次
    public void executeRealtimeTasks() {
        if (!isRunning() || realtimeTasks.isEmpty()) {
            return;
        }

        log.debug("执行实时计算任务: count={}", realtimeTasks.size());

        for (ComputeTask task : realtimeTasks.values()) {
            try {
                // 检查任务是否需要执行
                if (shouldExecuteTask(task)) {
                    executeTaskAsync(task);
                    task.setLastExecuteTime(LocalDateTime.now());
                }
            } catch (Exception e) {
                log.error("实时计算任务执行失败: taskId={}", task.getTaskId(), e);
            }
        }
    }

    /**
     * 选择合适的算法
     */
    private ComputeAlgorithm selectAlgorithm(ComputeTask task) {
        for (ComputeAlgorithm algorithm : algorithms) {
            if (algorithm.supports(task)) {
                return algorithm;
            }
        }
        return null;
    }

    /**
     * 获取任务数据
     */
    private List<Map<String, Object>> fetchDataForTask(ComputeTask task) {
        // TODO: 实现从数据库或缓存获取数据的逻辑
        // 这里返回模拟数据
        List<Map<String, Object>> dataPoints = new ArrayList<>();

        // 模拟一些数据点
        for (int i = 0; i < 10; i++) {
            Map<String, Object> dataPoint = new HashMap<>();
            dataPoint.put("deviceCode", task.getDeviceCode());
            dataPoint.put("dataCode", task.getDataCode());
            dataPoint.put("dataValue", Math.random() * 100);
            dataPoint.put("timestamp", System.currentTimeMillis() - i * 60000);
            dataPoints.add(dataPoint);
        }

        return dataPoints;
    }

    /**
     * 缓存计算结果
     */
    private void cacheResult(ComputeResult result) {
        String cacheKey = generateCacheKey(result);
        resultCache.put(cacheKey, result);
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(ComputeResult result) {
        return String.format("%s_%s_%s_%s_%s",
                result.getTaskId(),
                result.getDeviceCode(),
                result.getDataCode(),
                result.getCalcType(),
                result.getTimeWindow());
    }

    /**
     * 创建错误结果
     */
    private ComputeResult createErrorResult(ComputeTask task, String errorMessage) {
        ComputeResult result = new ComputeResult();
        result.setTaskId(task.getTaskId());
        result.setProjectId(task.getProjectId());
        result.setDeviceCode(task.getDeviceCode());
        result.setGroupId(task.getGroupId());
        result.setDataCode(task.getDataCode());
        result.setCalcType(task.getCalcType());
        result.setTimeWindow(task.getTimeWindow());
        result.setStatus((byte) 0);
        result.setErrorMessage(errorMessage);
        result.setExecuteTime(new Date());
        return result;
    }

    /**
     * 检查任务是否需要执行
     */
    private boolean shouldExecuteTask(ComputeTask task) {
        if (task.getStatus() != 1) {
            return false; // 任务已禁用
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastExecute = task.getLastExecuteTime();

        if (lastExecute == null) {
            return true; // 从未执行过
        }

        // 根据时间窗口判断是否需要执行
        long windowMinutes = timeWindowService.getWindowDurationMinutes(task.getTimeWindow());
        return now.isAfter(lastExecute.plusMinutes(windowMinutes));
    }
}
