package com.iot.platform.compute.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 计算引擎配置属性
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Component
@ConfigurationProperties(prefix = "iot.compute")
public class ComputeProperties {

    /**
     * 计算引擎ID
     */
    private String engineId = "compute-001";

    /**
     * 计算引擎名称
     */
    private String engineName = "默认计算引擎";

    /**
     * 是否启用实时计算
     */
    private Boolean realtimeEnabled = true;

    /**
     * 是否启用批量计算
     */
    private Boolean batchEnabled = true;

    /**
     * 线程池配置
     */
    private ThreadPool threadPool = new ThreadPool();

    /**
     * 实时计算配置
     */
    private Realtime realtime = new Realtime();

    /**
     * 批量计算配置
     */
    private Batch batch = new Batch();

    /**
     * 缓存配置
     */
    private Cache cache = new Cache();

    /**
     * 数据源配置
     */
    private DataSource dataSource = new DataSource();

    /**
     * 监控配置
     */
    private Monitor monitor = new Monitor();

    // 便捷方法
    public int getThreadPoolSize() {
        return threadPool.getCoreSize();
    }

    public int getResultCacheExpireMinutes() {
        return cache.getExpireSeconds() / 60;
    }

    @Data
    public static class ThreadPool {
        /**
         * 核心线程数
         */
        private Integer coreSize = 4;

        /**
         * 最大线程数
         */
        private Integer maxSize = 20;

        /**
         * 队列容量
         */
        private Integer queueCapacity = 100;

        /**
         * 线程存活时间（秒）
         */
        private Integer keepAliveSeconds = 60;
    }

    @Data
    public static class Realtime {
        /**
         * 处理间隔（毫秒）
         */
        private Long processInterval = 1000L;

        /**
         * 批处理大小
         */
        private Integer batchSize = 100;

        /**
         * 最大等待时间（毫秒）
         */
        private Long maxWaitTime = 5000L;
    }

    @Data
    public static class Batch {
        /**
         * 批量计算调度表达式
         */
        private String cronExpression = "0 */5 * * * ?";

        /**
         * 批量处理大小
         */
        private Integer batchSize = 1000;

        /**
         * 并行度
         */
        private Integer parallelism = 4;
    }

    @Data
    public static class Cache {
        /**
         * 缓存过期时间（秒）
         */
        private Integer expireSeconds = 3600;

        /**
         * 最大缓存大小
         */
        private Integer maxSize = 10000;

        /**
         * 是否启用缓存
         */
        private Boolean enabled = true;
    }

    @Data
    public static class DataSource {
        /**
         * 数据查询超时时间（秒）
         */
        private Integer queryTimeoutSeconds = 30;

        /**
         * 最大查询记录数
         */
        private Integer maxQueryRecords = 10000;

        /**
         * 数据分页大小
         */
        private Integer pageSize = 1000;

        /**
         * 是否启用数据压缩
         */
        private Boolean compressionEnabled = false;
    }

    @Data
    public static class Monitor {
        /**
         * 是否启用性能监控
         */
        private Boolean enabled = true;

        /**
         * 监控数据收集间隔（秒）
         */
        private Integer collectInterval = 60;

        /**
         * 监控数据保留天数
         */
        private Integer retentionDays = 7;

        /**
         * 是否启用告警
         */
        private Boolean alertEnabled = true;

        /**
         * CPU使用率告警阈值（百分比）
         */
        private Integer cpuThreshold = 80;

        /**
         * 内存使用率告警阈值（百分比）
         */
        private Integer memoryThreshold = 85;

        /**
         * 任务执行时间告警阈值（毫秒）
         */
        private Long executionTimeThreshold = 30000L;
    }
}
