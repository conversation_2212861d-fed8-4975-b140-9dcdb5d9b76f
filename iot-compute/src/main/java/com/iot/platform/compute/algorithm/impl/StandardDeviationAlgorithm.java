package com.iot.platform.compute.algorithm.impl;

import com.iot.platform.compute.algorithm.ComputeAlgorithm;
import com.iot.platform.compute.model.ComputeContext;
import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;
import com.iot.platform.common.enums.CalcType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准差计算算法
 * 计算指定时间窗口内数据的标准差
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class StandardDeviationAlgorithm implements ComputeAlgorithm {

    private static final MathContext MATH_CONTEXT = new MathContext(10, RoundingMode.HALF_UP);

    @Override
    public String getAlgorithmType() {
        return "STANDARD_DEVIATION";
    }

    @Override
    public String getAlgorithmName() {
        return "标准差算法";
    }

    @Override
    public String getDescription() {
        return "计算指定时间窗口内数据的标准差，用于衡量数据的离散程度";
    }

    @Override
    public CalcType getCalcType() {
        return CalcType.STDDEV;
    }

    @Override
    public boolean supports(ComputeTask task) {
        return task != null && CalcType.STDDEV.equals(task.getCalcType());
    }

    @Override
    public void reset() {
        // 标准差算法是无状态的，无需重置
        log.debug("重置标准差算法状态");
    }

    @Override
    public Map<String, Object> getConfigParameters() {
        Map<String, Object> params = new HashMap<>();
        params.put("precision", "计算精度（小数位数），默认4位");
        params.put("populationStdDev", "是否计算总体标准差，默认false（样本标准差）");
        return params;
    }

    @Override
    public boolean configure(Map<String, Object> config) {
        log.debug("配置标准差算法: {}", config);
        return true;
    }

    @Override
    public boolean validateInput(List<Map<String, Object>> dataPoints) {
        if (dataPoints == null || dataPoints.size() < 2) {
            log.warn("标准差计算至少需要2个数据点");
            return false;
        }

        long validCount = dataPoints.stream()
                .filter(point -> point != null && point.containsKey("value"))
                .filter(point -> {
                    Object value = point.get("value");
                    return value instanceof Number;
                })
                .count();

        if (validCount < 2) {
            log.warn("标准差计算至少需要2个有效数值数据点");
            return false;
        }

        return true;
    }

    @Override
    public ComputeResult compute(ComputeTask task, List<Map<String, Object>> dataPoints) {
        try {
            // 验证输入
            if (!validateInput(dataPoints)) {
                return ComputeResult.error(task.getTaskId().toString(), getCalcType(), "输入数据验证失败");
            }

            // 提取数值数据
            List<BigDecimal> values = dataPoints.stream()
                    .filter(point -> point != null && point.containsKey("value"))
                    .map(point -> {
                        Object value = point.get("value");
                        if (value instanceof Number) {
                            return new BigDecimal(value.toString());
                        }
                        return null;
                    })
                    .filter(value -> value != null)
                    .toList();

            if (values.size() < 2) {
                return ComputeResult.empty(task.getTaskId().toString(), getCalcType());
            }

            // 计算平均值
            BigDecimal sum = values.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal mean = sum.divide(new BigDecimal(values.size()), MATH_CONTEXT);

            // 计算方差
            BigDecimal variance = values.stream()
                    .map(value -> value.subtract(mean).pow(2))
                    .reduce(BigDecimal.ZERO, BigDecimal::add)
                    .divide(new BigDecimal(values.size() - 1), MATH_CONTEXT); // 样本标准差

            // 计算标准差
            BigDecimal standardDeviation = sqrt(variance, MATH_CONTEXT);

            // 构建结果
            ComputeResult result = new ComputeResult();
            result.setTaskId(task.getTaskId());
            result.setProjectId(task.getProjectId());
            result.setDeviceCode(task.getDeviceCode());
            result.setDataCode(task.getDataCode());
            result.setCalcType(getCalcType());
            result.setCalcValue(standardDeviation);
            result.setDataCount(values.size());
            result.setComputeTime(System.currentTimeMillis());

            log.debug("标准差计算完成: task={}, result={}, dataCount={}",
                    task.getTaskId(), standardDeviation, values.size());

            return result;

        } catch (Exception e) {
            log.error("计算标准差时发生错误: task={}", task.getTaskId(), e);
            return ComputeResult.error(task.getTaskId().toString(), getCalcType(), e.getMessage());
        }
    }

    public ComputeResult compute(ComputeContext context) {
        try {
            List<BigDecimal> values = context.getDataValues();

            if (values == null || values.isEmpty()) {
                log.warn("计算标准差时数据为空: task={}", context.getTaskId());
                return ComputeResult.empty(context.getTaskId(), getCalcType());
            }

            // 过滤有效数据
            List<BigDecimal> validValues = values.stream()
                    .filter(value -> value != null)
                    .toList();

            if (validValues.size() < 2) {
                log.warn("计算标准差需要至少2个有效数据点: task={}, validCount={}",
                        context.getTaskId(), validValues.size());
                return ComputeResult.empty(context.getTaskId(), getCalcType());
            }

            // 计算平均值
            BigDecimal sum = validValues.stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal mean = sum.divide(new BigDecimal(validValues.size()), MATH_CONTEXT);

            // 计算方差
            BigDecimal variance = calculateVariance(validValues, mean);

            // 计算标准差
            BigDecimal standardDeviation = sqrt(variance, MATH_CONTEXT);

            // 构建结果
            ComputeResult result = new ComputeResult();
            result.setTaskId(Long.parseLong(context.getTaskId()));
            result.setDeviceCode(context.getDeviceCode());
            result.setDataCode(context.getDataCode());
            result.setCalcType(getCalcType());
            result.setCalcValue(standardDeviation);
            result.setStartTime(context.getStartTime());
            result.setEndTime(context.getEndTime());
            result.setDataCount(values.size());
            result.setComputeTime(System.currentTimeMillis());

            // 添加扩展信息
            Map<String, Object> extInfo = result.getExtInfo();
            extInfo.put("algorithm", getAlgorithmName());
            extInfo.put("validDataCount", validValues.size());
            extInfo.put("mean", mean);
            extInfo.put("variance", variance);

            // 计算变异系数（标准差/平均值）
            if (mean.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal coefficientOfVariation = standardDeviation.divide(mean.abs(), MATH_CONTEXT);
                extInfo.put("coefficientOfVariation", coefficientOfVariation);
            }

            log.debug("标准差计算完成: device={}, dataCode={}, stddev={}, mean={}, validCount={}",
                    context.getDeviceCode(), context.getDataCode(), standardDeviation, mean, validValues.size());

            return result;

        } catch (Exception e) {
            log.error("标准差计算失败: task={}", context.getTaskId(), e);
            return ComputeResult.error(context.getTaskId(), getCalcType(), "计算异常: " + e.getMessage());
        }
    }

    public boolean validate(ComputeContext context) {
        if (context == null) {
            log.warn("计算上下文为空");
            return false;
        }

        if (context.getDataValues() == null || context.getDataValues().isEmpty()) {
            log.warn("数据值列表为空: task={}", context.getTaskId());
            return false;
        }

        // 检查是否有足够的有效数据
        long validCount = context.getDataValues().stream()
                .filter(value -> value != null)
                .count();

        if (validCount < 2) {
            log.warn("计算标准差需要至少2个有效数据点: task={}, validCount={}",
                    context.getTaskId(), validCount);
            return false;
        }

        return true;
    }

    /**
     * 计算方差
     */
    private BigDecimal calculateVariance(List<BigDecimal> values, BigDecimal mean) {
        BigDecimal sumOfSquaredDifferences = BigDecimal.ZERO;

        for (BigDecimal value : values) {
            BigDecimal difference = value.subtract(mean);
            BigDecimal squaredDifference = difference.multiply(difference);
            sumOfSquaredDifferences = sumOfSquaredDifferences.add(squaredDifference);
        }

        // 使用样本方差公式（除以n-1）
        return sumOfSquaredDifferences.divide(new BigDecimal(values.size() - 1), MATH_CONTEXT);
    }

    /**
     * 计算平方根（使用牛顿法）
     */
    private BigDecimal sqrt(BigDecimal value, MathContext mathContext) {
        if (value.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        if (value.compareTo(BigDecimal.ZERO) < 0) {
            throw new ArithmeticException("Cannot calculate square root of negative number");
        }

        BigDecimal x = value;
        BigDecimal lastX = BigDecimal.ZERO;

        // 牛顿法迭代
        for (int i = 0; i < 50; i++) {
            lastX = x;
            x = x.add(value.divide(x, mathContext)).divide(new BigDecimal(2), mathContext);

            // 检查收敛
            if (x.subtract(lastX).abs().compareTo(new BigDecimal("0.0000000001")) < 0) {
                break;
            }
        }

        return x;
    }

    @Override
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("algorithmType", getAlgorithmType());
        metrics.put("algorithmName", getAlgorithmName());
        metrics.put("complexity", "O(n)");
        metrics.put("memoryUsage", "O(1)");
        return metrics;
    }
}
