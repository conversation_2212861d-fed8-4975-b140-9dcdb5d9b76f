package com.iot.platform.compute.service;

import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;

import java.util.List;

/**
 * 实时计算服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface RealtimeComputeService {

    /**
     * 启动实时计算
     */
    void startRealtimeCompute();

    /**
     * 停止实时计算
     */
    void stopRealtimeCompute();

    /**
     * 处理实时数据
     *
     * @param deviceCode 设备识别码
     * @param dataCode 数据类型码
     * @param dataValue 数据值
     * @param timestamp 时间戳
     */
    void processRealtimeData(String deviceCode, Byte dataCode, Object dataValue, Long timestamp);

    /**
     * 批量处理实时数据
     *
     * @param dataPoints 数据点列表
     */
    void processBatchRealtimeData(List<DataPoint> dataPoints);

    /**
     * 注册实时计算任务
     *
     * @param task 计算任务
     */
    void registerTask(ComputeTask task);

    /**
     * 注销实时计算任务
     *
     * @param taskId 任务ID
     */
    void unregisterTask(Long taskId);

    /**
     * 获取实时计算结果
     *
     * @param taskId 任务ID
     * @return 计算结果
     */
    ComputeResult getRealtimeResult(Long taskId);

    /**
     * 数据点类
     */
    class DataPoint {
        private String deviceCode;
        private Byte dataCode;
        private Object dataValue;
        private Long timestamp;

        public DataPoint() {}

        public DataPoint(String deviceCode, Byte dataCode, Object dataValue, Long timestamp) {
            this.deviceCode = deviceCode;
            this.dataCode = dataCode;
            this.dataValue = dataValue;
            this.timestamp = timestamp;
        }

        // Getters and Setters
        public String getDeviceCode() { return deviceCode; }
        public void setDeviceCode(String deviceCode) { this.deviceCode = deviceCode; }

        public Byte getDataCode() { return dataCode; }
        public void setDataCode(Byte dataCode) { this.dataCode = dataCode; }

        public Object getDataValue() { return dataValue; }
        public void setDataValue(Object dataValue) { this.dataValue = dataValue; }

        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
    }
}
