package com.iot.platform.compute.algorithm.impl;

import com.iot.platform.compute.algorithm.ComputeAlgorithm;
import com.iot.platform.compute.model.ComputeContext;
import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;
import com.iot.platform.common.enums.CalcType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 最小值计算算法
 * 计算指定时间窗口内的数据最小值
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class MinValueAlgorithm implements ComputeAlgorithm {

    @Override
    public String getAlgorithmType() {
        return "MIN_VALUE";
    }

    @Override
    public String getAlgorithmName() {
        return "最小值算法";
    }

    @Override
    public String getDescription() {
        return "计算指定时间窗口内数据的最小值";
    }

    @Override
    public CalcType getCalcType() {
        return CalcType.MIN;
    }

    @Override
    public boolean supports(ComputeTask task) {
        return task != null && CalcType.MIN.equals(task.getCalcType());
    }

    @Override
    public void reset() {
        // 最小值算法是无状态的，无需重置
        log.debug("重置最小值算法状态");
    }

    @Override
    public Map<String, Object> getConfigParameters() {
        Map<String, Object> params = new HashMap<>();
        params.put("precision", "计算精度（小数位数），默认2位");
        params.put("ignoreNull", "是否忽略空值，默认true");
        return params;
    }

    @Override
    public boolean configure(Map<String, Object> config) {
        // 最小值算法无需特殊配置
        log.debug("配置最小值算法: {}", config);
        return true;
    }

    @Override
    public boolean validateInput(List<Map<String, Object>> dataPoints) {
        if (dataPoints == null || dataPoints.isEmpty()) {
            log.warn("输入数据点为空");
            return false;
        }

        // 检查是否有有效的数值数据
        long validCount = dataPoints.stream()
                .filter(point -> point != null && point.containsKey("value"))
                .filter(point -> {
                    Object value = point.get("value");
                    return value instanceof Number;
                })
                .count();

        if (validCount == 0) {
            log.warn("没有有效的数值数据点");
            return false;
        }

        return true;
    }

    @Override
    public ComputeResult compute(ComputeTask task, List<Map<String, Object>> dataPoints) {
        try {
            // 验证输入
            if (!validateInput(dataPoints)) {
                return ComputeResult.error(task.getTaskId().toString(), getCalcType(), "输入数据验证失败");
            }

            // 提取数值数据
            List<BigDecimal> values = dataPoints.stream()
                    .filter(point -> point != null && point.containsKey("value"))
                    .map(point -> {
                        Object value = point.get("value");
                        if (value instanceof Number) {
                            return new BigDecimal(value.toString());
                        }
                        return null;
                    })
                    .filter(value -> value != null)
                    .toList();

            if (values.isEmpty()) {
                return ComputeResult.empty(task.getTaskId().toString(), getCalcType());
            }

            // 计算最小值
            BigDecimal minValue = values.stream()
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            // 构建结果
            ComputeResult result = new ComputeResult();
            result.setTaskId(task.getTaskId());
            result.setProjectId(task.getProjectId());
            result.setDeviceCode(task.getDeviceCode());
            result.setDataCode(task.getDataCode());
            result.setCalcType(getCalcType());
            result.setCalcValue(minValue);
            result.setDataCount(values.size());
            result.setComputeTime(System.currentTimeMillis());

            log.debug("最小值计算完成: task={}, result={}, dataCount={}",
                    task.getTaskId(), minValue, values.size());

            return result;

        } catch (Exception e) {
            log.error("计算最小值时发生错误: task={}", task.getTaskId(), e);
            return ComputeResult.error(task.getTaskId().toString(), getCalcType(), e.getMessage());
        }
    }

    public ComputeResult compute(ComputeContext context) {
        try {
            List<BigDecimal> values = context.getDataValues();

            if (values == null || values.isEmpty()) {
                log.warn("计算最小值时数据为空: task={}", context.getTaskId());
                return ComputeResult.empty(context.getTaskId(), getCalcType());
            }

            // 计算最小值
            BigDecimal minValue = values.stream()
                    .filter(value -> value != null)
                    .min(BigDecimal::compareTo)
                    .orElse(BigDecimal.ZERO);

            // 构建结果
            ComputeResult result = new ComputeResult();
            result.setTaskId(Long.parseLong(context.getTaskId()));
            result.setDeviceCode(context.getDeviceCode());
            result.setDataCode(context.getDataCode());
            result.setCalcType(getCalcType());
            result.setCalcValue(minValue);
            result.setStartTime(context.getStartTime());
            result.setEndTime(context.getEndTime());
            result.setDataCount(values.size());
            result.setComputeTime(System.currentTimeMillis());

            // 添加扩展信息
            Map<String, Object> extInfo = result.getExtInfo();
            extInfo.put("algorithm", getAlgorithmName());
            extInfo.put("validDataCount", values.stream().mapToInt(v -> v != null ? 1 : 0).sum());

            // 计算最小值对应的时间戳（如果有的话）
            if (context.getTimestamps() != null && !context.getTimestamps().isEmpty()) {
                int minIndex = findMinValueIndex(values);
                if (minIndex >= 0 && minIndex < context.getTimestamps().size()) {
                    extInfo.put("minValueTime", context.getTimestamps().get(minIndex));
                }
            }

            log.debug("最小值计算完成: device={}, dataCode={}, minValue={}, dataCount={}",
                    context.getDeviceCode(), context.getDataCode(), minValue, values.size());

            return result;

        } catch (Exception e) {
            log.error("最小值计算失败: task={}", context.getTaskId(), e);
            return ComputeResult.error(context.getTaskId(), getCalcType(), "计算异常: " + e.getMessage());
        }
    }

    public boolean validate(ComputeContext context) {
        if (context == null) {
            log.warn("计算上下文为空");
            return false;
        }

        if (context.getDataValues() == null || context.getDataValues().isEmpty()) {
            log.warn("数据值列表为空: task={}", context.getTaskId());
            return false;
        }

        // 检查是否有有效数据
        long validCount = context.getDataValues().stream()
                .filter(value -> value != null)
                .count();

        if (validCount == 0) {
            log.warn("没有有效的数据值: task={}", context.getTaskId());
            return false;
        }

        return true;
    }

    /**
     * 查找最小值的索引位置
     */
    private int findMinValueIndex(List<BigDecimal> values) {
        if (values == null || values.isEmpty()) {
            return -1;
        }

        BigDecimal minValue = null;
        int minIndex = -1;

        for (int i = 0; i < values.size(); i++) {
            BigDecimal value = values.get(i);
            if (value != null && (minValue == null || value.compareTo(minValue) < 0)) {
                minValue = value;
                minIndex = i;
            }
        }

        return minIndex;
    }

    @Override
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        metrics.put("algorithmType", getAlgorithmType());
        metrics.put("algorithmName", getAlgorithmName());
        metrics.put("complexity", "O(n)");
        metrics.put("memoryUsage", "O(1)");
        return metrics;
    }
}
