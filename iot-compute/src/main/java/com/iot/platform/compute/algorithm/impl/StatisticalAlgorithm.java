package com.iot.platform.compute.algorithm.impl;

import com.iot.platform.common.enums.CalcType;
import com.iot.platform.compute.algorithm.ComputeAlgorithm;
import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 统计算法实现
 * 支持平均值、最大值、最小值、求和、计数等基础统计计算
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class StatisticalAlgorithm implements ComputeAlgorithm {

    private static final String ALGORITHM_TYPE = "STATISTICAL";
    private static final String ALGORITHM_NAME = "基础统计算法";

    /**
     * 性能统计
     */
    private final AtomicLong computeCount = new AtomicLong(0);
    private final AtomicLong totalExecutionTime = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);

    /**
     * 算法配置
     */
    private Map<String, Object> algorithmConfig = new HashMap<>();

    @Override
    public String getAlgorithmType() {
        return ALGORITHM_TYPE;
    }

    @Override
    public String getAlgorithmName() {
        return ALGORITHM_NAME;
    }

    @Override
    public CalcType getCalcType() {
        // 统计算法支持多种计算类型，这里返回一个默认值
        // 实际使用时应该根据具体的计算任务来确定
        return CalcType.AVG;
    }

    @Override
    public String getDescription() {
        return "提供平均值、最大值、最小值、求和、计数等基础统计计算功能";
    }

    @Override
    public boolean supports(ComputeTask task) {
        if (task == null || task.getCalcType() == null) {
            return false;
        }

        CalcType calcType = task.getCalcType();
        return calcType == CalcType.AVG ||
               calcType == CalcType.MAX ||
               calcType == CalcType.MIN ||
               calcType == CalcType.SUM ||
               calcType == CalcType.COUNT;
    }

    @Override
    public ComputeResult compute(ComputeTask task, List<Map<String, Object>> dataPoints) {
        long startTime = System.currentTimeMillis();

        try {
            // 验证输入
            if (!validateInput(dataPoints)) {
                return createErrorResult(task, "输入数据验证失败");
            }

            // 提取数值数据
            List<Double> values = extractNumericValues(dataPoints);
            if (values.isEmpty()) {
                return createErrorResult(task, "没有有效的数值数据");
            }

            // 执行计算
            Double result = performCalculation(task.getCalcType(), values);

            // 创建计算结果
            ComputeResult computeResult = createSuccessResult(task, result, values.size());

            // 更新性能统计
            long executionTime = System.currentTimeMillis() - startTime;
            updatePerformanceMetrics(executionTime, true);

            log.debug("统计计算完成: task={}, type={}, result={}, dataCount={}, time={}ms",
                    task.getTaskId(), task.getCalcType(), result, values.size(), executionTime);

            return computeResult;

        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;
            updatePerformanceMetrics(executionTime, false);

            log.error("统计计算失败: task={}, type={}", task.getTaskId(), task.getCalcType(), e);
            return createErrorResult(task, "计算执行异常: " + e.getMessage());
        }
    }

    @Override
    public boolean validateInput(List<Map<String, Object>> dataPoints) {
        if (dataPoints == null || dataPoints.isEmpty()) {
            log.warn("输入数据为空");
            return false;
        }

        // 检查数据点格式
        for (Map<String, Object> dataPoint : dataPoints) {
            if (dataPoint == null || !dataPoint.containsKey("dataValue")) {
                log.warn("数据点格式错误: {}", dataPoint);
                return false;
            }
        }

        return true;
    }

    @Override
    public Map<String, Object> getConfigParameters() {
        Map<String, Object> params = new HashMap<>();
        params.put("precision", "计算精度，默认保留2位小数");
        params.put("ignoreNullValues", "是否忽略空值，默认true");
        params.put("ignoreOutliers", "是否忽略异常值，默认false");
        params.put("outlierThreshold", "异常值阈值，默认3倍标准差");
        return params;
    }

    @Override
    public boolean configure(Map<String, Object> config) {
        try {
            if (config != null) {
                this.algorithmConfig.putAll(config);
                log.info("统计算法配置更新: {}", config);
            }
            return true;
        } catch (Exception e) {
            log.error("统计算法配置失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getPerformanceMetrics() {
        Map<String, Object> metrics = new HashMap<>();
        long count = computeCount.get();
        long totalTime = totalExecutionTime.get();

        metrics.put("algorithmType", ALGORITHM_TYPE);
        metrics.put("computeCount", count);
        metrics.put("errorCount", errorCount.get());
        metrics.put("totalExecutionTime", totalTime);
        metrics.put("averageExecutionTime", count > 0 ? totalTime / count : 0);
        metrics.put("successRate", count > 0 ? (count - errorCount.get()) * 100.0 / count : 0);
        metrics.put("lastUpdateTime", System.currentTimeMillis());

        return metrics;
    }

    @Override
    public void reset() {
        computeCount.set(0);
        totalExecutionTime.set(0);
        errorCount.set(0);
        algorithmConfig.clear();
        log.info("统计算法状态已重置");
    }

    /**
     * 提取数值数据
     */
    private List<Double> extractNumericValues(List<Map<String, Object>> dataPoints) {
        boolean ignoreNullValues = (Boolean) algorithmConfig.getOrDefault("ignoreNullValues", true);

        return dataPoints.stream()
                .map(point -> point.get("dataValue"))
                .filter(value -> value != null || !ignoreNullValues)
                .map(this::convertToDouble)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 转换为Double类型
     */
    private Double convertToDouble(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Number) {
                return ((Number) value).doubleValue();
            }
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            log.warn("无法转换为数值: {}", value);
            return null;
        }
    }

    /**
     * 执行具体计算
     */
    private Double performCalculation(CalcType calcType, List<Double> values) {
        return switch (calcType) {
            case AVG -> calculateAverage(values);
            case MAX -> calculateMaximum(values);
            case MIN -> calculateMinimum(values);
            case SUM -> calculateSum(values);
            case COUNT -> (double) values.size();
            default -> throw new IllegalArgumentException("不支持的计算类型: " + calcType);
        };
    }

    /**
     * 计算平均值
     */
    private Double calculateAverage(List<Double> values) {
        return values.stream()
                .mapToDouble(Double::doubleValue)
                .average()
                .orElse(0.0);
    }

    /**
     * 计算最大值
     */
    private Double calculateMaximum(List<Double> values) {
        return values.stream()
                .mapToDouble(Double::doubleValue)
                .max()
                .orElse(0.0);
    }

    /**
     * 计算最小值
     */
    private Double calculateMinimum(List<Double> values) {
        return values.stream()
                .mapToDouble(Double::doubleValue)
                .min()
                .orElse(0.0);
    }

    /**
     * 计算求和
     */
    private Double calculateSum(List<Double> values) {
        return values.stream()
                .mapToDouble(Double::doubleValue)
                .sum();
    }

    /**
     * 创建成功结果
     */
    private ComputeResult createSuccessResult(ComputeTask task, Double result, int dataCount) {
        ComputeResult computeResult = new ComputeResult();
        computeResult.setTaskId(task.getTaskId());
        computeResult.setProjectId(task.getProjectId());
        computeResult.setDeviceCode(task.getDeviceCode());
        computeResult.setGroupId(task.getGroupId());
        computeResult.setDataCode(task.getDataCode());
        computeResult.setCalcType(task.getCalcType());
        computeResult.setTimeWindow(task.getTimeWindow());
        computeResult.setResult(result);
        computeResult.setDataCount(dataCount);
        computeResult.setStatus((byte) 1); // 成功
        computeResult.setExecuteTime(new Date());

        // 添加元数据
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("algorithmType", ALGORITHM_TYPE);
        metadata.put("precision", algorithmConfig.getOrDefault("precision", 2));
        computeResult.setMetadata(metadata);

        return computeResult;
    }

    /**
     * 创建错误结果
     */
    private ComputeResult createErrorResult(ComputeTask task, String errorMessage) {
        ComputeResult computeResult = new ComputeResult();
        computeResult.setTaskId(task.getTaskId());
        computeResult.setProjectId(task.getProjectId());
        computeResult.setDeviceCode(task.getDeviceCode());
        computeResult.setGroupId(task.getGroupId());
        computeResult.setDataCode(task.getDataCode());
        computeResult.setCalcType(task.getCalcType());
        computeResult.setTimeWindow(task.getTimeWindow());
        computeResult.setStatus((byte) 0); // 失败
        computeResult.setErrorMessage(errorMessage);
        computeResult.setExecuteTime(new Date());

        return computeResult;
    }

    /**
     * 更新性能指标
     */
    private void updatePerformanceMetrics(long executionTime, boolean success) {
        computeCount.incrementAndGet();
        totalExecutionTime.addAndGet(executionTime);

        if (!success) {
            errorCount.incrementAndGet();
        }
    }
}
