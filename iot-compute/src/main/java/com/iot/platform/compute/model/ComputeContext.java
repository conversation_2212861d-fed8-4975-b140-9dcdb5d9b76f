package com.iot.platform.compute.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 计算上下文
 * 包含计算过程中需要的上下文信息和配置参数
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-23
 */
@Data
public class ComputeContext {

    /**
     * 计算任务ID
     */
    private String taskId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 数据类型码
     */
    private Byte dataCode;

    /**
     * 计算类型
     */
    private String calcType;

    /**
     * 时间窗口开始时间
     */
    private LocalDateTime windowStart;

    /**
     * 时间窗口结束时间
     */
    private LocalDateTime windowEnd;

    /**
     * 时间窗口大小（秒）
     */
    private Long windowSize;

    /**
     * 计算精度（小数位数）
     */
    private Integer precision = 2;

    /**
     * 是否忽略异常值
     */
    private Boolean ignoreOutliers = false;

    /**
     * 异常值阈值（标准差倍数）
     */
    private Double outlierThreshold = 3.0;

    /**
     * 最小数据点数量要求
     */
    private Integer minDataPoints = 1;

    /**
     * 计算配置参数
     */
    private Map<String, Object> parameters = new HashMap<>();

    /**
     * 扩展属性
     */
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 数据值列表
     */
    private java.util.List<java.math.BigDecimal> dataValues = new java.util.ArrayList<>();

    /**
     * 时间戳列表
     */
    private java.util.List<Long> timestamps = new java.util.ArrayList<>();

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 构造函数
     */
    public ComputeContext() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public ComputeContext(String taskId, String deviceCode, Byte dataCode, String calcType) {
        this();
        this.taskId = taskId;
        this.deviceCode = deviceCode;
        this.dataCode = dataCode;
        this.calcType = calcType;
    }

    /**
     * 设置参数
     */
    public ComputeContext setParameter(String key, Object value) {
        this.parameters.put(key, value);
        return this;
    }

    /**
     * 获取参数
     */
    public Object getParameter(String key) {
        return this.parameters.get(key);
    }

    /**
     * 获取参数（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, T defaultValue) {
        Object value = this.parameters.get(key);
        return value != null ? (T) value : defaultValue;
    }

    /**
     * 设置属性
     */
    public ComputeContext setAttribute(String key, Object value) {
        this.attributes.put(key, value);
        return this;
    }

    /**
     * 获取属性
     */
    public Object getAttribute(String key) {
        return this.attributes.get(key);
    }

    /**
     * 获取属性（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        Object value = this.attributes.get(key);
        return value != null ? (T) value : defaultValue;
    }

    /**
     * 检查是否有足够的数据点
     */
    public boolean hasEnoughDataPoints(int actualCount) {
        return actualCount >= this.minDataPoints;
    }

    /**
     * 检查是否应该忽略异常值
     */
    public boolean shouldIgnoreOutliers() {
        return Boolean.TRUE.equals(this.ignoreOutliers);
    }

    /**
     * 获取格式化的任务描述
     */
    public String getTaskDescription() {
        return String.format("Task[%s]: %s calculation for device %s, dataCode %s, window [%s - %s]",
                taskId, calcType, deviceCode, dataCode, windowStart, windowEnd);
    }

    /**
     * 创建子上下文（用于嵌套计算）
     */
    public ComputeContext createSubContext(String subTaskId) {
        ComputeContext subContext = new ComputeContext();
        subContext.setTaskId(subTaskId);
        subContext.setProjectId(this.projectId);
        subContext.setDeviceCode(this.deviceCode);
        subContext.setDataCode(this.dataCode);
        subContext.setCalcType(this.calcType);
        subContext.setWindowStart(this.windowStart);
        subContext.setWindowEnd(this.windowEnd);
        subContext.setWindowSize(this.windowSize);
        subContext.setPrecision(this.precision);
        subContext.setIgnoreOutliers(this.ignoreOutliers);
        subContext.setOutlierThreshold(this.outlierThreshold);
        subContext.setMinDataPoints(this.minDataPoints);

        // 复制参数和属性
        subContext.getParameters().putAll(this.parameters);
        subContext.getAttributes().putAll(this.attributes);

        return subContext;
    }

    /**
     * 验证上下文的有效性
     */
    public boolean isValid() {
        return taskId != null && !taskId.trim().isEmpty()
                && deviceCode != null && !deviceCode.trim().isEmpty()
                && dataCode != null
                && calcType != null && !calcType.trim().isEmpty()
                && windowStart != null
                && windowEnd != null
                && windowStart.isBefore(windowEnd);
    }

    /**
     * 获取开始时间（兼容性方法）
     */
    public LocalDateTime getStartTime() {
        return this.windowStart;
    }

    /**
     * 获取结束时间（兼容性方法）
     */
    public LocalDateTime getEndTime() {
        return this.windowEnd;
    }

    /**
     * 更新时间戳
     */
    public void touch() {
        this.updateTime = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return String.format("ComputeContext{taskId='%s', deviceCode='%s', dataCode=%s, calcType='%s', window=[%s-%s]}",
                taskId, deviceCode, dataCode, calcType, windowStart, windowEnd);
    }
}
