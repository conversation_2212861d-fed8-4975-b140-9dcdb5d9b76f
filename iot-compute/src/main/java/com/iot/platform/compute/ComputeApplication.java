package com.iot.platform.compute;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * IoT计算引擎主启动类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@SpringBootApplication(scanBasePackages = {
    "com.iot.platform.compute",
    "com.iot.platform.common"
})
@EnableConfigurationProperties
@EnableAsync
@EnableScheduling
public class ComputeApplication {

    public static void main(String[] args) {
        SpringApplication.run(ComputeApplication.class, args);
        System.out.println("""

            ====================================
            🧮 IoT计算引擎启动成功！
            ⚡ 实时计算引擎: 已启动
            📊 批量计算任务: 已准备就绪
            ====================================
            """);
    }
}
