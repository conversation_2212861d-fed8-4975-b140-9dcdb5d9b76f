package com.iot.platform.compute.service;

import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 计算引擎服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface ComputeEngineService {

    /**
     * 启动计算引擎
     */
    void startEngine();

    /**
     * 停止计算引擎
     */
    void stopEngine();

    /**
     * 检查引擎状态
     *
     * @return 是否正在运行
     */
    boolean isRunning();

    /**
     * 执行计算任务
     *
     * @param task 计算任务
     * @return 计算结果
     */
    ComputeResult executeTask(ComputeTask task);

    /**
     * 异步执行计算任务
     *
     * @param task 计算任务
     * @return 异步计算结果
     */
    CompletableFuture<ComputeResult> executeTaskAsync(ComputeTask task);

    /**
     * 批量执行计算任务
     *
     * @param tasks 计算任务列表
     * @return 计算结果列表
     */
    List<ComputeResult> executeBatchTasks(List<ComputeTask> tasks);

    /**
     * 添加实时计算任务
     *
     * @param task 计算任务
     */
    void addRealtimeTask(ComputeTask task);

    /**
     * 移除实时计算任务
     *
     * @param taskId 任务ID
     */
    void removeRealtimeTask(Long taskId);

    /**
     * 获取所有活跃的计算任务
     *
     * @return 任务列表
     */
    List<ComputeTask> getActiveTasks();

    /**
     * 清理过期的计算结果
     */
    void cleanExpiredResults();
}
