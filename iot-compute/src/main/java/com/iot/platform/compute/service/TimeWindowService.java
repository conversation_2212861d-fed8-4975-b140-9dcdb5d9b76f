package com.iot.platform.compute.service;

import com.iot.platform.common.enums.TimeWindow;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 时间窗口服务接口
 * 负责时间窗口的计算和数据分组
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface TimeWindowService {

    /**
     * 计算时间窗口边界
     *
     * @param timeWindow 时间窗口类型
     * @param referenceTime 参考时间
     * @return 时间窗口边界 [开始时间, 结束时间]
     */
    LocalDateTime[] calculateWindowBounds(TimeWindow timeWindow, LocalDateTime referenceTime);

    /**
     * 获取指定时间范围内的所有时间窗口
     *
     * @param timeWindow 时间窗口类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 时间窗口列表
     */
    List<LocalDateTime[]> getTimeWindows(TimeWindow timeWindow, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据时间窗口分组数据
     *
     * @param dataPoints 数据点列表
     * @param timeWindow 时间窗口类型
     * @param timeField 时间字段名
     * @return 分组后的数据
     */
    Map<String, List<Map<String, Object>>> groupDataByTimeWindow(
            List<Map<String, Object>> dataPoints,
            TimeWindow timeWindow,
            String timeField);

    /**
     * 检查时间是否在指定窗口内
     *
     * @param time 检查时间
     * @param windowStart 窗口开始时间
     * @param windowEnd 窗口结束时间
     * @return 是否在窗口内
     */
    boolean isTimeInWindow(LocalDateTime time, LocalDateTime windowStart, LocalDateTime windowEnd);

    /**
     * 获取下一个时间窗口的开始时间
     *
     * @param timeWindow 时间窗口类型
     * @param currentTime 当前时间
     * @return 下一个窗口开始时间
     */
    LocalDateTime getNextWindowStart(TimeWindow timeWindow, LocalDateTime currentTime);

    /**
     * 获取上一个时间窗口的开始时间
     *
     * @param timeWindow 时间窗口类型
     * @param currentTime 当前时间
     * @return 上一个窗口开始时间
     */
    LocalDateTime getPreviousWindowStart(TimeWindow timeWindow, LocalDateTime currentTime);

    /**
     * 计算时间窗口的持续时间（分钟）
     *
     * @param timeWindow 时间窗口类型
     * @return 持续时间（分钟）
     */
    long getWindowDurationMinutes(TimeWindow timeWindow);

    /**
     * 格式化时间窗口标识
     *
     * @param timeWindow 时间窗口类型
     * @param windowStart 窗口开始时间
     * @return 时间窗口标识字符串
     */
    String formatWindowKey(TimeWindow timeWindow, LocalDateTime windowStart);

    /**
     * 解析时间窗口标识
     *
     * @param windowKey 时间窗口标识
     * @return 窗口开始时间
     */
    LocalDateTime parseWindowKey(String windowKey);

    /**
     * 获取时间窗口统计信息
     *
     * @param timeWindow 时间窗口类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    Map<String, Object> getWindowStatistics(TimeWindow timeWindow, LocalDateTime startTime, LocalDateTime endTime);
}
