package com.iot.platform.compute.model;

import com.iot.platform.common.enums.CalcType;
import com.iot.platform.common.enums.TimeWindow;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 计算结果模型
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
public class ComputeResult {

    /**
     * 结果ID
     */
    private Long resultId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 设备识别码
     */
    private String deviceCode;

    /**
     * 设备分组ID
     */
    private Long groupId;

    /**
     * 数据类型码
     */
    private Byte dataCode;

    /**
     * 计算结果值
     */
    private Object resultValue;

    /**
     * 计算开始时间
     */
    private LocalDateTime startTime;

    /**
     * 计算结束时间
     */
    private LocalDateTime endTime;

    /**
     * 数据点数量
     */
    private Integer dataCount;

    /**
     * 计算耗时（毫秒）
     */
    private Long processingTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 扩展信息
     */
    private Map<String, Object> metadata;

    /**
     * 计算类型
     */
    private CalcType calcType;

    /**
     * 时间窗口
     */
    private TimeWindow timeWindow;

    /**
     * 状态
     */
    private Byte status;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 执行时间
     */
    private Date executeTime;

    /**
     * 计算结果（别名方法）
     */
    public Object getResult() {
        return this.resultValue;
    }

    /**
     * 设置计算结果（别名方法）
     */
    public void setResult(Object result) {
        this.resultValue = result;
    }

    /**
     * 构造函数
     */
    public ComputeResult() {
        this.createdAt = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public ComputeResult(Long taskId, String projectId, Object resultValue) {
        this();
        this.taskId = taskId;
        this.projectId = projectId;
        this.resultValue = resultValue;
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建空结果
     */
    public static ComputeResult empty(String taskId, CalcType calcType) {
        ComputeResult result = new ComputeResult();
        result.setTaskId(Long.parseLong(taskId));
        result.setCalcType(calcType);
        result.setStatus((byte) 0); // 空结果状态
        return result;
    }

    /**
     * 创建错误结果
     */
    public static ComputeResult error(String taskId, CalcType calcType, String errorMessage) {
        ComputeResult result = new ComputeResult();
        result.setTaskId(Long.parseLong(taskId));
        result.setCalcType(calcType);
        result.setStatus((byte) -1); // 错误状态
        result.setErrorMessage(errorMessage);
        return result;
    }

    // ========== 兼容性方法 ==========

    /**
     * 设置计算值（兼容性方法）
     */
    public void setCalcValue(java.math.BigDecimal value) {
        this.resultValue = value;
    }

    /**
     * 获取计算值（兼容性方法）
     */
    public java.math.BigDecimal getCalcValue() {
        if (resultValue instanceof java.math.BigDecimal) {
            return (java.math.BigDecimal) resultValue;
        }
        return null;
    }

    /**
     * 设置计算时间（兼容性方法）
     */
    public void setComputeTime(long computeTime) {
        this.processingTime = computeTime;
    }

    /**
     * 获取扩展信息（兼容性方法）
     */
    public Map<String, Object> getExtInfo() {
        return this.metadata;
    }

    /**
     * 设置扩展信息（兼容性方法）
     */
    public void setExtInfo(Map<String, Object> extInfo) {
        this.metadata = extInfo;
    }
}
