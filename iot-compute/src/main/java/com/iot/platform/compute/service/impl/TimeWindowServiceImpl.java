package com.iot.platform.compute.service.impl;

import com.iot.platform.common.enums.TimeWindow;
import com.iot.platform.compute.service.TimeWindowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间窗口服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class TimeWindowServiceImpl implements TimeWindowService {

    private static final DateTimeFormatter WINDOW_KEY_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    @Override
    public LocalDateTime[] calculateWindowBounds(TimeWindow timeWindow, LocalDateTime referenceTime) {
        if (timeWindow == null || referenceTime == null) {
            throw new IllegalArgumentException("时间窗口类型和参考时间不能为空");
        }

        LocalDateTime windowStart = calculateWindowStart(timeWindow, referenceTime);
        LocalDateTime windowEnd = calculateWindowEnd(timeWindow, windowStart);

        return new LocalDateTime[]{windowStart, windowEnd};
    }

    @Override
    public List<LocalDateTime[]> getTimeWindows(TimeWindow timeWindow, LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime.isAfter(endTime)) {
            throw new IllegalArgumentException("开始时间不能晚于结束时间");
        }

        List<LocalDateTime[]> windows = new ArrayList<>();
        LocalDateTime currentStart = calculateWindowStart(timeWindow, startTime);

        while (currentStart.isBefore(endTime)) {
            LocalDateTime currentEnd = calculateWindowEnd(timeWindow, currentStart);

            // 如果窗口开始时间在指定范围内，则添加到结果中
            if (currentStart.isBefore(endTime)) {
                windows.add(new LocalDateTime[]{currentStart, currentEnd});
            }

            currentStart = getNextWindowStart(timeWindow, currentStart);
        }

        return windows;
    }

    @Override
    public Map<String, List<Map<String, Object>>> groupDataByTimeWindow(
            List<Map<String, Object>> dataPoints,
            TimeWindow timeWindow,
            String timeField) {

        if (dataPoints == null || dataPoints.isEmpty()) {
            return Collections.emptyMap();
        }

        return dataPoints.stream()
                .filter(point -> point.containsKey(timeField))
                .collect(Collectors.groupingBy(point -> {
                    Object timeValue = point.get(timeField);
                    LocalDateTime dataTime = convertToLocalDateTime(timeValue);
                    LocalDateTime windowStart = calculateWindowStart(timeWindow, dataTime);
                    return formatWindowKey(timeWindow, windowStart);
                }));
    }

    @Override
    public boolean isTimeInWindow(LocalDateTime time, LocalDateTime windowStart, LocalDateTime windowEnd) {
        return !time.isBefore(windowStart) && time.isBefore(windowEnd);
    }

    @Override
    public LocalDateTime getNextWindowStart(TimeWindow timeWindow, LocalDateTime currentTime) {
        return switch (timeWindow) {
            case MIN_5 -> currentTime.plusMinutes(5);
            case MIN_15 -> currentTime.plusMinutes(15);
            case HOUR_1 -> currentTime.plusHours(1);
            case DAY_1 -> currentTime.plusDays(1);
        };
    }

    @Override
    public LocalDateTime getPreviousWindowStart(TimeWindow timeWindow, LocalDateTime currentTime) {
        return switch (timeWindow) {
            case MIN_5 -> currentTime.minusMinutes(5);
            case MIN_15 -> currentTime.minusMinutes(15);
            case HOUR_1 -> currentTime.minusHours(1);
            case DAY_1 -> currentTime.minusDays(1);
        };
    }

    @Override
    public long getWindowDurationMinutes(TimeWindow timeWindow) {
        return switch (timeWindow) {
            case MIN_5 -> 5;
            case MIN_15 -> 15;
            case HOUR_1 -> 60;
            case DAY_1 -> 1440;
        };
    }

    @Override
    public String formatWindowKey(TimeWindow timeWindow, LocalDateTime windowStart) {
        return timeWindow.name() + "_" + windowStart.format(WINDOW_KEY_FORMATTER);
    }

    @Override
    public LocalDateTime parseWindowKey(String windowKey) {
        try {
            String[] parts = windowKey.split("_", 2);
            if (parts.length != 2) {
                throw new IllegalArgumentException("无效的窗口键格式: " + windowKey);
            }

            return LocalDateTime.parse(parts[1], WINDOW_KEY_FORMATTER);
        } catch (Exception e) {
            log.error("解析窗口键失败: {}", windowKey, e);
            throw new IllegalArgumentException("无法解析窗口键: " + windowKey, e);
        }
    }

    @Override
    public Map<String, Object> getWindowStatistics(TimeWindow timeWindow, LocalDateTime startTime, LocalDateTime endTime) {
        List<LocalDateTime[]> windows = getTimeWindows(timeWindow, startTime, endTime);

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("timeWindow", timeWindow.name());
        statistics.put("startTime", startTime);
        statistics.put("endTime", endTime);
        statistics.put("windowCount", windows.size());
        statistics.put("windowDurationMinutes", getWindowDurationMinutes(timeWindow));
        statistics.put("totalDurationMinutes", ChronoUnit.MINUTES.between(startTime, endTime));

        return statistics;
    }

    /**
     * 计算窗口开始时间
     */
    private LocalDateTime calculateWindowStart(TimeWindow timeWindow, LocalDateTime referenceTime) {
        return switch (timeWindow) {
            case MIN_5 -> referenceTime.withSecond(0).withNano(0)
                    .withMinute((referenceTime.getMinute() / 5) * 5);
            case MIN_15 -> referenceTime.withSecond(0).withNano(0)
                    .withMinute((referenceTime.getMinute() / 15) * 15);
            case HOUR_1 -> referenceTime.withMinute(0).withSecond(0).withNano(0);
            case DAY_1 -> referenceTime.withHour(0).withMinute(0).withSecond(0).withNano(0);
        };
    }

    /**
     * 计算窗口结束时间
     */
    private LocalDateTime calculateWindowEnd(TimeWindow timeWindow, LocalDateTime windowStart) {
        return switch (timeWindow) {
            case MIN_5 -> windowStart.plusMinutes(5);
            case MIN_15 -> windowStart.plusMinutes(15);
            case HOUR_1 -> windowStart.plusHours(1);
            case DAY_1 -> windowStart.plusDays(1);
        };
    }

    /**
     * 转换为LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Object timeValue) {
        if (timeValue instanceof LocalDateTime) {
            return (LocalDateTime) timeValue;
        } else if (timeValue instanceof Long) {
            return LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli((Long) timeValue),
                    java.time.ZoneId.systemDefault()
            );
        } else if (timeValue instanceof Date) {
            return LocalDateTime.ofInstant(
                    ((Date) timeValue).toInstant(),
                    java.time.ZoneId.systemDefault()
            );
        } else if (timeValue instanceof String) {
            try {
                // 尝试解析时间戳
                long timestamp = Long.parseLong((String) timeValue);
                return LocalDateTime.ofInstant(
                        java.time.Instant.ofEpochMilli(timestamp),
                        java.time.ZoneId.systemDefault()
                );
            } catch (NumberFormatException e) {
                // 尝试解析ISO格式
                return LocalDateTime.parse((String) timeValue);
            }
        } else {
            throw new IllegalArgumentException("不支持的时间类型: " + timeValue.getClass());
        }
    }
}
