package com.iot.platform.compute.service.impl;

import com.iot.platform.common.enums.TimeWindow;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 时间窗口服务测试类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("时间窗口服务测试")
class TimeWindowServiceImplTest {

    private TimeWindowServiceImpl timeWindowService;

    @BeforeEach
    void setUp() {
        timeWindowService = new TimeWindowServiceImpl();
    }

    @Test
    @DisplayName("计算1分钟时间窗口边界")
    void testCalculateWindowBounds_Minute1() {
        LocalDateTime referenceTime = LocalDateTime.of(2024, 12, 16, 14, 35, 45);

        LocalDateTime[] bounds = timeWindowService.calculateWindowBounds(TimeWindow.MIN_5, referenceTime);

        assertNotNull(bounds);
        assertEquals(2, bounds.length);

        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 16, 14, 35, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2024, 12, 16, 14, 36, 0);

        assertEquals(expectedStart, bounds[0]);
        assertEquals(expectedEnd, bounds[1]);
    }

    @Test
    @DisplayName("计算5分钟时间窗口边界")
    void testCalculateWindowBounds_Minute5() {
        LocalDateTime referenceTime = LocalDateTime.of(2024, 12, 16, 14, 37, 30);

        LocalDateTime[] bounds = timeWindowService.calculateWindowBounds(TimeWindow.MIN_5, referenceTime);

        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 16, 14, 35, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2024, 12, 16, 14, 40, 0);

        assertEquals(expectedStart, bounds[0]);
        assertEquals(expectedEnd, bounds[1]);
    }

    @Test
    @DisplayName("计算1小时时间窗口边界")
    void testCalculateWindowBounds_Hour1() {
        LocalDateTime referenceTime = LocalDateTime.of(2024, 12, 16, 14, 35, 45);

        LocalDateTime[] bounds = timeWindowService.calculateWindowBounds(TimeWindow.HOUR_1, referenceTime);

        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 16, 14, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2024, 12, 16, 15, 0, 0);

        assertEquals(expectedStart, bounds[0]);
        assertEquals(expectedEnd, bounds[1]);
    }

    @Test
    @DisplayName("计算1天时间窗口边界")
    void testCalculateWindowBounds_Day1() {
        LocalDateTime referenceTime = LocalDateTime.of(2024, 12, 16, 14, 35, 45);

        LocalDateTime[] bounds = timeWindowService.calculateWindowBounds(TimeWindow.DAY_1, referenceTime);

        LocalDateTime expectedStart = LocalDateTime.of(2024, 12, 16, 0, 0, 0);
        LocalDateTime expectedEnd = LocalDateTime.of(2024, 12, 17, 0, 0, 0);

        assertEquals(expectedStart, bounds[0]);
        assertEquals(expectedEnd, bounds[1]);
    }

    @Test
    @DisplayName("计算时间窗口边界 - 异常情况")
    void testCalculateWindowBounds_InvalidInput() {
        LocalDateTime referenceTime = LocalDateTime.of(2024, 12, 16, 14, 35, 45);

        assertThrows(IllegalArgumentException.class, () ->
            timeWindowService.calculateWindowBounds(null, referenceTime));

        assertThrows(IllegalArgumentException.class, () ->
            timeWindowService.calculateWindowBounds(TimeWindow.MIN_5, null));
    }

    @Test
    @DisplayName("获取时间范围内的时间窗口列表")
    void testGetTimeWindows() {
        LocalDateTime startTime = LocalDateTime.of(2024, 12, 16, 14, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 12, 16, 14, 10, 0);

        List<LocalDateTime[]> windows = timeWindowService.getTimeWindows(TimeWindow.MIN_5, startTime, endTime);

        assertNotNull(windows);
        assertEquals(2, windows.size());

        // 第一个窗口: 14:00-14:05
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 0, 0), windows.get(0)[0]);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 5, 0), windows.get(0)[1]);

        // 第二个窗口: 14:05-14:10
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 5, 0), windows.get(1)[0]);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 10, 0), windows.get(1)[1]);
    }

    @Test
    @DisplayName("获取时间窗口列表 - 异常情况")
    void testGetTimeWindows_InvalidInput() {
        LocalDateTime startTime = LocalDateTime.of(2024, 12, 16, 14, 10, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 12, 16, 14, 0, 0);

        assertThrows(IllegalArgumentException.class, () ->
            timeWindowService.getTimeWindows(TimeWindow.MIN_5, startTime, endTime));
    }

    @Test
    @DisplayName("按时间窗口分组数据")
    void testGroupDataByTimeWindow() {
        List<Map<String, Object>> dataPoints = List.of(
            createDataPoint(LocalDateTime.of(2024, 12, 16, 14, 1, 0), 10.0),
            createDataPoint(LocalDateTime.of(2024, 12, 16, 14, 3, 0), 20.0),
            createDataPoint(LocalDateTime.of(2024, 12, 16, 14, 6, 0), 30.0),
            createDataPoint(LocalDateTime.of(2024, 12, 16, 14, 8, 0), 40.0)
        );

        Map<String, List<Map<String, Object>>> groupedData =
            timeWindowService.groupDataByTimeWindow(dataPoints, TimeWindow.MIN_5, "timestamp");

        assertNotNull(groupedData);
        assertEquals(2, groupedData.size());

        // 检查分组结果
        boolean hasFirstWindow = false;
        boolean hasSecondWindow = false;

        for (Map.Entry<String, List<Map<String, Object>>> entry : groupedData.entrySet()) {
            String windowKey = entry.getKey();
            List<Map<String, Object>> windowData = entry.getValue();

            if (windowKey.contains("20241216140000")) { // 14:00-14:05窗口
                hasFirstWindow = true;
                assertEquals(2, windowData.size()); // 14:01和14:03的数据
            } else if (windowKey.contains("20241216140500")) { // 14:05-14:10窗口
                hasSecondWindow = true;
                assertEquals(2, windowData.size()); // 14:06和14:08的数据
            }
        }

        assertTrue(hasFirstWindow);
        assertTrue(hasSecondWindow);
    }

    @Test
    @DisplayName("检查时间是否在窗口内")
    void testIsTimeInWindow() {
        LocalDateTime windowStart = LocalDateTime.of(2024, 12, 16, 14, 0, 0);
        LocalDateTime windowEnd = LocalDateTime.of(2024, 12, 16, 14, 5, 0);

        // 在窗口内的时间
        LocalDateTime timeInWindow = LocalDateTime.of(2024, 12, 16, 14, 2, 30);
        assertTrue(timeWindowService.isTimeInWindow(timeInWindow, windowStart, windowEnd));

        // 窗口开始时间（包含）
        assertTrue(timeWindowService.isTimeInWindow(windowStart, windowStart, windowEnd));

        // 窗口结束时间（不包含）
        assertFalse(timeWindowService.isTimeInWindow(windowEnd, windowStart, windowEnd));

        // 在窗口外的时间
        LocalDateTime timeBeforeWindow = LocalDateTime.of(2024, 12, 16, 13, 59, 0);
        LocalDateTime timeAfterWindow = LocalDateTime.of(2024, 12, 16, 14, 6, 0);

        assertFalse(timeWindowService.isTimeInWindow(timeBeforeWindow, windowStart, windowEnd));
        assertFalse(timeWindowService.isTimeInWindow(timeAfterWindow, windowStart, windowEnd));
    }

    @Test
    @DisplayName("获取下一个时间窗口开始时间")
    void testGetNextWindowStart() {
        LocalDateTime currentTime = LocalDateTime.of(2024, 12, 16, 14, 35, 0);

        // 5分钟窗口
        LocalDateTime nextFiveMinutes = timeWindowService.getNextWindowStart(TimeWindow.MIN_5, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 40, 0), nextFiveMinutes);

        // 15分钟窗口
        LocalDateTime nextFifteenMinutes = timeWindowService.getNextWindowStart(TimeWindow.MIN_15, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 45, 0), nextFifteenMinutes);

        // 1小时窗口
        LocalDateTime nextHour = timeWindowService.getNextWindowStart(TimeWindow.HOUR_1, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 16, 15, 0, 0), nextHour);

        // 1天窗口
        LocalDateTime nextDay = timeWindowService.getNextWindowStart(TimeWindow.DAY_1, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 17, 0, 0, 0), nextDay);
    }

    @Test
    @DisplayName("获取上一个时间窗口开始时间")
    void testGetPreviousWindowStart() {
        LocalDateTime currentTime = LocalDateTime.of(2024, 12, 16, 14, 35, 0);

        // 5分钟窗口
        LocalDateTime prevFiveMinutes = timeWindowService.getPreviousWindowStart(TimeWindow.MIN_5, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 30, 0), prevFiveMinutes);

        // 15分钟窗口
        LocalDateTime prevFifteenMinutes = timeWindowService.getPreviousWindowStart(TimeWindow.MIN_15, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 30, 0), prevFifteenMinutes);

        // 1小时窗口
        LocalDateTime prevHour = timeWindowService.getPreviousWindowStart(TimeWindow.HOUR_1, currentTime);
        assertEquals(LocalDateTime.of(2024, 12, 16, 14, 0, 0), prevHour);
    }

    @Test
    @DisplayName("获取时间窗口持续时间")
    void testGetWindowDurationMinutes() {
        assertEquals(5, timeWindowService.getWindowDurationMinutes(TimeWindow.MIN_5));
        assertEquals(15, timeWindowService.getWindowDurationMinutes(TimeWindow.MIN_15));
        assertEquals(60, timeWindowService.getWindowDurationMinutes(TimeWindow.HOUR_1));
        assertEquals(1440, timeWindowService.getWindowDurationMinutes(TimeWindow.DAY_1));
    }

    @Test
    @DisplayName("格式化和解析时间窗口标识")
    void testFormatAndParseWindowKey() {
        LocalDateTime windowStart = LocalDateTime.of(2024, 12, 16, 14, 35, 0);

        String windowKey = timeWindowService.formatWindowKey(TimeWindow.MIN_5, windowStart);
        assertNotNull(windowKey);
        assertTrue(windowKey.startsWith("MINUTE_5_"));
        assertTrue(windowKey.contains("20241216143500"));

        LocalDateTime parsedTime = timeWindowService.parseWindowKey(windowKey);
        assertEquals(windowStart, parsedTime);
    }

    @Test
    @DisplayName("解析时间窗口标识 - 异常情况")
    void testParseWindowKey_InvalidInput() {
        assertThrows(IllegalArgumentException.class, () ->
            timeWindowService.parseWindowKey("invalid_key"));

        assertThrows(IllegalArgumentException.class, () ->
            timeWindowService.parseWindowKey("MINUTE_5"));

        assertThrows(IllegalArgumentException.class, () ->
            timeWindowService.parseWindowKey("MINUTE_5_invalid_date"));
    }

    @Test
    @DisplayName("获取时间窗口统计信息")
    void testGetWindowStatistics() {
        LocalDateTime startTime = LocalDateTime.of(2024, 12, 16, 14, 0, 0);
        LocalDateTime endTime = LocalDateTime.of(2024, 12, 16, 15, 0, 0);

        Map<String, Object> statistics = timeWindowService.getWindowStatistics(
            TimeWindow.MIN_5, startTime, endTime);

        assertNotNull(statistics);
        assertEquals("MINUTE_5", statistics.get("timeWindow"));
        assertEquals(startTime, statistics.get("startTime"));
        assertEquals(endTime, statistics.get("endTime"));
        assertEquals(12, statistics.get("windowCount")); // 60分钟 / 5分钟 = 12个窗口
        assertEquals(5L, statistics.get("windowDurationMinutes"));
        assertEquals(60L, statistics.get("totalDurationMinutes"));
    }

    /**
     * 创建测试数据点
     */
    private Map<String, Object> createDataPoint(LocalDateTime timestamp, Double value) {
        Map<String, Object> dataPoint = new HashMap<>();
        dataPoint.put("deviceCode", "TEST_DEVICE");
        dataPoint.put("dataValue", value);
        dataPoint.put("timestamp", timestamp);
        return dataPoint;
    }
}
