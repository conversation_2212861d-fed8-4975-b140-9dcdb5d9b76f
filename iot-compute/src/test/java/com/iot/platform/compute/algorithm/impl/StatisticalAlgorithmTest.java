package com.iot.platform.compute.algorithm.impl;

import com.iot.platform.common.enums.CalcType;
import com.iot.platform.common.enums.TimeWindow;
import com.iot.platform.compute.model.ComputeResult;
import com.iot.platform.compute.model.ComputeTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 统计算法测试类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("统计算法测试")
class StatisticalAlgorithmTest {

    private StatisticalAlgorithm algorithm;

    @BeforeEach
    void setUp() {
        algorithm = new StatisticalAlgorithm();
        algorithm.configure(Map.of("precision", 2, "ignoreNullValues", true));
    }

    @Test
    @DisplayName("获取算法基本信息")
    void testGetAlgorithmInfo() {
        assertEquals("STATISTICAL", algorithm.getAlgorithmType());
        assertEquals("基础统计算法", algorithm.getAlgorithmName());
        assertNotNull(algorithm.getDescription());
    }

    @Test
    @DisplayName("算法支持性检查 - 支持的计算类型")
    void testSupports_SupportedTypes() {
        ComputeTask avgTask = createComputeTask(CalcType.AVG);
        ComputeTask maxTask = createComputeTask(CalcType.MAX);
        ComputeTask minTask = createComputeTask(CalcType.MIN);
        ComputeTask sumTask = createComputeTask(CalcType.SUM);
        ComputeTask countTask = createComputeTask(CalcType.COUNT);

        assertTrue(algorithm.supports(avgTask));
        assertTrue(algorithm.supports(maxTask));
        assertTrue(algorithm.supports(minTask));
        assertTrue(algorithm.supports(sumTask));
        assertTrue(algorithm.supports(countTask));
    }

    @Test
    @DisplayName("算法支持性检查 - 不支持的情况")
    void testSupports_UnsupportedCases() {
        assertFalse(algorithm.supports(null));

        ComputeTask taskWithNullCalcType = createComputeTask(null);
        assertFalse(algorithm.supports(taskWithNullCalcType));
    }

    @Test
    @DisplayName("平均值计算 - 正常情况")
    void testCompute_Average_Normal() {
        ComputeTask task = createComputeTask(CalcType.AVG);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 20.0, 30.0, 40.0, 50.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(30.0, ((Number) result.getResult()).doubleValue(), 0.001);
        assertEquals(5, result.getDataCount());
        assertNull(result.getErrorMessage());
    }

    @Test
    @DisplayName("最大值计算 - 正常情况")
    void testCompute_Maximum_Normal() {
        ComputeTask task = createComputeTask(CalcType.MAX);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 50.0, 30.0, 20.0, 40.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(50.0, ((Number) result.getResult()).doubleValue(), 0.001);
        assertEquals(5, result.getDataCount());
    }

    @Test
    @DisplayName("最小值计算 - 正常情况")
    void testCompute_Minimum_Normal() {
        ComputeTask task = createComputeTask(CalcType.MIN);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 50.0, 5.0, 20.0, 40.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(5.0, ((Number) result.getResult()).doubleValue(), 0.001);
        assertEquals(5, result.getDataCount());
    }

    @Test
    @DisplayName("求和计算 - 正常情况")
    void testCompute_Sum_Normal() {
        ComputeTask task = createComputeTask(CalcType.SUM);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 20.0, 30.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(60.0, ((Number) result.getResult()).doubleValue(), 0.001);
        assertEquals(3, result.getDataCount());
    }

    @Test
    @DisplayName("计数统计 - 正常情况")
    void testCompute_Count_Normal() {
        ComputeTask task = createComputeTask(CalcType.COUNT);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 20.0, 30.0, 40.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(4.0, ((Number) result.getResult()).doubleValue(), 0.001);
        assertEquals(4, result.getDataCount());
    }

    @Test
    @DisplayName("空数据处理")
    void testCompute_EmptyData() {
        ComputeTask task = createComputeTask(CalcType.AVG);
        List<Map<String, Object>> emptyDataPoints = Collections.emptyList();

        ComputeResult result = algorithm.compute(task, emptyDataPoints);

        assertNotNull(result);
        assertEquals((byte) 0, result.getStatus());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("输入数据验证失败"));
    }

    @Test
    @DisplayName("包含空值的数据处理")
    void testCompute_WithNullValues() {
        ComputeTask task = createComputeTask(CalcType.AVG);
        List<Map<String, Object>> dataPoints = new ArrayList<>();

        // 添加正常数据
        dataPoints.add(createDataPoint(10.0));
        dataPoints.add(createDataPoint(20.0));

        // 添加空值数据
        Map<String, Object> nullDataPoint = new HashMap<>();
        nullDataPoint.put("deviceCode", "TEST_DEVICE");
        nullDataPoint.put("dataValue", null);
        dataPoints.add(nullDataPoint);

        dataPoints.add(createDataPoint(30.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(20.0, ((Number) result.getResult()).doubleValue(), 0.001); // (10+20+30)/3 = 20
        assertEquals(3, result.getDataCount()); // 空值被忽略
    }

    @Test
    @DisplayName("包含非数值数据处理")
    void testCompute_WithNonNumericValues() {
        ComputeTask task = createComputeTask(CalcType.AVG);
        List<Map<String, Object>> dataPoints = new ArrayList<>();

        dataPoints.add(createDataPoint(10.0));
        dataPoints.add(createDataPoint("invalid_number"));
        dataPoints.add(createDataPoint(20.0));

        ComputeResult result = algorithm.compute(task, dataPoints);

        assertNotNull(result);
        assertEquals((byte) 1, result.getStatus());
        assertEquals(15.0, ((Number) result.getResult()).doubleValue(), 0.001); // (10+20)/2 = 15
        assertEquals(2, result.getDataCount()); // 无效数据被忽略
    }

    @Test
    @DisplayName("数据验证 - 有效数据")
    void testValidateInput_ValidData() {
        List<Map<String, Object>> validData = createTestDataPoints(Arrays.asList(10.0, 20.0, 30.0));
        assertTrue(algorithm.validateInput(validData));
    }

    @Test
    @DisplayName("数据验证 - 无效数据")
    void testValidateInput_InvalidData() {
        // 空数据
        assertFalse(algorithm.validateInput(null));
        assertFalse(algorithm.validateInput(Collections.emptyList()));

        // 缺少dataValue字段的数据
        List<Map<String, Object>> invalidData = new ArrayList<>();
        Map<String, Object> invalidPoint = new HashMap<>();
        invalidPoint.put("deviceCode", "TEST_DEVICE");
        // 缺少dataValue字段
        invalidData.add(invalidPoint);

        assertFalse(algorithm.validateInput(invalidData));
    }

    @Test
    @DisplayName("算法配置管理")
    void testConfigure() {
        Map<String, Object> config = new HashMap<>();
        config.put("precision", 3);
        config.put("ignoreNullValues", false);
        config.put("customParam", "test");

        assertTrue(algorithm.configure(config));

        Map<String, Object> configParams = algorithm.getConfigParameters();
        assertNotNull(configParams);
        assertTrue(configParams.containsKey("precision"));
        assertTrue(configParams.containsKey("ignoreNullValues"));
    }

    @Test
    @DisplayName("性能指标获取")
    void testGetPerformanceMetrics() {
        // 执行一些计算以生成性能数据
        ComputeTask task = createComputeTask(CalcType.AVG);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 20.0, 30.0));

        algorithm.compute(task, dataPoints);
        algorithm.compute(task, dataPoints);

        Map<String, Object> metrics = algorithm.getPerformanceMetrics();

        assertNotNull(metrics);
        assertTrue(metrics.containsKey("algorithmType"));
        assertTrue(metrics.containsKey("computeCount"));
        assertTrue(metrics.containsKey("errorCount"));
        assertTrue(metrics.containsKey("totalExecutionTime"));
        assertTrue(metrics.containsKey("averageExecutionTime"));
        assertTrue(metrics.containsKey("successRate"));

        assertEquals("STATISTICAL", metrics.get("algorithmType"));
        assertTrue((Long) metrics.get("computeCount") >= 2);
    }

    @Test
    @DisplayName("算法状态重置")
    void testReset() {
        // 执行一些计算
        ComputeTask task = createComputeTask(CalcType.AVG);
        List<Map<String, Object>> dataPoints = createTestDataPoints(Arrays.asList(10.0, 20.0, 30.0));
        algorithm.compute(task, dataPoints);

        // 重置前检查指标
        Map<String, Object> metricsBeforeReset = algorithm.getPerformanceMetrics();
        assertTrue((Long) metricsBeforeReset.get("computeCount") > 0);

        // 重置算法
        algorithm.reset();

        // 重置后检查指标
        Map<String, Object> metricsAfterReset = algorithm.getPerformanceMetrics();
        assertEquals(0L, metricsAfterReset.get("computeCount"));
        assertEquals(0L, metricsAfterReset.get("errorCount"));
        assertEquals(0L, metricsAfterReset.get("totalExecutionTime"));
    }

    /**
     * 创建测试用的计算任务
     */
    private ComputeTask createComputeTask(CalcType calcType) {
        ComputeTask task = new ComputeTask();
        task.setTaskId(1L);
        task.setProjectId("test_project");
        task.setDeviceCode("TEST_DEVICE_001");
        task.setDataCode((byte) 1);
        task.setCalcType(calcType);
        task.setTimeWindow(TimeWindow.MIN_5);
        task.setTaskName("测试计算任务");
        task.setDescription("用于单元测试的计算任务");
        return task;
    }

    /**
     * 创建测试数据点列表
     */
    private List<Map<String, Object>> createTestDataPoints(List<Double> values) {
        List<Map<String, Object>> dataPoints = new ArrayList<>();
        for (Double value : values) {
            dataPoints.add(createDataPoint(value));
        }
        return dataPoints;
    }

    /**
     * 创建单个数据点
     */
    private Map<String, Object> createDataPoint(Object value) {
        Map<String, Object> dataPoint = new HashMap<>();
        dataPoint.put("deviceCode", "TEST_DEVICE_001");
        dataPoint.put("dataCode", (byte) 1);
        dataPoint.put("dataValue", value);
        dataPoint.put("timestamp", System.currentTimeMillis());
        return dataPoint;
    }
}
