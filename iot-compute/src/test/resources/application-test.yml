# IoT计算引擎模块 - 测试环境配置

spring:
  profiles:
    active: test
  
  # 数据源配置 - 使用H2内存数据库
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:compute_testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
    username: sa
    password: 
    hikari:
      maximum-pool-size: 5
      minimum-idle: 1
      connection-timeout: 10000
      idle-timeout: 300000
      max-lifetime: 900000
  
  # H2数据库控制台
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # Redis配置 - 使用嵌入式Redis
  data:
    redis:
      host: localhost
      port: 6379
      database: 15
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 5
          max-idle: 3
          min-idle: 1
          max-wait: 3000ms

# 日志配置
logging:
  level:
    com.iot.platform.compute: DEBUG
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# IoT计算引擎配置
iot:
  compute:
    # 实时计算配置
    realtime-enabled: true
    realtime-interval: 60  # 秒
    
    # 批量计算配置
    batch-enabled: true
    batch-size: 100
    batch-interval: 300  # 秒
    
    # 线程池配置
    thread-pool:
      core-size: 2
      max-size: 5
      queue-capacity: 50
      keep-alive-seconds: 60
      thread-name-prefix: Test-Compute-
    
    # 缓存配置
    cache:
      enabled: true
      expire-seconds: 1800  # 30分钟
      max-size: 1000
    
    # 数据源配置
    data-source:
      query-timeout-seconds: 10
      max-query-records: 1000
      page-size: 100
      compression-enabled: false
    
    # 监控配置
    monitor:
      enabled: true
      collect-interval: 30  # 秒
      retention-days: 1
      alert-enabled: false  # 测试环境关闭告警
      cpu-threshold: 90
      memory-threshold: 90
      execution-time-threshold: 10000  # 10秒

# 测试专用配置
test:
  # 数据库初始化
  database:
    init-schema: true
    init-data: true
  
  # Mock配置
  mock:
    enabled: true
    delay: 0
  
  # 性能测试配置
  performance:
    concurrent-tasks: 5
    test-duration: 30
    data-points-per-task: 10
