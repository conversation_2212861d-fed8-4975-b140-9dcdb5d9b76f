package com.iot.platform.edge.enums;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 业务类型
 */
@ApiModel(description = "设备业务类型")
public enum DeviceType {

    /**
     * 人流
     */
    @ApiModelProperty(value = "人流")
    PEOPLE_FLOW("PeopleFlow"),

    /**
     * 占用
     */
    @ApiModelProperty(value = "占用")
    OCCUPY("Occupy"),

    /**
     * 异味
     */
    @ApiModelProperty(value = "异味")
    ODOR("Odor"),

    /**
     * 温湿度传感器
     */
    @ApiModelProperty(value = "温湿度传感器")
    TEMPERATURE_AND_HUMIDITY("TemperatureAndHumidity"),

    /**
     * 环境
     */
    @ApiModelProperty(value = "环境")
    AMBIENCE_MONITORING("AmbienceMonitoring"),

    /**
     * 灯控开关面板
     */
    @ApiModelProperty(value = "灯控开关面板")
    LAMP_SWITCH_PANEL("LampSwitchPanel"),

    /**
     * 空间人数统计
     */
    @ApiModelProperty(value = "空间人数统计")
    PEOPLE_REGION_COUNT("PeopleRegionCount"),

    /**
     * 未知
     */
    @ApiModelProperty(value = "未知")
    UNKNOWN("unknown");

    private final String value;

    // 私有构造函数
    DeviceType(String value) {
        this.value = value;
    }

    // Getter 方法
    public String getValue() {
        return value;
    }
}
