package com.iot.platform.edge.adapter.impl;

import com.iot.platform.edge.adapter.DeviceAdapter;
import com.iot.platform.edge.model.DeviceCommand;
import com.iot.platform.edge.model.DeviceData;
import com.iot.platform.common.enums.DeviceType;
import com.iot.platform.common.enums.DeviceVendor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 星纵(YEASTAR)设备适配器实现
 * 支持星纵物联网设备的数据采集和控制
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Component
public class YeastarDeviceAdapter implements DeviceAdapter {

    /**
     * 设备连接状态缓存
     */
    private final Map<String, Boolean> connectionStatus = new ConcurrentHashMap<>();

    /**
     * 设备配置缓存
     */
    private final Map<String, Map<String, Object>> deviceConfigs = new ConcurrentHashMap<>();

    /**
     * 设备数据缓存
     */
    private final Map<String, DeviceData> lastDataCache = new ConcurrentHashMap<>();

    @Override
    public boolean initialize(Map<String, Object> config) {
        try {
            log.info("初始化星纵设备适配器，配置: {}", config);

            // 初始化适配器配置
            if (config != null) {
                // 可以从config中读取适配器级别的配置
                Object timeout = config.get("connectionTimeout");
                if (timeout != null) {
                    log.info("设置连接超时时间: {}秒", timeout);
                }

                Object retryCount = config.get("maxRetryCount");
                if (retryCount != null) {
                    log.info("设置最大重试次数: {}", retryCount);
                }
            }

            log.info("星纵设备适配器初始化完成");
            return true;

        } catch (Exception e) {
            log.error("初始化星纵设备适配器失败", e);
            return false;
        }
    }

    @Override
    public String getAdapterType() {
        return "YEASTAR_ADAPTER";
    }

    public DeviceVendor getVendor() {
        return DeviceVendor.YEASTAR;
    }

    @Override
    public List<String> getSupportedVendors() {
        return Arrays.asList("YEASTAR");
    }

    public List<DeviceType> getSupportedDeviceTypes() {
        return Arrays.asList(
            DeviceType.SENSOR,
            DeviceType.GATEWAY,
            DeviceType.CONTROLLER
        );
    }

    @Override
    public boolean isConnected(String deviceCode) {
        return connectionStatus.getOrDefault(deviceCode, false);
    }

    @Override
    public boolean connect(String deviceCode, Map<String, Object> connectionParams) {
        if (deviceCode == null || deviceCode.trim().isEmpty()) {
            log.error("设备编码不能为空");
            return false;
        }

        try {
            // 解析连接参数
            String host = (String) connectionParams.get("host");
            Integer port = (Integer) connectionParams.getOrDefault("port", 502);
            String protocol = (String) connectionParams.getOrDefault("protocol", "modbus");
            Integer timeout = (Integer) connectionParams.getOrDefault("timeout", 5000);

            if (host == null || host.trim().isEmpty()) {
                log.error("星纵设备连接参数缺少主机地址: {}", deviceCode);
                return false;
            }

            // 建立星纵设备连接
            boolean connected = establishYeastarConnection(deviceCode, host, port, protocol, timeout);

            if (connected) {
                connectionStatus.put(deviceCode, true);
                deviceConfigs.put(deviceCode, new HashMap<>(connectionParams));
                log.info("星纵设备连接成功: {} -> {}:{}", deviceCode, host, port);
            } else {
                log.error("星纵设备连接失败: {} -> {}:{}", deviceCode, host, port);
            }

            return connected;

        } catch (Exception e) {
            log.error("星纵设备连接异常: {}", deviceCode, e);
            return false;
        }
    }

    @Override
    public boolean disconnect(String deviceCode) {
        try {
            if (isConnected(deviceCode)) {
                // 断开星纵设备连接
                boolean disconnected = closeYeastarConnection(deviceCode);

                if (disconnected) {
                    connectionStatus.put(deviceCode, false);
                    deviceConfigs.remove(deviceCode);
                    lastDataCache.remove(deviceCode);
                    log.info("星纵设备断开连接成功: {}", deviceCode);
                } else {
                    log.warn("星纵设备断开连接失败: {}", deviceCode);
                }

                return disconnected;
            }
            return true;
        } catch (Exception e) {
            log.error("星纵设备断开连接异常: {}", deviceCode, e);
            return false;
        }
    }

    @Override
    public List<DeviceData> readDeviceData(String deviceCode, List<String> dataPoints) {
        if (!isConnected(deviceCode)) {
            log.warn("星纵设备未连接，无法读取数据: {}", deviceCode);
            return Collections.emptyList();
        }

        List<DeviceData> dataList = new ArrayList<>();

        try {
            // 读取星纵设备数据
            Map<String, Object> rawData = readYeastarDeviceData(deviceCode, dataPoints);

            // 转换为标准格式
            for (Map.Entry<String, Object> entry : rawData.entrySet()) {
                DeviceData deviceData = convertYeastarData(deviceCode, entry.getKey(), entry.getValue());
                if (deviceData != null) {
                    dataList.add(deviceData);
                    lastDataCache.put(deviceCode + "_" + entry.getKey(), deviceData);
                }
            }

            log.debug("星纵设备数据读取成功: device={}, count={}", deviceCode, dataList.size());

        } catch (Exception e) {
            log.error("星纵设备数据读取失败: {}", deviceCode, e);
        }

        return dataList;
    }

    @Override
    public boolean sendCommand(String deviceCode, DeviceCommand command) {
        if (!isConnected(deviceCode)) {
            log.warn("星纵设备未连接，无法发送指令: {}", deviceCode);
            command.markAsFailed("设备未连接");
            return false;
        }

        try {
            command.markAsSent();
            command.markAsExecuting();

            // 转换为星纵设备指令格式
            Map<String, Object> yeastarCommand = convertToYeastarCommand(command);

            // 执行星纵设备指令
            boolean success = executeYeastarCommand(deviceCode, yeastarCommand);

            if (success) {
                Map<String, Object> result = Map.of(
                        "commandId", command.getCommandId(),
                        "executeTime", System.currentTimeMillis(),
                        "status", "success",
                        "vendor", "YEASTAR"
                );
                command.markAsSuccess(result);
                log.info("星纵设备指令执行成功: device={}, command={}", deviceCode, command.getCommandType());
            } else {
                command.markAsFailed("指令执行失败");
                log.error("星纵设备指令执行失败: device={}, command={}", deviceCode, command.getCommandType());
            }

            return success;

        } catch (Exception e) {
            command.markAsFailed("指令执行异常: " + e.getMessage());
            log.error("星纵设备指令执行异常: device={}, command={}", deviceCode, command.getCommandType(), e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getDeviceStatus(String deviceCode) {
        Map<String, Object> status = new HashMap<>();
        status.put("deviceCode", deviceCode);
        status.put("vendor", getVendor().getDescription());
        status.put("connected", isConnected(deviceCode));
        status.put("lastUpdateTime", System.currentTimeMillis());

        if (isConnected(deviceCode)) {
            Map<String, Object> config = deviceConfigs.get(deviceCode);
            if (config != null) {
                status.put("host", config.get("host"));
                status.put("port", config.get("port"));
                status.put("protocol", config.get("protocol"));
            }
        }

        return status;
    }

    @Override
    public Map<String, Object> getDeviceConfig(String deviceCode) {
        return deviceConfigs.getOrDefault(deviceCode, new HashMap<>());
    }

    @Override
    public boolean updateDeviceConfig(String deviceCode, Map<String, Object> config) {
        try {
            if (isConnected(deviceCode)) {
                // 更新星纵设备配置
                boolean updated = updateYeastarDeviceConfig(deviceCode, config);

                if (updated) {
                    deviceConfigs.put(deviceCode, new HashMap<>(config));
                    log.info("星纵设备配置更新成功: {}", deviceCode);
                } else {
                    log.error("星纵设备配置更新失败: {}", deviceCode);
                }

                return updated;
            } else {
                log.warn("星纵设备未连接，无法更新配置: {}", deviceCode);
                return false;
            }
        } catch (Exception e) {
            log.error("星纵设备配置更新异常: {}", deviceCode, e);
            return false;
        }
    }

    /**
     * 建立星纵设备连接
     */
    private boolean establishYeastarConnection(String deviceCode, String host, Integer port, String protocol, Integer timeout) {
        // TODO: 实现具体的星纵设备连接逻辑
        // 这里应该根据星纵设备的实际通信协议进行实现
        log.info("建立星纵设备连接: device={}, host={}, port={}, protocol={}", deviceCode, host, port, protocol);

        // 模拟连接成功
        return true;
    }

    /**
     * 关闭星纵设备连接
     */
    private boolean closeYeastarConnection(String deviceCode) {
        // TODO: 实现具体的星纵设备断开连接逻辑
        log.info("关闭星纵设备连接: {}", deviceCode);

        // 模拟断开成功
        return true;
    }

    /**
     * 读取星纵设备原始数据
     */
    private Map<String, Object> readYeastarDeviceData(String deviceCode, List<String> dataPoints) {
        // TODO: 实现具体的星纵设备数据读取逻辑
        Map<String, Object> data = new HashMap<>();

        // 模拟数据读取
        for (String dataPoint : dataPoints) {
            switch (dataPoint) {
                case "temperature" -> data.put("temperature", 25.5 + Math.random() * 10);
                case "humidity" -> data.put("humidity", 60.0 + Math.random() * 20);
                case "pm25" -> data.put("pm25", 35.0 + Math.random() * 50);
                case "co2" -> data.put("co2", 400.0 + Math.random() * 200);
                default -> log.warn("不支持的数据点: {}", dataPoint);
            }
        }

        return data;
    }

    /**
     * 转换星纵设备数据为标准格式
     */
    private DeviceData convertYeastarData(String deviceCode, String dataPoint, Object value) {
        try {
            DeviceData deviceData = new DeviceData();
            deviceData.setDeviceCode(deviceCode);
            deviceData.setReportTime(System.currentTimeMillis());

            // 根据数据点类型设置数据码和值
            switch (dataPoint) {
                case "temperature" -> {
                    deviceData.setDataCode((byte) 1);
                    deviceData.setDataValue(new BigDecimal(value.toString()));
                }
                case "humidity" -> {
                    deviceData.setDataCode((byte) 2);
                    deviceData.setDataValue(new BigDecimal(value.toString()));
                }
                case "pm25" -> {
                    deviceData.setDataCode((byte) 3);
                    deviceData.setDataValue(new BigDecimal(value.toString()));
                }
                case "co2" -> {
                    deviceData.setDataCode((byte) 4);
                    deviceData.setDataValue(new BigDecimal(value.toString()));
                }
                default -> {
                    log.warn("未知的星纵设备数据点: {}", dataPoint);
                    return null;
                }
            }

            return deviceData;

        } catch (Exception e) {
            log.error("星纵设备数据转换失败: device={}, dataPoint={}, value={}", deviceCode, dataPoint, value, e);
            return null;
        }
    }

    /**
     * 转换为星纵设备指令格式
     */
    private Map<String, Object> convertToYeastarCommand(DeviceCommand command) {
        Map<String, Object> yeastarCommand = new HashMap<>();
        yeastarCommand.put("commandId", command.getCommandId());
        yeastarCommand.put("commandType", command.getCommandType());
        yeastarCommand.put("parameters", command.getParameters());
        yeastarCommand.put("timeout", command.getTimeout());

        // 根据指令类型进行特殊处理
        switch (command.getCommandType()) {
            case "SET_TEMPERATURE" -> {
                // 温度设置指令的特殊处理
                Object temp = command.getParameters().get("temperature");
                yeastarCommand.put("register", "40001");
                yeastarCommand.put("value", temp);
            }
            case "SET_MODE" -> {
                // 模式设置指令的特殊处理
                Object mode = command.getParameters().get("mode");
                yeastarCommand.put("register", "40002");
                yeastarCommand.put("value", mode);
            }
            default -> log.warn("未知的星纵设备指令类型: {}", command.getCommandType());
        }

        return yeastarCommand;
    }

    /**
     * 执行星纵设备指令
     */
    private boolean executeYeastarCommand(String deviceCode, Map<String, Object> command) {
        // TODO: 实现具体的星纵设备指令执行逻辑
        log.info("执行星纵设备指令: device={}, command={}", deviceCode, command);

        // 模拟指令执行成功
        return true;
    }

    /**
     * 更新星纵设备配置
     */
    private boolean updateYeastarDeviceConfig(String deviceCode, Map<String, Object> config) {
        // TODO: 实现具体的星纵设备配置更新逻辑
        log.info("更新星纵设备配置: device={}, config={}", deviceCode, config);

        // 模拟配置更新成功
        return true;
    }



    @Override
    public boolean resetDevice(String deviceCode) {
        try {
            log.info("重置星纵设备: {}", deviceCode);

            // 先断开连接
            disconnect(deviceCode);

            // 清理设备缓存
            lastDataCache.remove(deviceCode);

            // 重新连接
            // 这里需要设备的连接参数，实际项目中应该从配置中获取
            Map<String, Object> config = new HashMap<>();
            boolean reconnected = connect(deviceCode, config);

            if (reconnected) {
                log.info("星纵设备重置成功: {}", deviceCode);
                return true;
            } else {
                log.error("星纵设备重置后重连失败: {}", deviceCode);
                return false;
            }

        } catch (Exception e) {
            log.error("重置星纵设备失败: {}", deviceCode, e);
            return false;
        }
    }

    @Override
    public Map<String, Object> getDiagnosticInfo(String deviceCode) {
        Map<String, Object> diagnostic = new HashMap<>();

        try {
            diagnostic.put("deviceCode", deviceCode);
            diagnostic.put("vendor", "YEASTAR");
            diagnostic.put("adapterName", "YeastarDeviceAdapter");
            diagnostic.put("connected", connectionStatus.getOrDefault(deviceCode, false));
            diagnostic.put("lastDataTime", lastDataCache.containsKey(deviceCode) ?
                    lastDataCache.get(deviceCode).getTimestamp() : null);

            // 模拟设备诊断信息
            diagnostic.put("signalStrength", -45); // dBm
            diagnostic.put("temperature", 35.2); // 设备温度
            diagnostic.put("uptime", System.currentTimeMillis() - 1000 * 60 * 60 * 24); // 运行时间
            diagnostic.put("errorCount", 0);
            diagnostic.put("lastError", null);
            diagnostic.put("diagnosticTime", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("获取星纵设备诊断信息失败: {}", deviceCode, e);
            diagnostic.put("error", "获取诊断信息失败: " + e.getMessage());
        }

        return diagnostic;
    }

    @Override
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("adapterName", "YeastarDeviceAdapter");
        status.put("vendor", "YEASTAR");
        status.put("status", "RUNNING");
        status.put("connectedDevices", connectionStatus.size());
        status.put("activeConnections", connectionStatus.values().stream().mapToInt(connected -> connected ? 1 : 0).sum());
        status.put("lastUpdateTime", System.currentTimeMillis());

        return status;
    }

    @Override
    public void destroy() {
        try {
            log.info("销毁星纵设备适配器");

            // 断开所有连接
            for (String deviceCode : connectionStatus.keySet()) {
                disconnect(deviceCode);
            }

            // 清理缓存
            connectionStatus.clear();
            lastDataCache.clear();

            log.info("星纵设备适配器销毁完成");
        } catch (Exception e) {
            log.error("销毁星纵设备适配器失败", e);
        }
    }
}
