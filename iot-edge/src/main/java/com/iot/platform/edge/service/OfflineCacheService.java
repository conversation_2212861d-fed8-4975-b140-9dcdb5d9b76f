package com.iot.platform.edge.service;

import com.iot.platform.edge.model.DeviceData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 断网缓存服务
 * 负责在网络断开时缓存设备数据，网络恢复后自动上传
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class OfflineCacheService {

    @Value("${iot.edge.cache.directory:./cache}")
    private String cacheDirectory;

    @Value("${iot.edge.cache.max-size:10000}")
    private int maxCacheSize;

    @Value("${iot.edge.cache.max-file-size:100}")
    private int maxFileSize; // MB

    @Value("${iot.edge.cache.retention-days:7}")
    private int retentionDays;

    /**
     * 内存缓存队列
     */
    private final Queue<DeviceData> memoryCache = new ConcurrentLinkedQueue<>();

    /**
     * 读写锁
     */
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    /**
     * 定时任务执行器
     */
    private ScheduledExecutorService scheduler;

    /**
     * 当前缓存文件
     */
    private File currentCacheFile;

    /**
     * 缓存文件写入器
     */
    private BufferedWriter cacheWriter;

    /**
     * 缓存统计信息
     */
    private volatile long totalCachedCount = 0;
    private volatile long totalUploadedCount = 0;
    private volatile long totalFailedCount = 0;

    @PostConstruct
    public void init() {
        try {
            // 创建缓存目录
            Path cachePath = Paths.get(cacheDirectory);
            if (!Files.exists(cachePath)) {
                Files.createDirectories(cachePath);
                log.info("创建缓存目录: {}", cacheDirectory);
            }

            // 初始化当前缓存文件
            initCurrentCacheFile();

            // 启动定时任务
            scheduler = Executors.newScheduledThreadPool(2);

            // 定时持久化内存缓存
            scheduler.scheduleWithFixedDelay(this::persistMemoryCache, 30, 30, TimeUnit.SECONDS);

            // 定时清理过期缓存文件
            scheduler.scheduleWithFixedDelay(this::cleanExpiredCacheFiles, 1, 24, TimeUnit.HOURS);

            log.info("断网缓存服务初始化完成: directory={}, maxSize={}, retentionDays={}",
                    cacheDirectory, maxCacheSize, retentionDays);

        } catch (Exception e) {
            log.error("断网缓存服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            // 持久化剩余的内存缓存
            persistMemoryCache();

            // 关闭缓存文件写入器
            if (cacheWriter != null) {
                cacheWriter.close();
            }

            // 关闭定时任务
            if (scheduler != null) {
                scheduler.shutdown();
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            }

            log.info("断网缓存服务已关闭");
        } catch (Exception e) {
            log.error("断网缓存服务关闭异常", e);
        }
    }

    /**
     * 缓存设备数据
     */
    public void cacheDeviceData(DeviceData deviceData) {
        if (deviceData == null) {
            return;
        }

        lock.writeLock().lock();
        try {
            // 检查缓存大小限制
            if (memoryCache.size() >= maxCacheSize) {
                // 移除最旧的数据
                DeviceData removed = memoryCache.poll();
                if (removed != null) {
                    log.warn("缓存已满，移除最旧数据: device={}, time={}",
                            removed.getDeviceCode(), removed.getReportTime());
                }
            }

            // 添加到内存缓存
            memoryCache.offer(deviceData);
            totalCachedCount++;

            log.debug("设备数据已缓存: device={}, dataCode={}, value={}",
                    deviceData.getDeviceCode(), deviceData.getDataCode(), deviceData.getDataValue());

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 批量缓存设备数据
     */
    public void cacheDeviceDataBatch(List<DeviceData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }

        lock.writeLock().lock();
        try {
            for (DeviceData deviceData : dataList) {
                // 检查缓存大小限制
                if (memoryCache.size() >= maxCacheSize) {
                    DeviceData removed = memoryCache.poll();
                    if (removed != null) {
                        log.warn("缓存已满，移除最旧数据: device={}", removed.getDeviceCode());
                    }
                }

                memoryCache.offer(deviceData);
                totalCachedCount++;
            }

            log.info("批量缓存设备数据: count={}", dataList.size());

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取缓存的设备数据
     */
    public List<DeviceData> getCachedData(int maxCount) {
        List<DeviceData> result = new ArrayList<>();

        lock.readLock().lock();
        try {
            // 从内存缓存获取
            int count = 0;
            Iterator<DeviceData> iterator = memoryCache.iterator();
            while (iterator.hasNext() && count < maxCount) {
                result.add(iterator.next());
                count++;
            }

            // 如果内存缓存不够，从文件缓存获取
            if (count < maxCount) {
                List<DeviceData> fileData = loadCachedDataFromFiles(maxCount - count);
                result.addAll(fileData);
            }

        } finally {
            lock.readLock().unlock();
        }

        return result;
    }

    /**
     * 移除已上传的缓存数据
     */
    public void removeCachedData(List<DeviceData> uploadedData) {
        if (uploadedData == null || uploadedData.isEmpty()) {
            return;
        }

        lock.writeLock().lock();
        try {
            Set<String> uploadedKeys = new HashSet<>();
            for (DeviceData data : uploadedData) {
                uploadedKeys.add(generateDataKey(data));
            }

            // 从内存缓存中移除
            memoryCache.removeIf(data -> uploadedKeys.contains(generateDataKey(data)));

            totalUploadedCount += uploadedData.size();

            log.info("移除已上传的缓存数据: count={}", uploadedData.size());

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new HashMap<>();

        lock.readLock().lock();
        try {
            stats.put("memoryCacheSize", memoryCache.size());
            stats.put("totalCachedCount", totalCachedCount);
            stats.put("totalUploadedCount", totalUploadedCount);
            stats.put("totalFailedCount", totalFailedCount);
            stats.put("cacheDirectory", cacheDirectory);
            stats.put("maxCacheSize", maxCacheSize);

            // 计算文件缓存大小
            long fileCacheSize = calculateFileCacheSize();
            stats.put("fileCacheSize", fileCacheSize);

            // 计算缓存文件数量
            int cacheFileCount = getCacheFileCount();
            stats.put("cacheFileCount", cacheFileCount);

        } finally {
            lock.readLock().unlock();
        }

        return stats;
    }

    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        lock.writeLock().lock();
        try {
            // 清空内存缓存
            memoryCache.clear();

            // 关闭当前缓存文件写入器
            if (cacheWriter != null) {
                cacheWriter.close();
                cacheWriter = null;
            }

            // 删除所有缓存文件
            deleteCacheFiles();

            // 重新初始化缓存文件
            initCurrentCacheFile();

            // 重置统计信息
            totalCachedCount = 0;
            totalUploadedCount = 0;
            totalFailedCount = 0;

            log.info("所有缓存已清空");

        } catch (Exception e) {
            log.error("清空缓存失败", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 持久化内存缓存到文件
     */
    private void persistMemoryCache() {
        if (memoryCache.isEmpty()) {
            return;
        }

        lock.writeLock().lock();
        try {
            List<DeviceData> dataToWrite = new ArrayList<>();

            // 从内存缓存中取出数据
            while (!memoryCache.isEmpty() && dataToWrite.size() < 1000) {
                DeviceData data = memoryCache.poll();
                if (data != null) {
                    dataToWrite.add(data);
                }
            }

            // 写入文件
            if (!dataToWrite.isEmpty()) {
                writeDataToFile(dataToWrite);
                log.debug("持久化内存缓存到文件: count={}", dataToWrite.size());
            }

        } catch (Exception e) {
            log.error("持久化内存缓存失败", e);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 初始化当前缓存文件
     */
    private void initCurrentCacheFile() throws IOException {
        String fileName = "cache_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".dat";
        currentCacheFile = new File(cacheDirectory, fileName);
        cacheWriter = new BufferedWriter(new FileWriter(currentCacheFile, true));
    }

    /**
     * 写入数据到文件
     */
    private void writeDataToFile(List<DeviceData> dataList) throws IOException {
        if (cacheWriter == null) {
            initCurrentCacheFile();
        }

        // 检查文件大小，如果超过限制则创建新文件
        if (currentCacheFile.length() > maxFileSize * 1024 * 1024) {
            cacheWriter.close();
            initCurrentCacheFile();
        }

        for (DeviceData data : dataList) {
            String line = serializeDeviceData(data);
            cacheWriter.write(line);
            cacheWriter.newLine();
        }

        cacheWriter.flush();
    }

    /**
     * 从文件加载缓存数据
     */
    private List<DeviceData> loadCachedDataFromFiles(int maxCount) {
        List<DeviceData> result = new ArrayList<>();

        try {
            File[] cacheFiles = getCacheFiles();

            for (File file : cacheFiles) {
                if (result.size() >= maxCount) {
                    break;
                }

                try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
                    String line;
                    while ((line = reader.readLine()) != null && result.size() < maxCount) {
                        DeviceData data = deserializeDeviceData(line);
                        if (data != null) {
                            result.add(data);
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.error("从文件加载缓存数据失败", e);
        }

        return result;
    }

    /**
     * 序列化设备数据
     */
    private String serializeDeviceData(DeviceData data) {
        return String.format("%s|%s|%d|%s|%d|%s",
                data.getDeviceCode(),
                data.getProjectId() != null ? data.getProjectId() : "",
                data.getDataCode(),
                data.getDataValue().toString(),
                data.getReportTime(),
                data.getExtData() != null ? data.getExtData() : "");
    }

    /**
     * 反序列化设备数据
     */
    private DeviceData deserializeDeviceData(String line) {
        try {
            String[] parts = line.split("\\|");
            if (parts.length >= 5) {
                DeviceData data = new DeviceData();
                data.setDeviceCode(parts[0]);
                data.setProjectId(parts[1].isEmpty() ? null : parts[1]);
                data.setDataCode(Byte.parseByte(parts[2]));
                data.setDataValue(new java.math.BigDecimal(parts[3]));
                data.setReportTime(Long.parseLong(parts[4]));
                if (parts.length > 5 && !parts[5].isEmpty()) {
                    data.setExtData(parts[5]);
                }
                return data;
            }
        } catch (Exception e) {
            log.warn("反序列化设备数据失败: {}", line, e);
        }
        return null;
    }

    /**
     * 生成数据唯一键
     */
    private String generateDataKey(DeviceData data) {
        return data.getDeviceCode() + "_" + data.getDataCode() + "_" + data.getReportTime();
    }

    /**
     * 获取缓存文件列表
     */
    private File[] getCacheFiles() {
        File dir = new File(cacheDirectory);
        File[] files = dir.listFiles((file, name) -> name.startsWith("cache_") && name.endsWith(".dat"));
        if (files != null) {
            Arrays.sort(files, Comparator.comparing(File::lastModified));
        }
        return files != null ? files : new File[0];
    }

    /**
     * 计算文件缓存大小
     */
    private long calculateFileCacheSize() {
        long totalSize = 0;
        File[] cacheFiles = getCacheFiles();
        for (File file : cacheFiles) {
            totalSize += file.length();
        }
        return totalSize;
    }

    /**
     * 获取缓存文件数量
     */
    private int getCacheFileCount() {
        return getCacheFiles().length;
    }

    /**
     * 清理过期的缓存文件
     */
    private void cleanExpiredCacheFiles() {
        try {
            long expireTime = System.currentTimeMillis() - (retentionDays * 24 * 60 * 60 * 1000L);
            File[] cacheFiles = getCacheFiles();

            int deletedCount = 0;
            for (File file : cacheFiles) {
                if (file.lastModified() < expireTime) {
                    if (file.delete()) {
                        deletedCount++;
                        log.debug("删除过期缓存文件: {}", file.getName());
                    }
                }
            }

            if (deletedCount > 0) {
                log.info("清理过期缓存文件: count={}", deletedCount);
            }

        } catch (Exception e) {
            log.error("清理过期缓存文件失败", e);
        }
    }

    /**
     * 删除所有缓存文件
     */
    private void deleteCacheFiles() {
        File[] cacheFiles = getCacheFiles();
        for (File file : cacheFiles) {
            if (file.delete()) {
                log.debug("删除缓存文件: {}", file.getName());
            }
        }
    }

    /**
     * 检查是否有缓存数据需要上传
     */
    public boolean hasCachedData() {
        lock.readLock().lock();
        try {
            return !memoryCache.isEmpty() || getCacheFileCount() > 0;
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 标记上传失败的数据
     */
    public void markUploadFailed(List<DeviceData> failedData) {
        if (failedData != null && !failedData.isEmpty()) {
            totalFailedCount += failedData.size();
            log.warn("标记上传失败的数据: count={}", failedData.size());
        }
    }
}
