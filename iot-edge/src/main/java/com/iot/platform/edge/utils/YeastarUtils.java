package com.iot.platform.edge.utils;

import com.alibaba.fastjson2.JSON;
import com.iot.platform.edge.enums.YeastarDeviceType;
import com.iot.platform.edge.model.yeastar.YeastarLinkData;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Base64;

@Slf4j
public class YeastarUtils {

    /**
     * 从 YeastarLinkData 对象的 devEUI 中提取设备型号。
     * 根据注释，设备型号信息位于 devEUI 的第 7-9 位（索引 6-8）。
     * 尝试将这 3 位字符解析为 YeastarDeviceType 枚举，如果失败则返回 UNKNOWN。
     *
     * @param data YeastarLinkData 对象
     * @return 解析出的 YeastarDeviceType，或 UNKNOWN
     */
    public static YeastarDeviceType getDeviceModel(YeastarLinkData data) {
        final int size = 3; // 提取3位
        final int startIndex = 6; // 从索引6开始 (第7位)
        try {
            String devEUI = data.getDevEUI();
            if (devEUI == null || devEUI.length() < startIndex + size) {
                return YeastarDeviceType.UNKNOWN;
            }
            // 提取子串
            String modelStr = devEUI.substring(startIndex, startIndex + size);
            return YeastarDeviceType.valueOf(modelStr);
        } catch (Exception e) {
            // 解析 DeviceModel 失败
            return YeastarDeviceType.UNKNOWN;
        }
    }

    /**
     * 解析输入的 JSON 字符串为 YeastarLinkData 对象。
     * 如果解析成功且关键字段（applicationID, devEUI, data, time）不为 null，则返回对象，否则返回 null。
     *
     * @param json JSON 字符串
     * @return 解析后的 YeastarLinkData 对象，或 null
     */
    public static YeastarLinkData parse(String json) {
        try {
            YeastarLinkData result = JSON.parseObject(json, YeastarLinkData.class);
            if (result.getApplicationID() == null ||
                    result.getDevEUI() == null ||
                    result.getData() == null ||
                    result.getTime() == null) {
                return null;
            }
            return result;
        } catch (Exception e) {
            log.error("解析 YeastarLinkData 失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 将 Base64 编码的字符串解码为字节数组。
     * @param text Base64 编码的字符串
     * @return 解码后的字节数组，或 null（如果解码失败）
     */
    public static byte[] decodeData(String text) {
        try {
            return Base64.getDecoder().decode(text);
        } catch (Exception e) {
            log.error("解码 Base64 字符串失败: {}", e.getMessage());
        }
        return null;
    }

    /**
     * 将 Base64 编码的字符串解码并转换为十六进制（hex）字符串。
     *
     * @param text Base64 编码的字符串
     * @return 转换后的十六进制字符串，或 null（如果解码失败）
     */
    public static String decodeDataHex(String text) {
        try {
            byte[] bytes = Base64.getDecoder().decode(text);
            StringBuilder hexStringBuilder = new StringBuilder();
            for (byte b : bytes) {
                // %02x: 格式化为至少2位的十六进制数，不足补0。& 0xFF 确保处理负数 byte。
                hexStringBuilder.append(String.format("%02x", b & 0xFF));
            }
            return hexStringBuilder.toString();
        } catch (Exception e) {
            e.printStackTrace(); // 打印异常栈
        }
        return null;
    }

    /**
     * 获取 byte 的无符号整数值。
     *
     * @param b the byte value
     * @return the unsigned int value of the byte
     */
    public static int toIntValue(byte b) {
        return b & 0xFF; // 将 byte 转换为 int，并通过 & 0xFF 处理负数
    }

    /**
     * 获取 byte 的无符号长整数值。
     * @param b the byte value
     * @return the unsigned long value of the byte
     */
    public static long toLongValue(byte b) {
        return b & 0xFFL; // 将 byte 转换为 long，并通过 & 0xFFL 处理负数
    }

    public static BigDecimal hexStringToBigEndianLong(String hexStr) {
        // 如果字符串为空，返回 0
        if (hexStr == null || hexStr.isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            // 1. 使用 BigInteger 从十六进制字符串创建 BigInteger 实例
            BigInteger bigInt = new BigInteger(hexStr, 16);
            // 2. 将 BigInteger 转换为 BigDecimal
            return new BigDecimal(bigInt);
        } catch (NumberFormatException e) {
            // 处理无效的十六进制字符串
            // 可以选择抛出异常，或者返回默认值（如 BigDecimal.ZERO），取决于你的需求
            // 这里选择抛出，让调用者处理
            throw new NumberFormatException("Invalid hexadecimal string: " + hexStr + ". " + e.getMessage());
        }
    }
}
