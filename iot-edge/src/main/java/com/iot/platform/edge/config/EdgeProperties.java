package com.iot.platform.edge.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 边缘程序配置属性
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
@Component
@ConfigurationProperties(prefix = "iot.edge")
public class EdgeProperties {

    /**
     * 边缘程序ID
     */
    private String edgeId = "edge-001";

    /**
     * 边缘程序名称
     */
    private String edgeName = "默认边缘程序";

    /**
     * 平台API地址
     */
    private String platformUrl = "http://localhost:8000/api";

    /**
     * 服务器配置
     */
    private Server server = new Server();

    /**
     * API认证配置
     */
    private Auth auth = new Auth();

    /**
     * 数据采集配置
     */
    private DataCollection dataCollection = new DataCollection();

    /**
     * MQTT配置
     */
    private Mqtt mqtt = new Mqtt();

    /**
     * 设备连接配置
     */
    private Device device = new Device();

    @Data
    public static class Auth {
        /**
         * Access Key
         */
        private String accessKey;

        /**
         * Secret Key
         */
        private String secretKey;
    }

    @Data
    public static class DataCollection {
        /**
         * 数据采集间隔（秒）
         */
        private Integer interval = 60;

        /**
         * 批量上报大小
         */
        private Integer batchSize = 100;

        /**
         * 数据缓存最大大小
         */
        private Integer maxCacheSize = 1000;
    }

    @Data
    public static class Mqtt {
        /**
         * MQTT Broker地址
         */
        @Value("${iot.edge.mqtt.broker-url}")
        private String brokerUrl;

        /**
         * 客户端ID
         */
        @Value("${iot.edge.mqtt.client-id}")
        private String clientId;

        /**
         * 用户名
         */
        @Value("${iot.edge.mqtt.username}")
        private String username;

        /**
         * 密码
         */
        @Value("${iot.edge.mqtt.password}")
        private String password;

        /**
         * 保持连接时间（秒）
         */
        private Integer keepAlive = 60;

        /**
         * 连接超时时间（秒）
         */
        private Integer connectionTimeout = 30;
    }

    @Data
    public static class Device {
        /**
         * 设备连接超时时间（秒）
         */
        private Integer connectionTimeout = 10;

        /**
         * 设备读取超时时间（秒）
         */
        private Integer readTimeout = 5;

        /**
         * 设备重连最大尝试次数
         */
        private Integer maxRetryAttempts = 3;
    }

    @Data
    public static class Server {
        /**
         * 服务器主机地址
         */
        private String host = "localhost";

        /**
         * 服务器端口
         */
        private Integer port = 8080;

        /**
         * 是否使用SSL
         */
        private Boolean ssl = false;
    }

}
