package com.iot.platform.edge.config;

import com.iot.platform.edge.adapter.MessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class ReceiverMessageHandler implements MessageHandler {

    @Autowired
    private MqttConfigurationProperties mqttConfigurationProperties;

    @Autowired
    private List<MessageConverter> messageConverterList;

    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        MessageHeaders headers = message.getHeaders();
        String receivedTopicName = (String) headers.get("mqtt_receivedTopic");
        if (mqttConfigurationProperties.getSubTopic().equals(receivedTopicName)) {
            System.out.println("接收到消息：" + message.getPayload());
            for (MessageConverter converter : messageConverterList) {
                converter.convert(message.getPayload().toString());
            }
        }
    }

}
