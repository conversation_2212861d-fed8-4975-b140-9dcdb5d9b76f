package com.iot.platform.edge.service;

import com.iot.platform.edge.model.DeviceCommand;
import com.iot.platform.edge.model.DeviceData;

import java.util.List;
import java.util.Map;

/**
 * MQTT通信服务接口
 * 负责与平台的MQTT双向通信
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface MqttCommunicationService {

    /**
     * 初始化MQTT连接
     *
     * @return 初始化结果
     */
    boolean initialize();

    /**
     * 连接到MQTT Broker
     *
     * @return 连接结果
     */
    boolean connect();

    /**
     * 断开MQTT连接
     *
     * @return 断开结果
     */
    boolean disconnect();

    /**
     * 检查MQTT连接状态
     *
     * @return 是否已连接
     */
    boolean isConnected();

    /**
     * 发送设备数据到平台
     *
     * @param deviceData 设备数据
     * @return 发送结果
     */
    boolean publishDeviceData(DeviceData deviceData);

    /**
     * 批量发送设备数据到平台
     *
     * @param dataList 设备数据列表
     * @return 发送结果
     */
    boolean publishBatchDeviceData(List<DeviceData> dataList);

    /**
     * 发送边缘程序心跳
     *
     * @param heartbeatData 心跳数据
     * @return 发送结果
     */
    boolean publishHeartbeat(Map<String, Object> heartbeatData);

    /**
     * 发送边缘程序状态
     *
     * @param statusData 状态数据
     * @return 发送结果
     */
    boolean publishStatus(Map<String, Object> statusData);

    /**
     * 发送指令执行响应
     *
     * @param commandId 指令ID
     * @param response 执行响应
     * @return 发送结果
     */
    boolean publishCommandResponse(String commandId, Map<String, Object> response);

    /**
     * 订阅平台指令主题
     *
     * @return 订阅结果
     */
    boolean subscribeToCommands();

    /**
     * 订阅平台配置更新主题
     *
     * @return 订阅结果
     */
    boolean subscribeToConfigUpdates();

    /**
     * 处理接收到的指令消息
     *
     * @param topic 主题
     * @param payload 消息内容
     */
    void handleCommandMessage(String topic, String payload);

    /**
     * 处理接收到的配置更新消息
     *
     * @param topic 主题
     * @param payload 消息内容
     */
    void handleConfigUpdateMessage(String topic, String payload);

    /**
     * 获取MQTT连接状态信息
     *
     * @return 状态信息
     */
    Map<String, Object> getConnectionStatus();

    /**
     * 获取MQTT统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getStatistics();

    /**
     * 重新连接MQTT
     *
     * @return 重连结果
     */
    boolean reconnect();

    /**
     * 销毁MQTT服务
     */
    void destroy();
}
