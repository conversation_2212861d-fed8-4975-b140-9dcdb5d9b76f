package com.iot.platform.edge.service;

import com.iot.platform.edge.model.DeviceData;

import java.util.List;

/**
 * 设备连接器服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface DeviceConnectorService {

    /**
     * 连接设备
     *
     * @param deviceCode 设备识别码
     * @return 连接是否成功
     */
    boolean connectDevice(String deviceCode);

    /**
     * 断开设备连接
     *
     * @param deviceCode 设备识别码
     */
    void disconnectDevice(String deviceCode);

    /**
     * 检查设备连接状态
     *
     * @param deviceCode 设备识别码
     * @return 是否已连接
     */
    boolean isDeviceConnected(String deviceCode);

    /**
     * 获取所有已连接的设备
     *
     * @return 设备识别码列表
     */
    List<String> getConnectedDevices();

    /**
     * 读取设备数据
     *
     * @param deviceCode 设备识别码
     * @return 设备数据
     */
    DeviceData readDeviceData(String deviceCode);

    /**
     * 向设备发送指令
     *
     * @param deviceCode 设备识别码
     * @param command 指令内容
     * @return 执行是否成功
     */
    boolean sendCommand(String deviceCode, String command);

    /**
     * 获取设备状态
     *
     * @param deviceCode 设备识别码
     * @return 设备状态信息
     */
    String getDeviceStatus(String deviceCode);

    /**
     * 重连所有设备
     */
    void reconnectAllDevices();
}
