package com.iot.platform.edge.model.tuya;

// 内部类 BizDataWrapperDTO
public class BizDataWrapperDTO {
    private String bizCode;
    private BizDataDTO bizData;

    public BizDataWrapperDTO() {
        // 无参构造函数，用于反序列化
    }

    public BizDataWrapperDTO(String bizCode, BizDataDTO bizData) {
        this.bizCode = bizCode;
        this.bizData = bizData;
    }

    // Getter 和 Setter 方法
    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public BizDataDTO getBizData() {
        return bizData;
    }

    public void setBizData(BizDataDTO bizData) {
        this.bizData = bizData;
    }
}
