package com.iot.platform.edge.adapter.impl.yeastar;

import com.iot.platform.common.entity.Device;
import com.iot.platform.edge.adapter.YeastarDataConverter;
import com.iot.platform.edge.model.IotPointDataDTO;
import com.iot.platform.edge.model.yeastar.YeastarLinkData;
import com.iot.platform.edge.utils.YeastarUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.HandlerExceptionResolver;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Service("AM319")
public class AM319DataConverter implements YeastarDataConverter {
    private final HandlerExceptionResolver handlerExceptionResolver;

    public AM319DataConverter(HandlerExceptionResolver handlerExceptionResolver) {
        this.handlerExceptionResolver = handlerExceptionResolver;
    }

    @Override
    public List<IotPointDataDTO> apply(Device device, YeastarLinkData data) {
        List<IotPointDataDTO> res = new ArrayList<>();
        String bytes = YeastarUtils.decodeDataHex(data.getData());
        if (bytes == null) {
            bytes = "";
        }

        Integer ONE_BYTE_HEX = 2;

        Integer i = 0;

        while (i < bytes.length() - 1) {
            IotPointDataDTO dto = IotPointDataDTO.builder()
                    .projectId(device.getProjectId())
                    .deviceCode(device.getDeviceCode())
                    .edgeProgramId(device.getEdgeProgramId())
                    .build();

            // 读取 channelId (2个十六进制字符)
            String channelId;
            if (i + ONE_BYTE_HEX <= bytes.length()) {
                channelId = bytes.substring(i, i + ONE_BYTE_HEX);
            } else {
                break; // 长度不足，跳出循环
            }
            i += ONE_BYTE_HEX;

            // 读取 channelType (2个十六进制字符)，并转换为大写
            String channelType;
            if (i + ONE_BYTE_HEX <= bytes.length()) {
                channelType = bytes.substring(i, i + ONE_BYTE_HEX).toUpperCase();
            } else {
                break; // 长度不足，跳出循环
            }
            i += ONE_BYTE_HEX;
            // BATTERY 电量
            if ("01".equalsIgnoreCase(channelId) && "75".equalsIgnoreCase(channelType)) {
                int length = 1 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)));
                i += length;
            }
            // TEMPERATURE  温度
            else if ("03".equalsIgnoreCase(channelId) && "67".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                BigDecimal value = YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).divide(new BigDecimal(10), 2, BigDecimal.ROUND_HALF_UP);
                dto.setDataValue(value);
                i += length;
            }
            // HUMIDITY 湿度
            else if ("04".equalsIgnoreCase(channelId) && "68".equalsIgnoreCase(channelType)) {
                int length = 1 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).divide(new BigDecimal(10), 2, BigDecimal.ROUND_HALF_UP));
                i += length;
            }
            // PIR  被动红外
            else if ("05".equalsIgnoreCase(channelId) && "00".equalsIgnoreCase(channelType)) {
                int length = 1 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)));
                i += length;
            }
            // LIGHT    光照水平
            else if ("06".equalsIgnoreCase(channelId) && "cb".equalsIgnoreCase(channelType)) {
                int length = 1 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)));
                i += length;
            }
            // CO2  二氧化碳
            else if ("07".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)));
                i += length;
            }
            // TVOC (iaq)   挥发性有机化合物
            else if ("08".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                i += length;
            }
            // PRESSURE  压力
            else if ("09".equalsIgnoreCase(channelId) && "73".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).divide(new BigDecimal(10), 2, BigDecimal.ROUND_HALF_UP));
                i += length;
            }
            // HCHO 甲醛
            else if ("0a".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                i += length;
            }
            // PM2.5
            else if ("0b".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)));
                i += length;
            }
            // PM10 颗粒物10
            else if ("0c".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)));
                i += length;
            }
            // o3   氧气
            else if ("0d".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 2 * ONE_BYTE_HEX;
                dto.setDataValue(YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP));
                i += length;
            }
            //BEEP  蜂鸣声
            else if ("0e".equalsIgnoreCase(channelId) && "7d".equalsIgnoreCase(channelType)) {
                int length = 1 * ONE_BYTE_HEX;
                int value = YeastarUtils.hexStringToBigEndianLong(bytes.substring(i, i + length)).compareTo(new BigDecimal(1L));
                dto.setDataValue(new BigDecimal(value));
                i += length;
            }
            // HISTORY DATA (AM307)
            else if ("20".equalsIgnoreCase(channelId) && "ce".equalsIgnoreCase(channelType)) {
                int length = 16 * ONE_BYTE_HEX;
                //不要这个数据
                i += length;
            }
            // HISTORY DATA (AM308)
            else if ("20".equalsIgnoreCase(channelId) && "ce".equalsIgnoreCase(channelType)) {
                int length = 20 * ONE_BYTE_HEX;
                //不要这个数据
                i += length;
            }
            // HISTORY DATA (AM319 CH2O)
            else if ("20".equalsIgnoreCase(channelId) && "ce".equalsIgnoreCase(channelType)) {
                int length = 22 * ONE_BYTE_HEX;
                //不要这个数据
                i += length;
            }
            // HISTORY DATA (AM319 O3)
            else if ("20".equalsIgnoreCase(channelId) && "ce".equalsIgnoreCase(channelType)) {
                int length = 22 * ONE_BYTE_HEX;
                //不要这个数据
                i += length;
            } else {
                break;
            }
            res.add(dto);
        }

        return res;
    }
}
