package com.iot.platform.edge.adapter;

import com.iot.platform.edge.model.DeviceCommand;
import com.iot.platform.edge.model.DeviceData;

import java.util.List;
import java.util.Map;

/**
 * 设备适配器接口
 * 定义不同厂商设备的统一访问接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface DeviceAdapter {

    /**
     * 获取适配器类型
     *
     * @return 适配器类型标识
     */
    String getAdapterType();

    /**
     * 获取支持的厂商
     *
     * @return 厂商标识列表
     */
    List<String> getSupportedVendors();

    /**
     * 初始化适配器
     *
     * @param config 配置参数
     * @return 初始化结果
     */
    boolean initialize(Map<String, Object> config);

    /**
     * 连接设备
     *
     * @param deviceCode 设备编码
     * @param connectionParams 连接参数
     * @return 连接结果
     */
    boolean connect(String deviceCode, Map<String, Object> connectionParams);

    /**
     * 断开设备连接
     *
     * @param deviceCode 设备编码
     * @return 断开结果
     */
    boolean disconnect(String deviceCode);

    /**
     * 检查设备连接状态
     *
     * @param deviceCode 设备编码
     * @return 是否已连接
     */
    boolean isConnected(String deviceCode);

    /**
     * 读取设备数据
     *
     * @param deviceCode 设备编码
     * @param dataPoints 数据点列表
     * @return 设备数据列表
     */
    List<DeviceData> readDeviceData(String deviceCode, List<String> dataPoints);

    /**
     * 向设备发送控制指令
     *
     * @param deviceCode 设备编码
     * @param command 控制指令
     * @return 指令执行结果
     */
    boolean sendCommand(String deviceCode, DeviceCommand command);

    /**
     * 获取设备状态信息
     *
     * @param deviceCode 设备编码
     * @return 设备状态信息
     */
    Map<String, Object> getDeviceStatus(String deviceCode);

    /**
     * 获取设备配置信息
     *
     * @param deviceCode 设备编码
     * @return 设备配置信息
     */
    Map<String, Object> getDeviceConfig(String deviceCode);

    /**
     * 更新设备配置
     *
     * @param deviceCode 设备编码
     * @param config 新的配置信息
     * @return 更新结果
     */
    boolean updateDeviceConfig(String deviceCode, Map<String, Object> config);

    /**
     * 获取设备诊断信息
     *
     * @param deviceCode 设备编码
     * @return 诊断信息
     */
    Map<String, Object> getDiagnosticInfo(String deviceCode);

    /**
     * 重置设备
     *
     * @param deviceCode 设备编码
     * @return 重置结果
     */
    boolean resetDevice(String deviceCode);

    /**
     * 销毁适配器资源
     */
    void destroy();

    /**
     * 获取适配器健康状态
     *
     * @return 健康状态信息
     */
    Map<String, Object> getHealthStatus();
}
