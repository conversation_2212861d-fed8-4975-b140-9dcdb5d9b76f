package com.iot.platform.edge.service.impl;

import com.iot.platform.edge.config.EdgeProperties;
import com.iot.platform.edge.model.DeviceData;
import com.iot.platform.edge.service.DataCollectionService;
import com.iot.platform.edge.service.DeviceConnectorService;
import com.iot.platform.edge.service.NetworkMonitorService;
import com.iot.platform.edge.service.OfflineCacheService;
import com.iot.platform.edge.service.PlatformApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 数据采集服务实现类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCollectionServiceImpl implements DataCollectionService {

    private final EdgeProperties edgeProperties;
    private final DeviceConnectorService deviceConnectorService;
    private final PlatformApiService platformApiService;
    private final NetworkMonitorService networkMonitorService;
    private final OfflineCacheService offlineCacheService;

    private final AtomicBoolean collecting = new AtomicBoolean(false);
    private final ConcurrentLinkedQueue<DeviceData> dataCache = new ConcurrentLinkedQueue<>();

    @PostConstruct
    public void init() {
        log.info("数据采集服务初始化完成");
    }

    @PreDestroy
    public void destroy() {
        stopCollection();
        log.info("数据采集服务已停止");
    }

    @Override
    public void startCollection() {
        if (collecting.compareAndSet(false, true)) {
            log.info("开始数据采集");
        }
    }

    @Override
    public void stopCollection() {
        if (collecting.compareAndSet(true, false)) {
            log.info("停止数据采集");
        }
    }

    @Override
    public boolean isCollecting() {
        return collecting.get();
    }

    /**
     * 定时数据采集任务
     */
    @Scheduled(fixedDelayString = "#{${iot.edge.data-collection.interval:60} * 1000}")
    public void scheduledCollection() {
        if (!isCollecting()) {
            return;
        }

        try {
            log.debug("执行定时数据采集任务");

            // 获取所有连接的设备
            List<String> deviceCodes = deviceConnectorService.getConnectedDevices();
            if (deviceCodes.isEmpty()) {
                log.debug("暂无连接的设备");
                return;
            }

            // 批量采集数据
            List<DeviceData> dataList = collectBatchDeviceData(deviceCodes);

            // 处理采集到的数据
            if (!dataList.isEmpty()) {
                processCollectedData(dataList);
            }

            log.info("采集到 {} 条数据，当前缓存大小: {}", dataList.size(), dataCache.size());

        } catch (Exception e) {
            log.error("定时数据采集任务执行失败", e);
        }
    }

    @Override
    public DeviceData collectDeviceData(String deviceCode) {
        try {
            log.debug("采集设备数据 - 设备码: {}", deviceCode);

            // 从设备连接器获取数据
            DeviceData data = deviceConnectorService.readDeviceData(deviceCode);

            if (data != null) {
                log.debug("成功采集设备数据 - 设备码: {}, 数据类型: {}, 数据值: {}",
                    deviceCode, data.getDataCode(), data.getDataValue());
            } else {
                log.warn("采集设备数据失败 - 设备码: {}", deviceCode);
            }

            return data;

        } catch (Exception e) {
            log.error("采集设备数据异常 - 设备码: {}", deviceCode, e);
            return null;
        }
    }

    @Override
    public List<DeviceData> collectBatchDeviceData(List<String> deviceCodes) {
        List<DeviceData> dataList = new ArrayList<>();

        for (String deviceCode : deviceCodes) {
            DeviceData data = collectDeviceData(deviceCode);
            if (data != null) {
                dataList.add(data);
            }
        }

        return dataList;
    }

    @Override
    public List<DeviceData> getCachedData() {
        return new ArrayList<>(dataCache);
    }

    @Override
    public void clearCache() {
        dataCache.clear();
        log.info("数据缓存已清空");
    }

    /**
     * 添加数据到缓存
     */
    private void addToCache(DeviceData data) {
        dataCache.offer(data);

        // 检查缓存大小，超出限制时移除旧数据
        Integer maxCacheSize = edgeProperties.getDataCollection().getMaxCacheSize();
        while (dataCache.size() > maxCacheSize) {
            DeviceData removedData = dataCache.poll();
            if (removedData != null) {
                log.warn("缓存已满，移除旧数据 - 设备码: {}, 时间戳: {}",
                    removedData.getDeviceCode(), removedData.getTimestamp());
            }
        }
    }

    /**
     * 处理采集到的数据
     */
    private void processCollectedData(List<DeviceData> dataList) {
        // 检查网络连接状态
        if (networkMonitorService.isNetworkConnected()) {
            // 网络连接正常，尝试直接上传
            try {
                boolean success = platformApiService.uploadDataBatch(dataList);
                if (success) {
                    log.debug("数据直接上传成功: count={}", dataList.size());
                    return;
                } else {
                    log.warn("数据直接上传失败，转为缓存模式");
                }
            } catch (Exception e) {
                log.warn("数据直接上传异常，转为缓存模式", e);
            }
        }

        // 网络断开或上传失败，缓存数据
        offlineCacheService.cacheDeviceDataBatch(dataList);
        log.info("数据已缓存，等待网络恢复后上传: count={}", dataList.size());

        // 同时也添加到内存缓存（保持原有逻辑）
        for (DeviceData data : dataList) {
            addToCache(data);
        }
    }

    /**
     * 检查并上报数据
     */
    private void checkAndUploadData() {
        Integer batchSize = edgeProperties.getDataCollection().getBatchSize();

        if (dataCache.size() >= batchSize) {
            List<DeviceData> uploadData = new ArrayList<>();

            // 从缓存中取出指定数量的数据
            for (int i = 0; i < batchSize && !dataCache.isEmpty(); i++) {
                DeviceData data = dataCache.poll();
                if (data != null) {
                    uploadData.add(data);
                }
            }

            if (!uploadData.isEmpty()) {
                // 处理数据（包含网络状态检查和缓存逻辑）
                processCollectedData(uploadData);
            }
        }
    }
}
