package com.iot.platform.edge.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@ApiModel
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IotPointDataDTO {

    @ApiModelProperty(value = "项目ID")
    private String projectId;

    @ApiModelProperty(value = "设备编号")
    private String deviceCode;

    @ApiModelProperty(value = "设备厂商")
    private Byte dataCode;

    @ApiModelProperty(value = "数据值")
    private BigDecimal dataValue;

    @ApiModelProperty(value = "数据质量等级")
    private Byte quality;

    @ApiModelProperty(value = "数据上报时间戳")
    private Long reportTime;

    @ApiModelProperty(value = "边缘程序ID")
    private String edgeProgramId;

    @ApiModelProperty(value = "扩展数据")
    private String extData;

}
