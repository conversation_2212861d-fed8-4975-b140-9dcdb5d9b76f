package com.iot.platform.edge.utils;

import com.alibaba.fastjson.JSON;
import com.iot.platform.edge.model.tuya.TuYaParseMessageDTO;

public class TuYaUtils {
    private TuYaUtils() {
        throw new AssertionError("No instances for you!");
    }
    public static final String URL = "pulsar+ssl://mqe.tuyacn.com:7285/";

    public static final String ACCESS_ID = "9ghjytgcunvywnem7jsd";
    public static final String ACCESS_KEY = "fadc9b3eb1d34df2b88c38c079a2c8dd";

    /**
     * 尝试解析输入的 JSON 字符串为 TuYaParseMessageDTO 对象。
     * 如果解析成功且数据校验通过（devId 不为 null）则返回对象，否则返回 null。
     *
     * @param input JSON 字符串
     * @return 解析后的 TuYaParseMessageDTO 对象，或 null
     */
    public static TuYaParseMessageDTO parse(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }
        try {
            TuYaParseMessageDTO result = JSON.parseObject(input, TuYaParseMessageDTO.class);

            if (result.getData() == null ||
                    result.getData().getBizData() == null ||
                    result.getData().getBizData().getDevId() == null) {
                return null; // 校验失败，返回 null
            }

            return result;

        } catch (Exception e) {
            // runCatching 会捕获所有异常，getOrNull 在异常时返回 null
            // 捕获 Json 解析异常、空指针异常等
            return null;
        }
    }

    /**
     * 对输入的十六进制编码（实际是 Base64 编码的密文）数据进行 AES 解密。
     * 使用 ACCESS_KEY 的第 8 到 23 个字符（索引 8 到 23，共 16 个字符）作为解密密钥。
     *
     * @param data 需要解密的字符串（通常是 Base64 编码的密文）
     * @return 解密后的字符串，或 null（如果解密失败）
     */
    public static String decodeDataHex(String data) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        try {
            // 提取 ACCESS_KEY 的子串作为密钥 (索引 8 到 23，共 16 个字符)
            String key = ACCESS_KEY.substring(8, 24); // substring(start, end) in Java: [start, end)
            // 调用解密工具
            String decrypt = AESBase64Utils.decrypt(data, key);
            return decrypt;
        } catch (Exception e) {
            // 捕获可能的异常，如 StringIndexOutOfBoundsException (如果 ACCESS_KEY 长度不够), 或解密过程中的异常
            // 根据业务需求，可以选择记录日志或直接返回 null
            // System.err.println("Decryption failed: " + e.getMessage());
            return null;
        }
    }
}
