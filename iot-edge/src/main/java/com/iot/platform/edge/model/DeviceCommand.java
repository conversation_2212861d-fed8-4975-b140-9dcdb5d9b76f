package com.iot.platform.edge.model;

import lombok.Data;

import java.util.Map;

/**
 * 设备控制指令模型
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
public class DeviceCommand {

    /**
     * 指令ID
     */
    private String commandId;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 指令类型
     */
    private String commandType;

    /**
     * 指令参数
     */
    private Map<String, Object> parameters;

    /**
     * 指令优先级：1-低,2-中,3-高,4-紧急
     */
    private Integer priority = 2;

    /**
     * 超时时间（秒）
     */
    private Integer timeout = 30;

    /**
     * 是否需要响应确认
     */
    private Boolean requireResponse = true;

    /**
     * 重试次数
     */
    private Integer retryCount = 3;

    /**
     * 指令创建时间戳
     */
    private Long createTime;

    /**
     * 指令发送时间戳
     */
    private Long sendTime;

    /**
     * 指令状态
     */
    private CommandStatus status = CommandStatus.PENDING;

    /**
     * 执行结果
     */
    private Map<String, Object> result;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 扩展元数据
     */
    private Map<String, Object> metadata;

    /**
     * 指令状态枚举
     */
    public enum CommandStatus {
        PENDING("待发送"),
        SENT("已发送"),
        EXECUTING("执行中"),
        SUCCESS("成功"),
        FAILED("失败"),
        TIMEOUT("超时"),
        CANCELLED("已取消");

        private final String description;

        CommandStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建指令
     */
    public static DeviceCommand create(String deviceCode, String commandType, Map<String, Object> parameters) {
        DeviceCommand command = new DeviceCommand();
        command.setCommandId(generateCommandId());
        command.setDeviceCode(deviceCode);
        command.setCommandType(commandType);
        command.setParameters(parameters);
        command.setCreateTime(System.currentTimeMillis());
        return command;
    }

    /**
     * 生成指令ID
     */
    private static String generateCommandId() {
        return "CMD_" + System.currentTimeMillis() + "_" +
               Integer.toHexString((int)(Math.random() * 0xFFFF));
    }

    /**
     * 标记指令为已发送
     */
    public void markAsSent() {
        this.status = CommandStatus.SENT;
        this.sendTime = System.currentTimeMillis();
    }

    /**
     * 标记指令为执行中
     */
    public void markAsExecuting() {
        this.status = CommandStatus.EXECUTING;
    }

    /**
     * 标记指令为成功
     */
    public void markAsSuccess(Map<String, Object> result) {
        this.status = CommandStatus.SUCCESS;
        this.result = result;
    }

    /**
     * 标记指令为失败
     */
    public void markAsFailed(String errorMessage) {
        this.status = CommandStatus.FAILED;
        this.errorMessage = errorMessage;
    }

    /**
     * 标记指令为超时
     */
    public void markAsTimeout() {
        this.status = CommandStatus.TIMEOUT;
        this.errorMessage = "指令执行超时";
    }

    /**
     * 标记指令为已取消
     */
    public void markAsCancelled() {
        this.status = CommandStatus.CANCELLED;
        this.errorMessage = "指令已被取消";
    }

    /**
     * 检查指令是否完成
     */
    public boolean isCompleted() {
        return status == CommandStatus.SUCCESS ||
               status == CommandStatus.FAILED ||
               status == CommandStatus.TIMEOUT ||
               status == CommandStatus.CANCELLED;
    }

    /**
     * 检查指令是否成功
     */
    public boolean isSuccess() {
        return status == CommandStatus.SUCCESS;
    }

    /**
     * 检查指令是否失败
     */
    public boolean isFailed() {
        return status == CommandStatus.FAILED ||
               status == CommandStatus.TIMEOUT ||
               status == CommandStatus.CANCELLED;
    }

    /**
     * 获取执行耗时
     */
    public Long getExecutionTime() {
        if (sendTime != null && isCompleted()) {
            return System.currentTimeMillis() - sendTime;
        }
        return null;
    }
}
