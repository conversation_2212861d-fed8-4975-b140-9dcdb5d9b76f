package com.iot.platform.edge.model.tuya;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.iot.platform.edge.utils.TuYaUtils;

import java.lang.reflect.Type;
import java.io.Serializable;

// 主类
public class TuYaParseMessageDTO implements Serializable {
    // 使用 @JSONField 和 parseFeatures 来确保正确处理
    // 我们将通过 ParserConfig 注册自定义解析器来处理 data 字段的特殊逻辑
    private BizDataWrapperDTO data;
    private Integer protocol; // IoT Core 协议号 1001 推送的 deviceOnline 和旧版本协议号 20 推送的 online，均表示设备上线，
    private String pv;
    private String sign;
    private Long t;

    // Getter 和 Setter 方法
    @JSONField(name = "data")
    public BizDataWrapperDTO getData() {
        return data;
    }

    public void setData(BizDataWrapperDTO data) {
        this.data = data;
    }

    @JSONField(name = "protocol")
    public Integer getProtocol() {
        return protocol;
    }

    public void setProtocol(Integer protocol) {
        this.protocol = protocol;
    }

    @JSONField(name = "pv")
    public String getPv() {
        return pv;
    }

    public void setPv(String pv) {
        this.pv = pv;
    }

    @JSONField(name = "sign")
    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    @JSONField(name = "t")
    public Long getT() {
        return t;
    }

    public void setT(Long t) {
        this.t = t;
    }
}

// 内部类 PropertyItemDTO
class PropertyItemDTO {
    private String code; // 设备属性上报 code
    private String value; // 设备属性上报值
    private String dpId; // 功能点 ID
    private Long time;   // 设备属性上报时间戳，13 位

    public PropertyItemDTO() {
        // 无参构造函数，用于反序列化
    }

    public PropertyItemDTO(String code, String value, String dpId, Long time) {
        this.code = code;
        this.value = value;
        this.dpId = dpId;
        this.time = time;
    }

    // Getter 和 Setter 方法
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDpId() {
        return dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    // 重写 equals, hashCode, toString (可选)
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PropertyItemDTO that = (PropertyItemDTO) o;
        return java.util.Objects.equals(code, that.code) &&
                java.util.Objects.equals(value, that.value) &&
                java.util.Objects.equals(dpId, that.dpId) &&
                java.util.Objects.equals(time, that.time);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(code, value, dpId, time);
    }

    @Override
    public String toString() {
        return "PropertyItemDTO{" +
                "code='" + code + '\'' +
                ", value='" + value + '\'' +
                ", dpId='" + dpId + '\'' +
                ", time=" + time +
                '}';
    }
}

// 自定义反序列化器，用于处理 BizDataWrapperDTO 的解密逻辑
class BizDataWrapperDTOFastJsonDeserializer implements ObjectDeserializer {

    // 单例模式，提高性能
    public static final BizDataWrapperDTOFastJsonDeserializer INSTANCE = new BizDataWrapperDTOFastJsonDeserializer();

    @Override
    public <T> T deserialze(com.alibaba.fastjson.parser.DefaultJSONParser parser, Type type, Object fieldName) {
        // 获取当前解析的 JSON 文本
        String jsonString = parser.parseObject(String.class); // 假设 data 字段的值是一个需要解密的字符串
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }

        try {
            // 执行解密
            String decrypt = TuYaUtils.decodeDataHex(jsonString);

            // 使用 FastJSON 重新解析解密后的字符串
            // 注意：这里我们直接解析成 BizDataWrapperDTO，FastJSON 会处理嵌套的 JSON 结构
            // 我们不需要像 Gson 那样使用一个 UnLoop 类，因为 FastJSON 的解析机制不同
            return (T) JSON.parseObject(decrypt, BizDataWrapperDTO.class);
        } catch (Exception e) {
            // 解密或解析失败，返回 null
            System.err.println("Failed to decrypt or parse BizDataWrapperDTO: " + e.getMessage());
            return null;
        }
    }

    @Override
    public int getFastMatchToken() {
        // 返回 STRING，因为我们的输入期望是一个字符串（加密后的数据）
        return com.alibaba.fastjson.parser.JSONToken.LITERAL_STRING;
    }
}
