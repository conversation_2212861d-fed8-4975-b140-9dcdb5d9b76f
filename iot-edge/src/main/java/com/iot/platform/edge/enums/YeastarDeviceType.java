package com.iot.platform.edge.enums;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.pulsar.shade.com.google.common.collect.Maps;

import java.util.Collections;
import java.util.Map;

@ApiModel(description = "设备类型")
public enum YeastarDeviceType {

    /**
     * 人流传感器
     */
    @ApiModelProperty(value = "人流传感器")
    VS133("VS133", DeviceType.PEOPLE_FLOW),

    /**
     * 卫生间占用传感器
     */
    @ApiModelProperty(value = "卫生间占用传感器")
    VS330("VS330", DeviceType.OCCUPY),

    /**
     * 卫生间异味传感器
     */
    @ApiModelProperty(value = "卫生间异味传感器")
    GS301("GS301", DeviceType.OCCUPY),

    /**
     * 温湿度传感器
     */
    @ApiModelProperty(value = "温湿度传感器")
    EM300("EM300", DeviceType.TEMPERATURE_AND_HUMIDITY),

    /**
     * 室内环境传感器
     */
    @ApiModelProperty(value = "室内环境传感器")
    AM319("AM319", DeviceType.AMBIENCE_MONITORING),

    /**
     * 灯控开关面板 触摸面板
     */
    @ApiModelProperty(value = "灯控开关面板 触摸面板")
    WS50X_V1("WS50X_V1", DeviceType.LAMP_SWITCH_PANEL),

    /**
     * 灯控开关面板 机械面板
     */
    @ApiModelProperty(value = "灯控开关面板 机械面板")
    WS50X_V2("WS50X_V2", DeviceType.LAMP_SWITCH_PANEL),

    /**
     * 空间人数统计
     */
    @ApiModelProperty(value = "空间人数统计")
    VS_121("VS_121", DeviceType.PEOPLE_REGION_COUNT),

    /**
     * 母婴室占用
     */
    @ApiModelProperty(value = "母婴室占用")
    MSA_201Z("MSA_201Z", DeviceType.OCCUPY),

    /**
     * 未知
     */
    @ApiModelProperty(value = "未知")
    UNKNOWN("unknown", DeviceType.UNKNOWN);

    private String value;

    private DeviceType type;

    // 构造函数
    YeastarDeviceType(String value, DeviceType type) {
        this.value = value;
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public DeviceType getType() {
        return type;
    }

    private static Map<String, YeastarDeviceType> VALUE_MAP = Maps.newHashMap();

    static {
        for (YeastarDeviceType p : YeastarDeviceType.values()) {
            VALUE_MAP.put(p.getValue(), p);
        }
        VALUE_MAP = Collections.unmodifiableMap(VALUE_MAP);
    }

    public static YeastarDeviceType getByValue(String value) {
        return VALUE_MAP.get(value);
    }
}
