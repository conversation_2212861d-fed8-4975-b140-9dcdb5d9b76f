package com.iot.platform.edge.model.tuya;

import java.util.List;

// 内部类 BizDataDTO
public class BizDataDTO {
    private String dataId; // 该条上报数据的唯一标识 ID，无业务含义
    private String devId;  // 设备 ID
    private String productKey; // 设备所属的产品 ID
    private List<PropertyItemDTO> properties; // 该次设备属性数据上报集

    // Getter 和 Setter 方法
    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getDevId() {
        return devId;
    }

    public void setDevId(String devId) {
        this.devId = devId;
    }

    public String getProductKey() {
        return productKey;
    }

    public void setProductKey(String productKey) {
        this.productKey = productKey;
    }

    public List<PropertyItemDTO> getProperties() {
        return properties;
    }

    public void setProperties(List<PropertyItemDTO> properties) {
        this.properties = properties;
    }
}
