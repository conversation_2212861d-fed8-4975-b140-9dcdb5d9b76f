package com.iot.platform.edge.adapter.impl;

import com.iot.platform.common.entity.Device;
import com.iot.platform.edge.adapter.MessageConverter;
import com.iot.platform.edge.adapter.YeastarDataConverter;
import com.iot.platform.edge.enums.YeastarDeviceType;
import com.iot.platform.edge.model.DeviceData;
import com.iot.platform.edge.model.yeastar.YeastarLinkData;
import com.iot.platform.edge.service.PlatformApiService;
import com.iot.platform.edge.utils.YeastarUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class YeastarMessageConverter implements MessageConverter{

    @Autowired
    private Map<String, YeastarDataConverter> dataConverterMap = new HashMap<>();

    @Autowired
    private PlatformApiService platformApiService;

    @Override
    public DeviceData convert(String message) {
        YeastarLinkData data = YeastarUtils.parse(message);
        Device device = platformApiService.getDeviceByCode(data.getDevEUI());
        YeastarDeviceType deviceType = YeastarDeviceType.getByValue(device.getModel()) != null ? YeastarDeviceType.getByValue(device.getModel()) : YeastarDeviceType.UNKNOWN;
        switch (deviceType){
            case VS330 :
                YeastarDataConverter vs330 = dataConverterMap.get("VS330");
                vs330.apply(device,data);
                break;
            case AM319:
                YeastarDataConverter am319 = dataConverterMap.get("AM319");
                am319.apply(device,data);
        }
        return null;
    }
}
