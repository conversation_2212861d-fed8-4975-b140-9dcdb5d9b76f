package com.iot.platform.edge.service.impl;

import com.iot.platform.edge.config.EdgeProperties;
import com.iot.platform.edge.model.DeviceData;
import com.iot.platform.edge.service.DataUploadService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据上传服务实现
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataUploadServiceImpl implements DataUploadService {

    private final EdgeProperties edgeProperties;
    private final RestTemplate restTemplate;

    // 统计信息
    private final AtomicLong totalUploaded = new AtomicLong(0);
    private final AtomicLong totalFailed = new AtomicLong(0);
    private final AtomicLong totalRetries = new AtomicLong(0);
    private volatile long lastUploadTime = 0;

    @Override
    public boolean uploadDeviceData(DeviceData deviceData) {
        try {
            String url = buildUploadUrl("/api/device-data/report");

            HttpHeaders headers = createHeaders();
            HttpEntity<DeviceData> request = new HttpEntity<>(deviceData, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                totalUploaded.incrementAndGet();
                lastUploadTime = System.currentTimeMillis();
                log.debug("设备数据上传成功: device={}, dataCode={}",
                        deviceData.getDeviceCode(), deviceData.getDataCode());
                return true;
            } else {
                totalFailed.incrementAndGet();
                log.warn("设备数据上传失败: device={}, status={}",
                        deviceData.getDeviceCode(), response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            totalFailed.incrementAndGet();
            log.error("设备数据上传异常: device={}", deviceData.getDeviceCode(), e);
            return false;
        }
    }

    @Override
    public boolean uploadDeviceDataBatch(List<DeviceData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            return true;
        }

        try {
            String url = buildUploadUrl("/api/device-data/batch-report");

            Map<String, Object> batchRequest = new HashMap<>();
            batchRequest.put("dataList", dataList);
            batchRequest.put("batchId", "batch_" + System.currentTimeMillis());
            batchRequest.put("batchTime", System.currentTimeMillis());

            HttpHeaders headers = createHeaders();
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(batchRequest, headers);

            ResponseEntity<Map> response = restTemplate.postForEntity(url, request, Map.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                totalUploaded.addAndGet(dataList.size());
                lastUploadTime = System.currentTimeMillis();
                log.info("批量设备数据上传成功: count={}", dataList.size());
                return true;
            } else {
                totalFailed.addAndGet(dataList.size());
                log.warn("批量设备数据上传失败: count={}, status={}",
                        dataList.size(), response.getStatusCode());
                return false;
            }

        } catch (Exception e) {
            totalFailed.addAndGet(dataList.size());
            log.error("批量设备数据上传异常: count={}", dataList.size(), e);
            return false;
        }
    }

    @Override
    @Async
    public void uploadDeviceDataAsync(DeviceData deviceData) {
        uploadDeviceData(deviceData);
    }

    @Override
    @Async
    public void uploadDeviceDataBatchAsync(List<DeviceData> dataList) {
        uploadDeviceDataBatch(dataList);
    }

    @Override
    public boolean isServiceAvailable() {
        try {
            String url = buildUploadUrl("/actuator/health");
            ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
            return response.getStatusCode() == HttpStatus.OK;
        } catch (Exception e) {
            log.debug("检查上传服务可用性失败", e);
            return false;
        }
    }

    @Override
    public UploadStats getUploadStats() {
        UploadStats stats = new UploadStats();
        stats.setTotalUploaded(totalUploaded.get());
        stats.setTotalFailed(totalFailed.get());
        stats.setTotalRetries(totalRetries.get());
        stats.setLastUploadTime(lastUploadTime);

        long total = stats.getTotalUploaded() + stats.getTotalFailed();
        if (total > 0) {
            stats.setSuccessRate((double) stats.getTotalUploaded() / total * 100);
        }

        return stats;
    }

    /**
     * 构建上传URL
     */
    private String buildUploadUrl(String path) {
        EdgeProperties.Server server = edgeProperties.getServer();
        String protocol = server.getSsl() ? "https" : "http";
        return String.format("%s://%s:%d%s", protocol, server.getHost(), server.getPort(), path);
    }

    /**
     * 创建HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 添加边缘程序ID
        String edgeId = edgeProperties.getEdgeId();
        if (edgeId != null && !edgeId.trim().isEmpty()) {
            headers.set("X-Edge-Program-Id", edgeId);
        }

        // 添加认证信息
        EdgeProperties.Auth auth = edgeProperties.getAuth();
        if (auth != null && auth.getAccessKey() != null && auth.getSecretKey() != null) {
            // 这里应该实现AKSK签名逻辑
            // 为简化，暂时直接设置认证头
            headers.set("X-Access-Key", auth.getAccessKey());
            // 实际项目中需要实现完整的签名算法
        }

        return headers;
    }
}
