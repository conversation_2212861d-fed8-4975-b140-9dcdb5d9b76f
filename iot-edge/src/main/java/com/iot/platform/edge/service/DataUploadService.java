package com.iot.platform.edge.service;

import com.iot.platform.edge.model.DeviceData;

import java.util.List;

/**
 * 数据上传服务接口
 * 负责将设备数据上传到IoT平台
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface DataUploadService {

    /**
     * 上传单条设备数据
     *
     * @param deviceData 设备数据
     * @return 是否上传成功
     */
    boolean uploadDeviceData(DeviceData deviceData);

    /**
     * 批量上传设备数据
     *
     * @param dataList 设备数据列表
     * @return 是否上传成功
     */
    boolean uploadDeviceDataBatch(List<DeviceData> dataList);

    /**
     * 异步上传设备数据
     *
     * @param deviceData 设备数据
     */
    void uploadDeviceDataAsync(DeviceData deviceData);

    /**
     * 异步批量上传设备数据
     *
     * @param dataList 设备数据列表
     */
    void uploadDeviceDataBatchAsync(List<DeviceData> dataList);

    /**
     * 检查上传服务是否可用
     *
     * @return 是否可用
     */
    boolean isServiceAvailable();

    /**
     * 获取上传统计信息
     *
     * @return 统计信息
     */
    UploadStats getUploadStats();

    /**
     * 上传统计信息
     */
    class UploadStats {
        private long totalUploaded;
        private long totalFailed;
        private long totalRetries;
        private double successRate;
        private long lastUploadTime;

        // Getters and Setters
        public long getTotalUploaded() { return totalUploaded; }
        public void setTotalUploaded(long totalUploaded) { this.totalUploaded = totalUploaded; }

        public long getTotalFailed() { return totalFailed; }
        public void setTotalFailed(long totalFailed) { this.totalFailed = totalFailed; }

        public long getTotalRetries() { return totalRetries; }
        public void setTotalRetries(long totalRetries) { this.totalRetries = totalRetries; }

        public double getSuccessRate() { return successRate; }
        public void setSuccessRate(double successRate) { this.successRate = successRate; }

        public long getLastUploadTime() { return lastUploadTime; }
        public void setLastUploadTime(long lastUploadTime) { this.lastUploadTime = lastUploadTime; }
    }
}
