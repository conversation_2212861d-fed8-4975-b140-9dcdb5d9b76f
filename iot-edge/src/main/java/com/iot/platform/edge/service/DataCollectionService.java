package com.iot.platform.edge.service;

import com.iot.platform.edge.model.DeviceData;

import java.util.List;

/**
 * 数据采集服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface DataCollectionService {

    /**
     * 启动数据采集
     */
    void startCollection();

    /**
     * 停止数据采集
     */
    void stopCollection();

    /**
     * 检查采集状态
     *
     * @return 是否正在采集
     */
    boolean isCollecting();

    /**
     * 采集单个设备数据
     *
     * @param deviceCode 设备识别码
     * @return 设备数据
     */
    DeviceData collectDeviceData(String deviceCode);

    /**
     * 批量采集设备数据
     *
     * @param deviceCodes 设备识别码列表
     * @return 设备数据列表
     */
    List<DeviceData> collectBatchDeviceData(List<String> deviceCodes);

    /**
     * 获取缓存的数据
     *
     * @return 缓存数据列表
     */
    List<DeviceData> getCachedData();

    /**
     * 清空缓存
     */
    void clearCache();
}
