package com.iot.platform.edge.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iot.platform.common.entity.Device;
import com.iot.platform.edge.config.EdgeProperties;
import com.iot.platform.edge.model.DeviceData;
import com.iot.platform.edge.response.ApiResponse;
import com.iot.platform.edge.service.PlatformApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import jakarta.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * 平台API服务实现类
 * 负责与IoT平台进行数据交互
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PlatformApiServiceImpl implements PlatformApiService {

    private final EdgeProperties edgeProperties;
    private final RestTemplate restTemplate;

    /**
     * 异步执行器
     */
    private final Executor asyncExecutor = Executors.newFixedThreadPool(4);

    /**
     * 平台API基础URL
     */
    private String platformBaseUrl;

    @PostConstruct
    public void init() {
        this.platformBaseUrl = edgeProperties.getPlatformUrl();
        log.info("平台API服务初始化完成: platformUrl={}", platformBaseUrl);
    }

    @Override
    public Device getDeviceByCode(String deviceCode) {
        try {
            String url = platformBaseUrl + "/device/code/"+deviceCode;
            ResponseEntity<ApiResponse> response = restTemplate.getForEntity(url, ApiResponse.class);


//            ResponseEntity<Device> response = restTemplate.getForEntity(url, Device.class);
            ResponseEntity<String> responseStr = restTemplate.getForEntity(url, String.class);
            boolean success = response.getStatusCode() == HttpStatus.OK;

            if (success) {
                Object data = response.getBody().getData();
                Device device = JSONObject.parseObject(JSON.toJSONString(data), Device.class);
                return device;
            } else {
                log.warn("获取设备信息失败: deviceCode={}, status={}",deviceCode, response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("获取设备信息异常: deviceCode="+deviceCode, e);
            return null;
        }
        return null;
    }

    @Override
    public boolean uploadData(DeviceData data) {
        try {
            String url = platformBaseUrl + "/data/report";

            // 构建请求头
            HttpHeaders headers = createHeaders();

            // 构建请求体
            Map<String, Object> requestBody = convertToRequestBody(data);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.debug("上报单条数据到平台: device={}, url={}", data.getDeviceCode(), url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            boolean success = response.getStatusCode() == HttpStatus.OK;

            if (success) {
                log.debug("单条数据上报成功: device={}", data.getDeviceCode());
            } else {
                log.warn("单条数据上报失败: device={}, status={}",
                    data.getDeviceCode(), response.getStatusCode());
            }

            return success;

        } catch (Exception e) {
            log.error("上报单条数据异常: device={}", data.getDeviceCode(), e);
            return false;
        }
    }

    @Override
    public boolean uploadBatchData(List<DeviceData> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("批量上报数据为空");
            return false;
        }

        try {
            String url = platformBaseUrl + "/data/batch-report";

            // 构建请求头
            HttpHeaders headers = createHeaders();

            // 构建批量请求体
            Map<String, Object> requestBody = createBatchRequestBody(dataList);

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.debug("批量上报数据到平台: count={}, url={}", dataList.size(), url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            boolean success = response.getStatusCode() == HttpStatus.OK;

            if (success) {
                log.info("批量数据上报成功: count={}", dataList.size());
            } else {
                log.warn("批量数据上报失败: count={}, status={}",
                    dataList.size(), response.getStatusCode());
            }

            return success;

        } catch (Exception e) {
            log.error("批量上报数据异常: count={}", dataList.size(), e);
            return false;
        }
    }

    @Override
    public CompletableFuture<Boolean> uploadDataAsync(List<DeviceData> dataList) {
        return CompletableFuture.supplyAsync(() -> uploadBatchData(dataList), asyncExecutor);
    }

    @Override
    public boolean registerEdgeProgram() {
        try {
            String url = platformBaseUrl + "/edge/register";

            // 构建请求头
            HttpHeaders headers = createHeaders();

            // 构建注册请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("edgeId", edgeProperties.getEdgeId());
            requestBody.put("edgeName", edgeProperties.getEdgeName());
            requestBody.put("version", "1.0.0");
            requestBody.put("capabilities", List.of("data-collection", "device-control", "offline-cache"));
            requestBody.put("registerTime", System.currentTimeMillis());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.info("向平台注册边缘程序: edgeId={}, url={}", edgeProperties.getEdgeId(), url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            boolean success = response.getStatusCode() == HttpStatus.OK;

            if (success) {
                log.info("边缘程序注册成功: edgeId={}", edgeProperties.getEdgeId());
            } else {
                log.warn("边缘程序注册失败: edgeId={}, status={}",
                    edgeProperties.getEdgeId(), response.getStatusCode());
            }

            return success;

        } catch (Exception e) {
            log.error("注册边缘程序异常: edgeId={}", edgeProperties.getEdgeId(), e);
            return false;
        }
    }

    @Override
    public boolean sendHeartbeat() {
        try {
            String url = platformBaseUrl + "/edge/heartbeat";

            // 构建请求头
            HttpHeaders headers = createHeaders();

            // 构建心跳请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("edgeId", edgeProperties.getEdgeId());
            requestBody.put("timestamp", System.currentTimeMillis());
            requestBody.put("status", "RUNNING");
            requestBody.put("uptime", System.currentTimeMillis());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.debug("发送心跳到平台: edgeId={}, url={}", edgeProperties.getEdgeId(), url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            boolean success = response.getStatusCode() == HttpStatus.OK;

            if (success) {
                log.debug("心跳发送成功: edgeId={}", edgeProperties.getEdgeId());
            } else {
                log.warn("心跳发送失败: edgeId={}, status={}",
                    edgeProperties.getEdgeId(), response.getStatusCode());
            }

            return success;

        } catch (Exception e) {
            log.debug("发送心跳异常: edgeId={}, error={}", edgeProperties.getEdgeId(), e.getMessage());
            return false;
        }
    }

    @Override
    public String getConfiguration() {
        try {
            String url = platformBaseUrl + "/edge/config?edgeId=" + edgeProperties.getEdgeId();

            // 构建请求头
            HttpHeaders headers = createHeaders();
            HttpEntity<Void> request = new HttpEntity<>(headers);

            log.debug("从平台获取配置: edgeId={}, url={}", edgeProperties.getEdgeId(), url);

            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.GET, request, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                log.debug("获取配置成功: edgeId={}", edgeProperties.getEdgeId());
                return response.getBody();
            } else {
                log.warn("获取配置失败: edgeId={}, status={}",
                    edgeProperties.getEdgeId(), response.getStatusCode());
                return null;
            }

        } catch (Exception e) {
            log.error("获取配置异常: edgeId={}", edgeProperties.getEdgeId(), e);
            return null;
        }
    }

    @Override
    public boolean reportStatus(String status) {
        try {
            String url = platformBaseUrl + "/edge/status";

            // 构建请求头
            HttpHeaders headers = createHeaders();

            // 构建状态上报请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("edgeId", edgeProperties.getEdgeId());
            requestBody.put("status", status);
            requestBody.put("timestamp", System.currentTimeMillis());
            requestBody.put("reportTime", LocalDateTime.now().toString());

            HttpEntity<Map<String, Object>> request = new HttpEntity<>(requestBody, headers);

            log.debug("上报状态到平台: edgeId={}, status={}, url={}",
                edgeProperties.getEdgeId(), status, url);

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

            boolean success = response.getStatusCode() == HttpStatus.OK;

            if (success) {
                log.debug("状态上报成功: edgeId={}, status={}", edgeProperties.getEdgeId(), status);
            } else {
                log.warn("状态上报失败: edgeId={}, status={}, httpStatus={}",
                    edgeProperties.getEdgeId(), status, response.getStatusCode());
            }

            return success;

        } catch (Exception e) {
            log.error("上报状态异常: edgeId={}, status={}", edgeProperties.getEdgeId(), status, e);
            return false;
        }
    }

    /**
     * 创建HTTP请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("X-Edge-Program-Id", edgeProperties.getEdgeId());

        // 如果配置了认证信息，添加认证头
        if (edgeProperties.getAuth() != null) {
            String accessKey = edgeProperties.getAuth().getAccessKey();
            if (accessKey != null && !accessKey.isEmpty()) {
                headers.set("X-Access-Key", accessKey);
                // 这里可以添加签名逻辑
            }
        }

        return headers;
    }

    /**
     * 将DeviceData转换为请求体
     */
    private Map<String, Object> convertToRequestBody(DeviceData data) {
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("projectId", data.getProjectId());
        requestBody.put("deviceCode", data.getDeviceCode());
        requestBody.put("dataCode", data.getDataCode());
        requestBody.put("dataValue", data.getDataValue());
        requestBody.put("quality", data.getQuality());
        requestBody.put("reportTime", data.getReportTime());
        requestBody.put("edgeProgramId", edgeProperties.getEdgeId());
        requestBody.put("extData", data.getExtData());
        requestBody.put("timestamp", data.getTimestamp());
        requestBody.put("batchId", "batch_" + System.currentTimeMillis());
        requestBody.put("dataValueValid", true);
        requestBody.put("dataTypeDescription", "边缘设备数据");

        return requestBody;
    }

    /**
     * 创建批量请求体
     */
    private Map<String, Object> createBatchRequestBody(List<DeviceData> dataList) {
        Map<String, Object> requestBody = new HashMap<>();

        // 转换数据列表
        List<Map<String, Object>> dataListMap = dataList.stream()
            .map(this::convertToRequestBody)
            .toList();

        requestBody.put("dataList", dataListMap);
        requestBody.put("edgeProgramId", edgeProperties.getEdgeId());
        requestBody.put("batchId", "batch_" + System.currentTimeMillis());
        requestBody.put("batchTime", System.currentTimeMillis());
        requestBody.put("dataCount", dataList.size());

        // 统计设备数量和项目ID
        long deviceCount = dataList.stream().map(DeviceData::getDeviceCode).distinct().count();
        List<String> projectIds = dataList.stream().map(DeviceData::getProjectId).distinct().toList();
        List<String> deviceCodes = dataList.stream().map(DeviceData::getDeviceCode).distinct().toList();

        requestBody.put("deviceCount", deviceCount);
        requestBody.put("projectIds", projectIds);
        requestBody.put("deviceCodes", deviceCodes);

        return requestBody;
    }
}
