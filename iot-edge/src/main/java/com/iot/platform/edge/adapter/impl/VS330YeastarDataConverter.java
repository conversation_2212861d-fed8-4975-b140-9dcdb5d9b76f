package com.iot.platform.edge.adapter.impl;

import com.alibaba.fastjson.JSONObject;
import com.iot.platform.common.entity.Device;
import com.iot.platform.edge.adapter.YeastarDataConverter;
import com.iot.platform.edge.model.IotPointDataDTO;
import com.iot.platform.edge.model.yeastar.YeastarLinkData;
import com.iot.platform.edge.service.PlatformApiService;
import com.iot.platform.edge.utils.YeastarUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service("VS330")
public class VS330YeastarDataConverter implements YeastarDataConverter {

    @Override
    public List<IotPointDataDTO> apply(Device device,YeastarLinkData data) {
        List<IotPointDataDTO> res = new ArrayList<>();
        try {
            String bytes = YeastarUtils.decodeDataHex(data.getData());
            if (bytes == null) {
                bytes = "";
            }
            // 16进制2个字符占一个字节
            final int ONE_BYTE_HEX = 2;
            int i = 0;

            // 循环解析数据帧
            while (i < bytes.length() - 1) {
                IotPointDataDTO dto = IotPointDataDTO.builder()
                        .projectId(device.getProjectId())
                        .deviceCode(device.getDeviceCode())
                        .edgeProgramId(device.getEdgeProgramId())
                        .build();
                // 读取 channel_id (2个十六进制字符)
                String channelId;
                if (i + ONE_BYTE_HEX <= bytes.length()) {
                    channelId = bytes.substring(i, i + ONE_BYTE_HEX);
                } else {
                    break; // 长度不足，跳出循环
                }
                i += ONE_BYTE_HEX;

                // 读取 channel_type (2个十六进制字符)，并转换为大写
                String channelType;
                if (i + ONE_BYTE_HEX <= bytes.length()) {
                    channelType = bytes.substring(i, i + ONE_BYTE_HEX).toUpperCase();
                } else {
                    break; // 长度不足，跳出循环
                }
                i += ONE_BYTE_HEX;

                // 根据 channel_id 和 channel_type 解析数据
                // BATTERY
                if ("01".equalsIgnoreCase(channelId) && "75".equalsIgnoreCase(channelType)) {
                    int length = 1 * ONE_BYTE_HEX;
                    if (i + length <= bytes.length()) {
                        dto.setDataValue(hexStringToBigEndianLong(bytes.substring(i, i + length)));
                        i += length;
                    } else {
                        break; // 数据长度不足
                    }
                }
                // DISTANCE
                else if ("02".equalsIgnoreCase(channelId) && "82".equalsIgnoreCase(channelType)) {
                    int length = 2 * ONE_BYTE_HEX;
                    if (i + length <= bytes.length()) {
                        dto.setDataValue(hexStringToBigEndianLong(bytes.substring(i, i + length)));
                        i += length;
                    } else {
                        break; // 数据长度不足
                    }
                }
                // OCCUPANCY
                else if ("03".equalsIgnoreCase(channelId) && "8E".equalsIgnoreCase(channelType)) {
                    int length = 1 * ONE_BYTE_HEX;
                    if (i + length <= bytes.length()) {
                        dto.setDataValue(hexStringToBigEndianLong(bytes.substring(i, i + length)));
                        i += length;
                    } else {
                        break; // 数据长度不足
                    }
                }
                // CALIBRATION
                else if ("04".equalsIgnoreCase(channelId) && "8E".equalsIgnoreCase(channelType)) {
                    int length = 1 * ONE_BYTE_HEX;
                    if (i + length <= bytes.length()) {
                        dto.setDataValue(hexStringToBigEndianLong(bytes.substring(i, i + length)));
                        i += length;
                    } else {
                        break; // 数据长度不足
                    }
                } else {
                    // 遇到未知或不匹配的 channel，跳出循环
                    break;
                }
                res.add(dto);
            }
        } catch (Throwable e) {
            e.printStackTrace();
            log.error("数据解析失败: deviceCode={}, data={}", data.getDevEUI(), JSONObject.toJSONString(data));
        }
        return res;
    }

    private static BigDecimal hexStringToBigEndianLong(String hexStr) {
        // 如果字符串为空，返回 0
        if (hexStr == null || hexStr.isEmpty()) {
            return BigDecimal.ZERO;
        }
        try {
            // 1. 使用 BigInteger 从十六进制字符串创建 BigInteger 实例
            BigInteger bigInt = new BigInteger(hexStr, 16);
            // 2. 将 BigInteger 转换为 BigDecimal
            return new BigDecimal(bigInt);
        } catch (NumberFormatException e) {
            // 处理无效的十六进制字符串
            // 可以选择抛出异常，或者返回默认值（如 BigDecimal.ZERO），取决于你的需求
            // 这里选择抛出，让调用者处理
            throw new NumberFormatException("Invalid hexadecimal string: " + hexStr + ". " + e.getMessage());
        }
    }
}
