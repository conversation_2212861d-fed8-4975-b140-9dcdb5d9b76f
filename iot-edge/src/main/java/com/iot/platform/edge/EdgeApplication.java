package com.iot.platform.edge;

import com.iot.platform.edge.config.MqttConfigurationProperties;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.iot.platform.common.archive.DataArchiveService;
import com.iot.platform.common.sharding.impl.MonthlyShardingStrategy;
import com.iot.platform.common.sharding.ShardingService;

/**
 * IoT边缘程序主启动类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.iot.platform.edge",
        "com.iot.platform.common"
    },
    exclude = {
        DataSourceAutoConfiguration.class,
        HibernateJpaAutoConfiguration.class
    }
)
@ComponentScan(
    basePackages = {
        "com.iot.platform.edge",
        "com.iot.platform.common"
    },
    excludeFilters = {
        @ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = {
                DataArchiveService.class,
                MonthlyShardingStrategy.class,
                ShardingService.class
            }
        )
    }
)
@EnableConfigurationProperties(value = MqttConfigurationProperties.class)
@EnableAsync
@EnableScheduling
public class EdgeApplication {

    public static void main(String[] args) {
        SpringApplication.run(EdgeApplication.class, args);
        System.out.println("""

            ====================================
            🔧 IoT边缘程序启动成功！
            📡 MQTT连接状态: 等待连接...
            🔄 数据采集服务: 已启动
            ====================================
            """);
    }
}
