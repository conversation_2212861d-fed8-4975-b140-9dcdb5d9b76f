package com.iot.platform.edge.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.time.LocalDateTime;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 网络连接监控服务
 * 负责监控网络连接状态，处理断网重连和故障恢复
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Slf4j
@Service
public class NetworkMonitorService {

    @Value("${iot.edge.server.host:localhost}")
    private String serverHost;

    @Value("${iot.edge.server.port:8080}")
    private int serverPort;

    @Value("${iot.edge.network.check-interval:30}")
    private int checkInterval; // 秒

    @Value("${iot.edge.network.timeout:5000}")
    private int connectionTimeout; // 毫秒

    @Value("${iot.edge.network.max-retry:3}")
    private int maxRetryCount;

    @Value("${iot.edge.network.retry-delay:10}")
    private int retryDelay; // 秒

    @Autowired
    private OfflineCacheService offlineCacheService;

    @Autowired
    private DataUploadService dataUploadService;

    /**
     * 网络连接状态
     */
    private final AtomicBoolean isConnected = new AtomicBoolean(false);

    /**
     * 上次连接检查时间
     */
    private final AtomicLong lastCheckTime = new AtomicLong(0);

    /**
     * 连续失败次数
     */
    private final AtomicInteger consecutiveFailures = new AtomicInteger(0);

    /**
     * 定时任务执行器
     */
    private ScheduledExecutorService scheduler;

    /**
     * 网络状态监听器
     */
    private NetworkStatusListener statusListener;

    /**
     * 网络连接统计
     */
    private volatile long totalCheckCount = 0;
    private volatile long totalFailureCount = 0;
    private volatile LocalDateTime lastConnectedTime;
    private volatile LocalDateTime lastDisconnectedTime;

    @PostConstruct
    public void init() {
        try {
            // 初始化网络状态监听器
            statusListener = new NetworkStatusListener();

            // 启动定时任务
            scheduler = Executors.newScheduledThreadPool(2);

            // 定时检查网络连接
            scheduler.scheduleWithFixedDelay(this::checkNetworkConnection,
                    0, checkInterval, TimeUnit.SECONDS);

            // 定时尝试上传缓存数据
            scheduler.scheduleWithFixedDelay(this::tryUploadCachedData,
                    60, 60, TimeUnit.SECONDS);

            log.info("网络监控服务初始化完成: server={}:{}, checkInterval={}s",
                    serverHost, serverPort, checkInterval);

        } catch (Exception e) {
            log.error("网络监控服务初始化失败", e);
        }
    }

    @PreDestroy
    public void destroy() {
        try {
            if (scheduler != null) {
                scheduler.shutdown();
                if (!scheduler.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            }
            log.info("网络监控服务已关闭");
        } catch (Exception e) {
            log.error("网络监控服务关闭异常", e);
        }
    }

    /**
     * 检查网络连接状态
     */
    public boolean isNetworkConnected() {
        return isConnected.get();
    }

    /**
     * 获取网络状态统计信息
     */
    public NetworkStatus getNetworkStatus() {
        NetworkStatus status = new NetworkStatus();
        status.setConnected(isConnected.get());
        status.setServerHost(serverHost);
        status.setServerPort(serverPort);
        status.setLastCheckTime(lastCheckTime.get());
        status.setConsecutiveFailures(consecutiveFailures.get());
        status.setTotalCheckCount(totalCheckCount);
        status.setTotalFailureCount(totalFailureCount);
        status.setLastConnectedTime(lastConnectedTime);
        status.setLastDisconnectedTime(lastDisconnectedTime);
        return status;
    }

    /**
     * 手动触发网络连接检查
     */
    public boolean checkNetworkNow() {
        return checkNetworkConnection();
    }

    /**
     * 手动触发缓存数据上传
     */
    public void uploadCachedDataNow() {
        if (isNetworkConnected()) {
            tryUploadCachedData();
        } else {
            log.warn("网络未连接，无法上传缓存数据");
        }
    }

    /**
     * 检查网络连接
     */
    private boolean checkNetworkConnection() {
        totalCheckCount++;
        lastCheckTime.set(System.currentTimeMillis());

        try {
            boolean connected = testConnection(serverHost, serverPort, connectionTimeout);

            if (connected) {
                if (!isConnected.get()) {
                    // 网络恢复
                    onNetworkConnected();
                }
                consecutiveFailures.set(0);
                isConnected.set(true);
                lastConnectedTime = LocalDateTime.now();

                log.debug("网络连接正常: {}:{}", serverHost, serverPort);
                return true;

            } else {
                if (isConnected.get()) {
                    // 网络断开
                    onNetworkDisconnected();
                }

                int failures = consecutiveFailures.incrementAndGet();
                isConnected.set(false);
                lastDisconnectedTime = LocalDateTime.now();
                totalFailureCount++;

                log.warn("网络连接失败: {}:{}, 连续失败次数: {}", serverHost, serverPort, failures);

                // 如果连续失败次数超过阈值，尝试重连
                if (failures >= maxRetryCount) {
                    scheduleReconnect();
                }

                return false;
            }

        } catch (Exception e) {
            log.error("网络连接检查异常: {}:{}", serverHost, serverPort, e);

            if (isConnected.get()) {
                onNetworkDisconnected();
            }

            consecutiveFailures.incrementAndGet();
            isConnected.set(false);
            totalFailureCount++;

            return false;
        }
    }

    /**
     * 测试网络连接
     */
    private boolean testConnection(String host, int port, int timeout) {
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), timeout);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 网络连接恢复处理
     */
    private void onNetworkConnected() {
        log.info("网络连接已恢复: {}:{}", serverHost, serverPort);

        // 通知监听器
        if (statusListener != null) {
            statusListener.onNetworkConnected();
        }

        // 立即尝试上传缓存数据
        scheduler.execute(this::tryUploadCachedData);
    }

    /**
     * 网络断开处理
     */
    private void onNetworkDisconnected() {
        log.warn("网络连接已断开: {}:{}", serverHost, serverPort);

        // 通知监听器
        if (statusListener != null) {
            statusListener.onNetworkDisconnected();
        }
    }

    /**
     * 调度重连任务
     */
    private void scheduleReconnect() {
        log.info("调度网络重连任务: {}秒后重试", retryDelay);

        scheduler.schedule(() -> {
            log.info("开始网络重连尝试");
            consecutiveFailures.set(0); // 重置失败计数
            checkNetworkConnection();
        }, retryDelay, TimeUnit.SECONDS);
    }

    /**
     * 尝试上传缓存数据
     */
    private void tryUploadCachedData() {
        if (!isNetworkConnected()) {
            log.debug("网络未连接，跳过缓存数据上传");
            return;
        }

        if (!offlineCacheService.hasCachedData()) {
            log.debug("没有缓存数据需要上传");
            return;
        }

        try {
            log.info("开始上传缓存数据");

            // 分批上传缓存数据
            int batchSize = 100;
            int uploadedCount = 0;

            while (offlineCacheService.hasCachedData() && isNetworkConnected()) {
                var cachedData = offlineCacheService.getCachedData(batchSize);

                if (cachedData.isEmpty()) {
                    break;
                }

                boolean success = dataUploadService.uploadDeviceDataBatch(cachedData);

                if (success) {
                    offlineCacheService.removeCachedData(cachedData);
                    uploadedCount += cachedData.size();
                    log.debug("成功上传缓存数据批次: count={}", cachedData.size());
                } else {
                    offlineCacheService.markUploadFailed(cachedData);
                    log.warn("缓存数据上传失败，停止上传");
                    break;
                }

                // 避免过于频繁的上传
                Thread.sleep(1000);
            }

            if (uploadedCount > 0) {
                log.info("缓存数据上传完成: totalCount={}", uploadedCount);
            }

        } catch (Exception e) {
            log.error("上传缓存数据异常", e);
        }
    }

    /**
     * 网络状态监听器
     */
    private class NetworkStatusListener {

        public void onNetworkConnected() {
            log.info("网络状态监听器: 网络已连接");
        }

        public void onNetworkDisconnected() {
            log.info("网络状态监听器: 网络已断开");
        }
    }

    /**
     * 网络状态信息
     */
    public static class NetworkStatus {
        private boolean connected;
        private String serverHost;
        private int serverPort;
        private long lastCheckTime;
        private int consecutiveFailures;
        private long totalCheckCount;
        private long totalFailureCount;
        private LocalDateTime lastConnectedTime;
        private LocalDateTime lastDisconnectedTime;

        // Getters and Setters
        public boolean isConnected() { return connected; }
        public void setConnected(boolean connected) { this.connected = connected; }

        public String getServerHost() { return serverHost; }
        public void setServerHost(String serverHost) { this.serverHost = serverHost; }

        public int getServerPort() { return serverPort; }
        public void setServerPort(int serverPort) { this.serverPort = serverPort; }

        public long getLastCheckTime() { return lastCheckTime; }
        public void setLastCheckTime(long lastCheckTime) { this.lastCheckTime = lastCheckTime; }

        public int getConsecutiveFailures() { return consecutiveFailures; }
        public void setConsecutiveFailures(int consecutiveFailures) { this.consecutiveFailures = consecutiveFailures; }

        public long getTotalCheckCount() { return totalCheckCount; }
        public void setTotalCheckCount(long totalCheckCount) { this.totalCheckCount = totalCheckCount; }

        public long getTotalFailureCount() { return totalFailureCount; }
        public void setTotalFailureCount(long totalFailureCount) { this.totalFailureCount = totalFailureCount; }

        public LocalDateTime getLastConnectedTime() { return lastConnectedTime; }
        public void setLastConnectedTime(LocalDateTime lastConnectedTime) { this.lastConnectedTime = lastConnectedTime; }

        public LocalDateTime getLastDisconnectedTime() { return lastDisconnectedTime; }
        public void setLastDisconnectedTime(LocalDateTime lastDisconnectedTime) { this.lastDisconnectedTime = lastDisconnectedTime; }
    }
}
