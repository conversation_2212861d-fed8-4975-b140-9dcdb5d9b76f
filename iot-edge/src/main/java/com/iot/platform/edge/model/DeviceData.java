package com.iot.platform.edge.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 设备数据模型
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@Data
public class DeviceData {

    /**
     * 设备识别码
     */
    private String deviceCode;

    /**
     * 数据类型码
     */
    private Byte dataCode;

    /**
     * 数据值
     */
    private Object dataValue;

    /**
     * 数据采集时间戳
     */
    private Long timestamp;

    /**
     * 数据质量（0-100）
     */
    private Integer quality = 100;

    /**
     * 元数据
     */
    private Map<String, Object> metadata;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 项目ID（兼容性字段）
     */
    private String projectId;

    /**
     * 扩展数据（兼容性字段）
     */
    private String extData;

    /**
     * 构造函数
     */
    public DeviceData() {
        this.timestamp = System.currentTimeMillis();
        this.createdAt = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public DeviceData(String deviceCode, Byte dataCode, Object dataValue) {
        this();
        this.deviceCode = deviceCode;
        this.dataCode = dataCode;
        this.dataValue = dataValue;
    }

    /**
     * 构造函数
     */
    public DeviceData(String deviceCode, Byte dataCode, Object dataValue, Integer quality) {
        this(deviceCode, dataCode, dataValue);
        this.quality = quality;
    }

    /**
     * 设置上报时间（兼容性方法）
     */
    public void setReportTime(long reportTime) {
        this.timestamp = reportTime;
    }

    /**
     * 获取上报时间（兼容性方法）
     */
    public Long getReportTime() {
        return this.timestamp;
    }
}
