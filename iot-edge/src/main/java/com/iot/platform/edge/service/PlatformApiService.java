package com.iot.platform.edge.service;

import com.iot.platform.common.entity.Device;
import com.iot.platform.edge.model.DeviceData;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 平台API服务接口
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
public interface PlatformApiService {

    /**
     * 上报单条数据
     *
     * @param data 设备数据
     * @return 上报是否成功
     */
    boolean uploadData(DeviceData data);

    /**
     * 根据设备编码获取设备信息
     *
     * @param deviceCode 设备编码
     * @return 设备信息
     */
    Device getDeviceByCode(String deviceCode);

    /**
     * 批量上报数据
     *
     * @param dataList 设备数据列表
     * @return 上报是否成功
     */
    boolean uploadBatchData(List<DeviceData> dataList);

    /**
     * 异步上报数据
     *
     * @param dataList 设备数据列表
     * @return 异步结果
     */
    CompletableFuture<Boolean> uploadDataAsync(List<DeviceData> dataList);

    /**
     * 向平台注册边缘程序
     *
     * @return 注册是否成功
     */
    boolean registerEdgeProgram();

    /**
     * 向平台发送心跳
     *
     * @return 心跳是否成功
     */
    boolean sendHeartbeat();

    /**
     * 从平台获取配置
     *
     * @return 配置信息
     */
    String getConfiguration();

    /**
     * 向平台上报状态
     *
     * @param status 状态信息
     * @return 上报是否成功
     */
    boolean reportStatus(String status);

    /**
     * 批量上报数据（兼容性方法）
     *
     * @param dataList 设备数据列表
     * @return 上报是否成功
     */
    default boolean uploadDataBatch(List<DeviceData> dataList) {
        return uploadBatchData(dataList);
    }
}
