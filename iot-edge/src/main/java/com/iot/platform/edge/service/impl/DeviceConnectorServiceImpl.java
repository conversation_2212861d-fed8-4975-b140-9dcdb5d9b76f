package com.iot.platform.edge.service.impl;

import com.iot.platform.edge.config.EdgeProperties;
import com.iot.platform.edge.model.DeviceData;
import com.iot.platform.edge.service.DeviceConnectorService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 设备连接器服务实现类
 * 负责管理设备连接、数据读取和指令发送
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeviceConnectorServiceImpl implements DeviceConnectorService {

    private final EdgeProperties edgeProperties;

    /**
     * 设备连接状态缓存
     * key: deviceCode, value: 连接状态
     */
    private final Map<String, Boolean> deviceConnectionStatus = new ConcurrentHashMap<>();

    /**
     * 模拟设备列表（实际项目中应该从配置文件或数据库读取）
     */
    private final List<String> simulatedDevices = List.of(
        "DEVICE_001", "DEVICE_002", "DEVICE_003",
        "DEVICE_004", "DEVICE_005"
    );

    @PostConstruct
    public void init() {
        log.info("设备连接器服务初始化完成");

        // 初始化时自动连接模拟设备
        initializeSimulatedDevices();
    }

    @PreDestroy
    public void destroy() {
        // 断开所有设备连接
        disconnectAllDevices();
        log.info("设备连接器服务已关闭");
    }

    @Override
    public boolean connectDevice(String deviceCode) {
        try {
            log.debug("尝试连接设备: {}", deviceCode);

            // 模拟设备连接过程
            Thread.sleep(100); // 模拟连接延迟

            // 模拟连接成功率（90%成功率）
            boolean connected = ThreadLocalRandom.current().nextDouble() < 0.9;

            deviceConnectionStatus.put(deviceCode, connected);

            if (connected) {
                log.info("设备连接成功: {}", deviceCode);
            } else {
                log.warn("设备连接失败: {}", deviceCode);
            }

            return connected;

        } catch (Exception e) {
            log.error("连接设备异常: {}", deviceCode, e);
            deviceConnectionStatus.put(deviceCode, false);
            return false;
        }
    }

    @Override
    public void disconnectDevice(String deviceCode) {
        try {
            log.debug("断开设备连接: {}", deviceCode);

            deviceConnectionStatus.put(deviceCode, false);

            log.info("设备连接已断开: {}", deviceCode);

        } catch (Exception e) {
            log.error("断开设备连接异常: {}", deviceCode, e);
        }
    }

    @Override
    public boolean isDeviceConnected(String deviceCode) {
        return deviceConnectionStatus.getOrDefault(deviceCode, false);
    }

    @Override
    public List<String> getConnectedDevices() {
        List<String> connectedDevices = new ArrayList<>();

        for (Map.Entry<String, Boolean> entry : deviceConnectionStatus.entrySet()) {
            if (entry.getValue()) {
                connectedDevices.add(entry.getKey());
            }
        }

        log.debug("当前已连接设备数量: {}", connectedDevices.size());
        return connectedDevices;
    }

    @Override
    public DeviceData readDeviceData(String deviceCode) {
        if (!isDeviceConnected(deviceCode)) {
            log.warn("设备未连接，无法读取数据: {}", deviceCode);
            return null;
        }

        try {
            // 模拟数据读取过程
            Thread.sleep(50); // 模拟读取延迟

            // 生成模拟数据
            DeviceData deviceData = generateSimulatedData(deviceCode);

            log.debug("成功读取设备数据: device={}, dataCode={}, value={}",
                deviceCode, deviceData.getDataCode(), deviceData.getDataValue());

            return deviceData;

        } catch (Exception e) {
            log.error("读取设备数据异常: {}", deviceCode, e);
            return null;
        }
    }

    @Override
    public boolean sendCommand(String deviceCode, String command) {
        if (!isDeviceConnected(deviceCode)) {
            log.warn("设备未连接，无法发送指令: {}", deviceCode);
            return false;
        }

        try {
            log.info("向设备发送指令: device={}, command={}", deviceCode, command);

            // 模拟指令发送过程
            Thread.sleep(100); // 模拟发送延迟

            // 模拟指令执行成功率（95%成功率）
            boolean success = ThreadLocalRandom.current().nextDouble() < 0.95;

            if (success) {
                log.info("指令发送成功: device={}, command={}", deviceCode, command);
            } else {
                log.warn("指令发送失败: device={}, command={}", deviceCode, command);
            }

            return success;

        } catch (Exception e) {
            log.error("发送设备指令异常: device={}, command={}", deviceCode, command, e);
            return false;
        }
    }

    @Override
    public String getDeviceStatus(String deviceCode) {
        boolean connected = isDeviceConnected(deviceCode);

        if (connected) {
            // 模拟设备状态信息
            return String.format("ONLINE|温度:%.1f°C|湿度:%.1f%%|电量:%.0f%%",
                ThreadLocalRandom.current().nextDouble(20, 30),
                ThreadLocalRandom.current().nextDouble(40, 60),
                ThreadLocalRandom.current().nextDouble(80, 100));
        } else {
            return "OFFLINE";
        }
    }

    @Override
    public void reconnectAllDevices() {
        log.info("开始重连所有设备");

        List<String> allDevices = new ArrayList<>(deviceConnectionStatus.keySet());
        allDevices.addAll(simulatedDevices);

        int successCount = 0;
        for (String deviceCode : allDevices) {
            if (connectDevice(deviceCode)) {
                successCount++;
            }
        }

        log.info("设备重连完成: 成功={}, 总数={}", successCount, allDevices.size());
    }

    /**
     * 初始化模拟设备
     */
    private void initializeSimulatedDevices() {
        log.info("初始化模拟设备连接");

        for (String deviceCode : simulatedDevices) {
            connectDevice(deviceCode);
        }

        log.info("模拟设备初始化完成，已连接设备数量: {}", getConnectedDevices().size());
    }

    /**
     * 断开所有设备连接
     */
    private void disconnectAllDevices() {
        for (String deviceCode : deviceConnectionStatus.keySet()) {
            disconnectDevice(deviceCode);
        }
    }

    /**
     * 生成模拟设备数据
     */
    private DeviceData generateSimulatedData(String deviceCode) {
        DeviceData data = new DeviceData();

        data.setProjectId("project_001");
        data.setDeviceCode(deviceCode);
        data.setDataCode((byte) 1); // 温度数据类型码
        data.setDataValue(ThreadLocalRandom.current().nextDouble(20.0, 30.0)); // 20-30度
        data.setQuality(100); // 数据质量
        data.setReportTime(System.currentTimeMillis());
        data.setExtData("{\"sensorType\":\"temperature\",\"unit\":\"celsius\"}");
        data.setTimestamp(System.currentTimeMillis());

        return data;
    }
}
