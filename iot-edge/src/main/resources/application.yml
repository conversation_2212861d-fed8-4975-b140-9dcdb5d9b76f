spring:
  profiles:
    active: test
  application:
    name: iot-platform-edge

# 服务器配置
server:
  port: 8001
  servlet:
    context-path: /edge

# 日志配置
logging:
  level:
    com.iot.platform: debug
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%X{traceId}] %logger{50} - %msg%n"

# 自定义配置
iot:
  edge:
    edge-id: edge-001
    edge-name: 默认边缘程序
    platform-url: http://localhost:8000/api
    auth:
      access-key: ${EDGE_ACCESS_KEY:}
      secret-key: ${EDGE_SECRET_KEY:}
    data-collection:
      interval: 60              # 数据采集间隔（秒）
      batch-size: 100           # 批量上报大小
      max-cache-size: 1000      # 数据缓存最大大小
    mqtt:
      broker-url: tcp://localhost:1883
      client-id: iot-edge-001
      username: ${MQTT_USERNAME:}
      password: ${MQTT_PASSWORD:}
      keep-alive: 60
      connection-timeout: 30
    device:
      connection-timeout: 10    # 设备连接超时时间（秒）
      read-timeout: 5           # 设备读取超时时间（秒）
      max-retry-attempts: 3     # 设备重连最大尝试次数
