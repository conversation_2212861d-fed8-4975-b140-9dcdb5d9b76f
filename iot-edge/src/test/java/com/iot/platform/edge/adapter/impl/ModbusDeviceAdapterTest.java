package com.iot.platform.edge.adapter.impl;

import com.iot.platform.edge.model.DeviceCommand;
import com.iot.platform.edge.model.DeviceData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Modbus设备适配器测试类
 *
 * <AUTHOR> Platform Team
 * @since 2025-07-22
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Modbus设备适配器测试")
class ModbusDeviceAdapterTest {

    private ModbusDeviceAdapter adapter;
    private static final String TEST_DEVICE_CODE = "MODBUS_DEVICE_001";
    private static final String TEST_HOST = "*************";
    private static final int TEST_PORT = 502;

    @BeforeEach
    void setUp() {
        adapter = new ModbusDeviceAdapter();
    }

    @Test
    @DisplayName("获取适配器基本信息")
    void testGetAdapterInfo() {
        assertEquals("MODBUS", adapter.getAdapterType());

        List<String> supportedVendors = adapter.getSupportedVendors();
        assertNotNull(supportedVendors);
        assertTrue(supportedVendors.contains("SCHNEIDER"));
        assertTrue(supportedVendors.contains("SIEMENS"));
        assertTrue(supportedVendors.contains("ABB"));
        assertTrue(supportedVendors.contains("GENERIC"));
    }

    @Test
    @DisplayName("适配器初始化 - 成功")
    void testInitialize_Success() {
        Map<String, Object> config = createValidConfig();

        boolean result = adapter.initialize(config);

        assertTrue(result);
    }

    @Test
    @DisplayName("适配器初始化 - 配置为空")
    void testInitialize_EmptyConfig() {
        boolean result = adapter.initialize(Collections.emptyMap());

        // 即使配置为空，也应该能初始化成功（使用默认配置）
        assertTrue(result);
    }

    @Test
    @DisplayName("适配器初始化 - 配置为null")
    void testInitialize_NullConfig() {
        boolean result = adapter.initialize(null);

        assertFalse(result);
    }

    @Test
    @DisplayName("设备连接 - 成功")
    void testConnect_Success() {
        // 先初始化适配器
        adapter.initialize(createValidConfig());

        Map<String, Object> connectionParams = createConnectionParams();

        boolean result = adapter.connect(TEST_DEVICE_CODE, connectionParams);

        assertTrue(result);
        assertTrue(adapter.isConnected(TEST_DEVICE_CODE));
    }

    @Test
    @DisplayName("设备连接 - 适配器未初始化")
    void testConnect_NotInitialized() {
        Map<String, Object> connectionParams = createConnectionParams();

        boolean result = adapter.connect(TEST_DEVICE_CODE, connectionParams);

        assertFalse(result);
        assertFalse(adapter.isConnected(TEST_DEVICE_CODE));
    }

    @Test
    @DisplayName("设备连接 - 缺少主机地址")
    void testConnect_MissingHost() {
        adapter.initialize(createValidConfig());

        Map<String, Object> connectionParams = new HashMap<>();
        connectionParams.put("port", TEST_PORT);
        connectionParams.put("slaveId", 1);

        boolean result = adapter.connect(TEST_DEVICE_CODE, connectionParams);

        assertFalse(result);
        assertFalse(adapter.isConnected(TEST_DEVICE_CODE));
    }

    @Test
    @DisplayName("设备断开连接 - 成功")
    void testDisconnect_Success() {
        // 先连接设备
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());
        assertTrue(adapter.isConnected(TEST_DEVICE_CODE));

        // 断开连接
        boolean result = adapter.disconnect(TEST_DEVICE_CODE);

        assertTrue(result);
        assertFalse(adapter.isConnected(TEST_DEVICE_CODE));
    }

    @Test
    @DisplayName("读取设备数据 - 成功")
    void testReadDeviceData_Success() {
        // 连接设备
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());

        List<String> dataPoints = Arrays.asList("temperature", "humidity", "pressure");

        List<DeviceData> dataList = adapter.readDeviceData(TEST_DEVICE_CODE, dataPoints);

        assertNotNull(dataList);
        assertEquals(3, dataList.size());

        for (DeviceData data : dataList) {
            assertEquals(TEST_DEVICE_CODE, data.getDeviceCode());
            assertNotNull(data.getDataValue());
            assertNotNull(data.getTimestamp());
            assertEquals((byte) 1, data.getQuality());
        }
    }

    @Test
    @DisplayName("读取设备数据 - 设备未连接")
    void testReadDeviceData_NotConnected() {
        adapter.initialize(createValidConfig());

        List<String> dataPoints = Arrays.asList("temperature", "humidity");

        List<DeviceData> dataList = adapter.readDeviceData(TEST_DEVICE_CODE, dataPoints);

        assertNotNull(dataList);
        assertTrue(dataList.isEmpty());
    }

    @Test
    @DisplayName("发送设备指令 - 成功")
    void testSendCommand_Success() {
        // 连接设备
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());

        DeviceCommand command = createTestCommand();

        boolean result = adapter.sendCommand(TEST_DEVICE_CODE, command);

        assertTrue(result);
        assertTrue(command.isSuccess());
        assertNotNull(command.getResult());
    }

    @Test
    @DisplayName("发送设备指令 - 设备未连接")
    void testSendCommand_NotConnected() {
        adapter.initialize(createValidConfig());

        DeviceCommand command = createTestCommand();

        boolean result = adapter.sendCommand(TEST_DEVICE_CODE, command);

        assertFalse(result);
        assertTrue(command.isFailed());
        assertEquals("设备未连接", command.getErrorMessage());
    }

    @Test
    @DisplayName("获取设备状态 - 已连接")
    void testGetDeviceStatus_Connected() {
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());

        Map<String, Object> status = adapter.getDeviceStatus(TEST_DEVICE_CODE);

        assertNotNull(status);
        assertEquals(TEST_DEVICE_CODE, status.get("deviceCode"));
        assertEquals(true, status.get("connected"));
        assertEquals("MODBUS", status.get("adapterType"));
        assertEquals(TEST_HOST, status.get("host"));
        assertEquals(TEST_PORT, status.get("port"));
    }

    @Test
    @DisplayName("获取设备状态 - 未连接")
    void testGetDeviceStatus_NotConnected() {
        adapter.initialize(createValidConfig());

        Map<String, Object> status = adapter.getDeviceStatus(TEST_DEVICE_CODE);

        assertNotNull(status);
        assertEquals(TEST_DEVICE_CODE, status.get("deviceCode"));
        assertEquals(false, status.get("connected"));
        assertEquals("MODBUS", status.get("adapterType"));
    }

    @Test
    @DisplayName("获取设备配置")
    void testGetDeviceConfig() {
        adapter.initialize(createValidConfig());
        Map<String, Object> connectionParams = createConnectionParams();
        adapter.connect(TEST_DEVICE_CODE, connectionParams);

        Map<String, Object> config = adapter.getDeviceConfig(TEST_DEVICE_CODE);

        assertNotNull(config);
        assertEquals(TEST_HOST, config.get("host"));
        assertEquals(TEST_PORT, config.get("port"));
    }

    @Test
    @DisplayName("更新设备配置")
    void testUpdateDeviceConfig() {
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());

        Map<String, Object> newConfig = new HashMap<>();
        newConfig.put("host", "*************");
        newConfig.put("port", 503);
        newConfig.put("slaveId", 2);

        boolean result = adapter.updateDeviceConfig(TEST_DEVICE_CODE, newConfig);

        assertTrue(result);

        Map<String, Object> updatedConfig = adapter.getDeviceConfig(TEST_DEVICE_CODE);
        assertEquals("*************", updatedConfig.get("host"));
        assertEquals(503, updatedConfig.get("port"));
    }

    @Test
    @DisplayName("获取诊断信息")
    void testGetDiagnosticInfo() {
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());

        Map<String, Object> diagnostic = adapter.getDiagnosticInfo(TEST_DEVICE_CODE);

        assertNotNull(diagnostic);
        assertEquals(TEST_DEVICE_CODE, diagnostic.get("deviceCode"));
        assertEquals("MODBUS", diagnostic.get("adapterType"));
        assertEquals(true, diagnostic.get("connected"));
        assertEquals("good", diagnostic.get("connectionQuality"));
        assertNotNull(diagnostic.get("responseTime"));
    }

    @Test
    @DisplayName("重置设备")
    void testResetDevice() {
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());

        boolean result = adapter.resetDevice(TEST_DEVICE_CODE);

        assertTrue(result);
    }

    @Test
    @DisplayName("获取健康状态 - 已初始化")
    void testGetHealthStatus_Initialized() {
        adapter.initialize(createValidConfig());

        Map<String, Object> health = adapter.getHealthStatus();

        assertNotNull(health);
        assertEquals("MODBUS", health.get("adapterType"));
        assertEquals(true, health.get("initialized"));
        assertEquals("healthy", health.get("status"));
        assertEquals("适配器运行正常", health.get("message"));
    }

    @Test
    @DisplayName("获取健康状态 - 未初始化")
    void testGetHealthStatus_NotInitialized() {
        Map<String, Object> health = adapter.getHealthStatus();

        assertNotNull(health);
        assertEquals("MODBUS", health.get("adapterType"));
        assertEquals(false, health.get("initialized"));
        assertEquals("unhealthy", health.get("status"));
        assertEquals("适配器未初始化", health.get("message"));
    }

    @Test
    @DisplayName("销毁适配器")
    void testDestroy() {
        adapter.initialize(createValidConfig());
        adapter.connect(TEST_DEVICE_CODE, createConnectionParams());
        assertTrue(adapter.isConnected(TEST_DEVICE_CODE));

        adapter.destroy();

        assertFalse(adapter.isConnected(TEST_DEVICE_CODE));

        Map<String, Object> health = adapter.getHealthStatus();
        assertEquals(false, health.get("initialized"));
    }

    /**
     * 创建有效的配置
     */
    private Map<String, Object> createValidConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("connectionPoolSize", 10);
        config.put("connectionTimeout", 5000);
        config.put("readTimeout", 3000);
        return config;
    }

    /**
     * 创建连接参数
     */
    private Map<String, Object> createConnectionParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("host", TEST_HOST);
        params.put("port", TEST_PORT);
        params.put("slaveId", 1);
        params.put("timeout", 5000);
        return params;
    }

    /**
     * 创建测试指令
     */
    private DeviceCommand createTestCommand() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("address", 1);
        parameters.put("value", 100);

        return DeviceCommand.create(TEST_DEVICE_CODE, "WRITE_REGISTER", parameters);
    }
}
