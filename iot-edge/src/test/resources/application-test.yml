# IoT边缘程序模块 - 测试环境配置

spring:
  profiles:
    active: test

# 日志配置
logging:
  level:
    com.iot.platform.edge: DEBUG
    org.springframework: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# IoT边缘程序配置
iot:
  edge:
    # 边缘程序基本信息
    edge-id: "test_edge_001"
    edge-name: "测试边缘程序"
    location: "测试环境"
    version: "1.0.0-TEST"
    
    # 平台连接配置
    platform:
      api-url: "http://localhost:8080/api"
      access-key: "test_access_key"
      secret-key: "test_secret_key"
      connection-timeout: 10000
      read-timeout: 30000
      retry-times: 3
      retry-interval: 5000
    
    # MQTT配置
    mqtt:
      broker-url: "tcp://localhost:1883"
      client-id: "test_edge_001"
      username: "test_user"
      password: "test_pass"
      connection-timeout: 10
      keep-alive-interval: 30
      qos: 1
      clean-session: true
      automatic-reconnect: true
      max-reconnect-delay: 60
    
    # 数据采集配置
    data-collection:
      enabled: true
      interval: 30  # 秒
      batch-size: 10
      max-cache-size: 1000
      cache-expire-minutes: 60
      retry-times: 3
      timeout: 10000
    
    # 设备连接配置
    device-connection:
      enabled: true
      connection-pool-size: 5
      connection-timeout: 5000
      read-timeout: 3000
      max-idle-time: 300000  # 5分钟
      health-check-interval: 60  # 秒
    
    # 缓存配置
    cache:
      enabled: true
      type: "memory"  # memory, redis
      max-size: 1000
      expire-minutes: 30
      cleanup-interval: 300  # 秒
    
    # 监控配置
    monitor:
      enabled: true
      collect-interval: 30  # 秒
      report-interval: 300  # 秒
      metrics-retention-hours: 24
      alert-enabled: false  # 测试环境关闭告警
    
    # 安全配置
    security:
      enabled: false  # 测试环境关闭安全验证
      encryption-enabled: false
      certificate-path: ""
      private-key-path: ""

# 设备适配器配置
device:
  adapters:
    modbus:
      enabled: true
      connection-pool-size: 5
      connection-timeout: 5000
      read-timeout: 3000
      max-retries: 3
      retry-delay: 1000
    
    opcua:
      enabled: false  # 测试环境暂时禁用
    
    mqtt-device:
      enabled: false  # 测试环境暂时禁用

# 测试专用配置
test:
  # 模拟设备配置
  mock-devices:
    enabled: true
    device-count: 3
    data-interval: 10  # 秒
    data-variation: 0.1  # 数据变化幅度
  
  # 网络模拟配置
  network:
    simulate-delay: false
    delay-range: [100, 500]  # 毫秒
    simulate-failure: false
    failure-rate: 0.05  # 5%失败率
  
  # 性能测试配置
  performance:
    concurrent-devices: 10
    messages-per-second: 100
    test-duration: 60  # 秒
